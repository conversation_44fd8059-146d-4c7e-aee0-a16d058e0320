<!DOCTYPE html>
<html lang="en">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<script>var QUnit = parent.QUnit</script>
	<script src="testinit.js"></script>
	<script src="../../dist/sizzle.js"></script>
</head>
<body>
	<script>
		var doc = parent.document,
			unframed = [ doc.getElementById( "qunit-fixture" ), doc.body, doc.documentElement ],
			framed = Sizzle( "*" );

		window.parent.iframeCallback(
			Sizzle.uniqueSort( unframed.concat( framed ) ),
			framed.concat( unframed.reverse() ),
			"Mixed array was sorted correctly"
		);
	</script>
</body>
</html>
