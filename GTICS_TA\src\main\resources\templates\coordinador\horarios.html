<!DOCTYPE html>
<html lang="es" xmlns:th="http://www.thymeleaf.org">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="">
  <meta name="author" content="">
  <link rel="icon" th:href="@{/images/favicon.ico}">

  <title>Fibra - Horarios del Coordinador</title>

  <!-- Vendors Style-->
  <link rel="stylesheet" th:href="@{/front-end/css/vendors_css.css}">

  <!-- Style-->
  <link rel="stylesheet" th:href="@{/front-end/css/style.css}">
  <link rel="stylesheet" th:href="@{/front-end/css/skin_color.css}">

</head>

<body class="hold-transition light-skin sidebar-mini theme-success fixed">

<div class="wrapper">
  <div id="loader"></div>

  <header class="main-header">
    <div class="d-flex align-items-center logo-box justify-content-center">
      <!-- Logo -->
      <a th:href="@{/coordinador/principal}" class="logo">
        <!-- logo-->
        <div class="logo-mini w-150 text-center">
          <span class="light-logo"><img th:src="@{/images/logo-sanMiguel.png}" alt="logo"></span>
        </div>
      </a>
    </div>
    <!-- Header Navbar -->
    <nav class="navbar navbar-static-top">
      <!-- Sidebar toggle button-->
      <div class="app-menu">
        <ul class="header-megamenu nav">
          <li class="btn-group nav-item">
            <a href="#" class="waves-effect waves-light nav-link push-btn btn-primary-light" data-toggle="push-menu" role="button">
              <i data-feather="align-left"></i>
            </a>
          </li>
        </ul>
      </div>

      <div class="navbar-custom-menu r-side">
        <ul class="nav navbar-nav">
          <li class="btn-group nav-item d-lg-inline-flex d-none">
            <a href="#" data-provide="fullscreen" class="waves-effect waves-light nav-link full-screen btn-warning-light" title="Full Screen">
              <i data-feather="maximize"></i>
            </a>
          </li>

          <!-- User Account-->
          <li class="dropdown user user-menu">
            <a href="#" class="waves-effect waves-light dropdown-toggle w-auto l-h-12 bg-transparent py-0 no-shadow" data-bs-toggle="dropdown" title="User">
              <div class="d-flex pt-5">
                <div class="text-end me-10">
                  <p class="pt-5 fs-14 mb-0 fw-700 text-primary" th:text="${usuario.nombre + ' ' + usuario.apellido}">Usuario</p>
                  <small class="fs-10 mb-0 text-uppercase text-mute">Coordinador</small>
                </div>
                <img th:src="@{'/coordinador/profileimage/' + ${usuario.id}}" class="avatar rounded-10 bg-primary-light h-40 w-40" alt="" />
              </div>
            </a>
            <ul class="dropdown-menu animated flipInX">
              <li class="user-body">
                <a class="dropdown-item" th:href="@{/coordinador/perfil}"><i class="ti-user text-muted me-2"></i> Perfil</a>
                <div class="dropdown-divider"></div>
                <a class="dropdown-item" href="#" onclick="document.getElementById('logoutForm').submit(); return false;"><i class="ti-lock text-muted me-2"></i> Cerrar sesión</a>
              </li>
            </ul>
          </li>

        </ul>
      </div>
    </nav>
  </header>

  <!-- Left side column. contains the logo and sidebar -->
  <aside class="main-sidebar">
    <!-- sidebar-->
    <section class="sidebar position-relative">
      <div class="multinav">
        <div class="multinav-scroll" style="height: 100%;">
          <!-- sidebar menu-->
          <ul class="sidebar-menu" data-widget="tree">
            <li>
              <a th:href="@{/coordinador/perfil(id=3)}">
                <i data-feather="user"></i>
                <span>Perfil</span>
              </a>
            </li>
            <li>
              <a th:href="@{/coordinador/principal}">
                <i data-feather="clock"></i>
                <span>Marcar asistencia</span>
              </a>
            </li>
            <li>
              <a th:href="@{/coordinador/mis-observaciones}">
                <i data-feather="file-text"></i>
                <span>Mis Observaciones</span>
              </a>
            </li>
            <li class="active">
              <a th:href="@{/coordinador/horarios}">
                <i data-feather="calendar"></i>
                <span>Horarios</span>
              </a>
            </li>

            <li>
              <a href="#" class="nav-link" onclick="document.getElementById('logoutForm').submit(); return false;">
                <i data-feather="log-out"></i>
                <span>Cerrar sesión</span>
              </a>
            </li>
          </ul>
        </div>
      </div>
    </section>
  </aside>

  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <div class="container-full">
      <!-- Content Header (Page header) -->
      <div class="content-header">
        <div class="d-flex align-items-center">
          <div class="me-auto">
            <h4 class="page-title">Mis Horarios Laborales</h4>
            <div class="d-inline-block align-items-center">
              <nav>
                <ol class="breadcrumb">
                  <li class="breadcrumb-item"><a href="#"><i class="mdi mdi-home-outline"></i></a></li>
                  <li class="breadcrumb-item" aria-current="page">Coordinador</li>
                  <li class="breadcrumb-item active" aria-current="page">Horarios</li>
                </ol>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <!-- Main content -->
      <section class="content">
        <div class="row">
          <div class="col-12">
            <div class="box">
              <div class="box-header with-border">
                <h4 class="box-title">Calendario de Horarios Asignados</h4>
                <p class="box-subtitle">Visualiza tus horarios de trabajo asignados. Haz clic en cualquier evento para ver más detalles.</p>
              </div>
              <div class="box-body">
                <div id="calendar"></div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <!-- /.content -->
    </div>

  </div>
  <!-- /.content-wrapper -->

  <!-- Modal para mostrar detalles del horario -->
  <div class="modal fade" id="horarioModal" tabindex="-1" aria-labelledby="horarioModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="horarioModalLabel">Detalles del Horario</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-12">
              <h6><i class="fa fa-map-marker text-primary"></i> Ubicación:</h6>
              <p id="modalUbicacion" class="mb-3"></p>
              
              <h6><i class="fa fa-clock-o text-success"></i> Horario:</h6>
              <p id="modalHorario" class="mb-3"></p>
              
              <h6><i class="fa fa-calendar text-info"></i> Período:</h6>
              <p id="modalPeriodo" class="mb-3"></p>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Formulario oculto para logout -->
  <form id="logoutForm" th:action="@{/logout}" method="post" style="display: none;">
    <input type="hidden" name="_csrf" th:value="${_csrf.token}"/>
  </form>

  <footer class="main-footer">
    <div class="pull-right d-none d-sm-inline-block">
      <ul class="nav nav-primary nav-dotted nav-dot-separated justify-content-center justify-content-md-end">
        <li class="nav-item">
          <a class="nav-link" href="javascript:void(0)">FAQ</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="#">Soporte</a>
        </li>
      </ul>
    </div>
    &copy; <script>document.write(new Date().getFullYear())</script> <a href="#">Fibra</a>. Todos los derechos reservados.
  </footer>

</div>
<!-- ./wrapper -->

<!-- Vendor JS -->
<script th:src="@{/js/vendors.min.js}"></script>
<script th:src="@{/js/pages/chat-popup.js}"></script>
<script th:src="@{/assets/icons/feather-icons/feather.min.js}"></script>

<!-- FullCalendar -->
<script th:src="@{/assets/vendor_components/moment/moment.js}"></script>
<script th:src="@{/assets/vendor_components/fullcalendar/fullcalendar.min.js}"></script>
<script th:src="@{/assets/vendor_components/fullcalendar/locale/es.js}"></script>

<!-- Rhythm Admin App -->
<script th:src="@{/js/template.js}"></script>

<script>
$(document).ready(function() {
    // Configurar FullCalendar
    $('#calendar').fullCalendar({
        locale: 'es',
        header: {
            left: 'prev,next today',
            center: 'title',
            right: 'month,agendaWeek,agendaDay'
        },
        defaultView: 'month',
        editable: false,
        eventLimit: true,
        height: 'auto',
        events: function(start, end, timezone, callback) {
            // Cargar eventos desde el servidor
            $.ajax({
                url: '/coordinador/mis-horarios',
                type: 'GET',
                success: function(data) {
                    callback(data);
                },
                error: function() {
                    alert('Error al cargar los horarios');
                }
            });
        },
        eventClick: function(calEvent, jsEvent, view) {
            // Mostrar modal con detalles del horario
            var props = calEvent.extendedProps;
            
            $('#modalUbicacion').text(props.espacio + ' - ' + props.ubicacion);
            $('#modalHorario').text(props.horaEntrada + ' - ' + props.horaSalida);
            
            // Formatear fechas
            var fechaInicio = moment(props.fechaInicio).format('DD/MM/YYYY');
            var fechaFin = moment(props.fechaFin).format('DD/MM/YYYY');
            
            if (fechaInicio === fechaFin) {
                $('#modalPeriodo').text(fechaInicio);
            } else {
                $('#modalPeriodo').text(fechaInicio + ' - ' + fechaFin);
            }
            
            $('#horarioModal').modal('show');
        }
    });
});
</script>

</body>
</html>
