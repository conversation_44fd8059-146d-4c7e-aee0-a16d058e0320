<!DOCTYPE html>
<html lang="es" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="/images/logo-solo.png">

    <title>Recuperar contraseña</title>

    <!-- Vendors Style-->
    <link rel="stylesheet" href="/css/vendors_css.css">

    <!-- Style-->
    <link rel="stylesheet" href="/css/style.css">
    <link rel="stylesheet" href="/css/skin_color.css">

</head>

<body class="hold-transition theme-primary bg-img" th:style="|background-image: url('@{/images/auth-bg/background.png}')|">

<div class="container h-p100">
    <div class="row align-items-center justify-content-md-center h-p100">

        <div class="col-12">
            <div class="row justify-content-center g-0">
                <div class="col-lg-5 col-md-5 col-12">
                    <div class="bg-white rounded10 shadow-lg">
                        <div class="content-top-agile p-20 pb-0">
                            <h3 class="mb-0 text-primary">Cuenta Activada</h3>
                            <br>
                            <img th:src="@{/images/logo-sanMiguel.png}" alt="Logo Municipalidad"  width="150">
                        </div>
                        <div class="p-40">
                                <div class="p-40">
                                    <h3 th:text="'¡Hola ' + ${usuario.getNombres()} + ' ' + ${usuario.getApellidos()} + '!'"></h3>
                                    <h4>Gracias por registrarte. Tu cuenta ya está activada, inicia sesión para que puedas acceder a la plataforma.</h4>
                                </div>
                                <div class="row">
                                    <div class="col-12 text-center" style="margin-top: 10px;">
                                        <a th:href="@{/login}" class="btn btn-info margin-top-10">Ingresar</a>
                                    </div>
                                    <!-- /.col -->
                                </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- Vendor JS -->
<script src="/js/vendors.min.js"></script>
<script src="/js/pages/chat-popup.js"></script>
<script src="/assets/icons/feather-icons/feather.min.js"></script>

</body>
</html>