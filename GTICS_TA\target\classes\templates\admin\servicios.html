<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <meta name="_csrf" th:content="${_csrf.token}" />
    <meta name="_csrf_header" th:content="${_csrf.headerName}" />

    <link rel="icon" th:href="@{/images/san_miguel_logo.ico}">

    <title>Admin - Servicios</title>

    <!-- Vendors Style-->

    <!-- Style-->
    <link rel="stylesheet" th:href="@{/css/vendors_css.css}">
    <link rel="stylesheet" th:href="@{/css/style.css}">
    <link rel="stylesheet" th:href="@{/css/skin_color.css}">

    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@mdi/font/css/materialdesignicons.min.css">


    <style>

        .dataTables_filter {
            margin-bottom: 20px; /* Separación */
            float: right; /* Puedes quitar esto si lo quieres alineado a la izquierda */
        }
    </style>

</head>

<body class="hold-transition light-skin sidebar-mini theme-success fixed">

<div class="wrapper">
    <div id="loader"></div>

    <header class="main-header">

        <div class="d-flex align-items-center logo-box justify-content-center">
            <!-- Logo -->
            <a th:href="@{/admin}" class="logo">
                <!-- logo-->
                <div class="logo-mini w-150 text-center">
                    <span class="light-logo"><img th:src="@{/images/logo-sanMiguel.png}" alt="logo"></span>
                </div>
            </a>
        </div>
        <!-- Header Navbar -->
        <nav class="navbar navbar-static-top">
            <!-- Sidebar toggle button-->
            <div class="app-menu">
                <ul class="header-megamenu nav">
                    <li class="btn-group nav-item">
                        <a href="#" class="waves-effect waves-light nav-link push-btn btn-primary-light" data-toggle="push-menu" role="button">
                            <i data-feather="align-left"></i>
                        </a>
                    </li>

                </ul>
            </div>

            <div th:replace="fragments :: navbarAdmin"></div>
        </nav>
    </header>

    <aside th:replace="fragments :: sideBarAdmin"></aside>

    <!-- Content Wrapper. Contains page content -->
    <div class="content-wrapper">
        <div class="container-full">
            <!-- Content Header (Page header) -->
            <div class="content-header">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h2 class="fw-bold mb-0 me-3">Servicios</h2>
                    <div>
                        <a th:href="@{/admin/nuevo}" class="btn btn-sm text-white" style="background-color: #008B9A;">
                            <i data-feather="plus"></i> Agregar Servicio
                        </a>
                    </div>
                </div>
            </div>

            <!-- Main content -->
            <section class="content">
                <div class="row mt-2">
                    <div class="col-12">
                        <div class="box">

                            <div class="box-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped" id="tablaServicios">
                                        <thead>
                                        <tr>
                                            <th>Tipo de Servicio</th>
                                            <th>Nombre</th>
                                            <th>Ubicación</th>
                                            <th>Aforo</th>
                                            <th>Horario</th>
                                            <th>Opciones</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <!-- Iteración sobre la lista de espacios deportivos -->
                                        <tr th:each="servicio : ${listaEspacios}" th:attr="data-id=${servicio.id}">
                                            <td th:text="${servicio.tipoEspacio.nombre}"></td>
                                            <td th:text="${servicio.nombre}"></td>
                                            <td th:text="${servicio.ubicacion}"></td>
                                            <td th:text="${servicio.aforo != null ? servicio.aforo : '-'}"></td>
                                            <td th:text="${servicio.horaAbre + ' - ' + servicio.horaCierra}"></td>
                                            <td>
                                                <a type="button"
                                                        class="btn btn-sm btn-warning me-1"
                                                        th:href="@{/admin/editar(id=${servicio.id})}">
                                                    <i class="mdi mdi-pencil"></i>
                                                </a>

                                                <button type="button"
                                                        class="btn btn-sm btn-info me-1"
                                                        title="Mantenimiento"
                                                        onclick="abrirMantenimiento(this)">
                                                    <i class="mdi mdi-wrench"></i>
                                                </button>

                                                <button type="button"
                                                        class="btn btn-sm btn-danger delete-btn"
                                                        title="Eliminar"
                                                        data-bs-toggle="modal"
                                                        th:attr="data-bs-target=${'#modal-cancel-' + servicio.id}">
                                                    <i class="mdi mdi-delete"></i>
                                                </button>
                                                <div class="btn-group dropdown">
                                                    <button type="button" class="btn btn-sm btn-info dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                                        <i class="mdi mdi-download"></i> Reporte
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li>
                                                            <a class="dropdown-item"
                                                               th:href="@{/admin/servicios/exportar-reporte-pdf(id=${servicio.id})}">
                                                                <i class="mdi mdi-file-pdf text-danger"></i> Descargar PDF
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="dropdown-item"
                                                               th:href="@{/admin/servicios/exportar-reporte-excel(id=${servicio.id})}">
                                                                <i class="mdi mdi-file-excel text-success"></i> Descargar Excel
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>


                                            </td>
                                        </tr>
                                        </tbody>

                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- /.content -->
        </div>
    </div>
    <!-- /.content-wrapper -->
    <footer class="main-footer">
        <div class="pull-right d-none d-sm-inline-block">
            <ul class="nav nav-primary nav-dotted nav-dot-separated justify-content-center justify-content-md-end">
                <li class="nav-item">
                    <a class="nav-link" href="javascript:void(0)">Contáctanos</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#">FAQ</a>
                </li>
            </ul>
        </div>
        &copy; <script>document.write(new Date().getFullYear())</script> <a href="https://www.munisanmiguel.gob.pe/">Municipalidad de San Miguel</a>. Todos los derechos reservados.
    </footer>

    <!-- Modal Eliminar único -->
    <div class="modal fade" th:each="servicio, idx : ${listaEspacios}" id="modalEliminar" th:id="'modal-cancel-' + ${servicio.getId()}" tabindex="-1" aria-labelledby="modalEliminarLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="modalEliminarLabel">¿Estás seguro?</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Cerrar"></button>
                </div>
                <div class="modal-body">
                    Esta acción eliminará el servicio de forma permanente.<br>
                    ATENCIÓN, ESTA ACCIÓN ELIMINARÁ TODO EL CONTENIDO RELACIONADO A ESTE SERVICIO (RESERVAS, HORARIOS)<br>
                    ¿Deseas continuar?
                </div>
                <div class="modal-footer">
                    <form th:action="@{/admin/eliminar/}" method="post">
                    <input type="hidden" th:value="${servicio.id}" name="id">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" id="btnConfirmarEliminar" class="btn btn-danger">Eliminar</button>
                    </form>
                </div>
            </div>
        </div>
    </div>


    <!-- Modal Programar Mantenimiento -->
    <div class="modal fade" id="modalMantenimiento" tabindex="-1" aria-labelledby="modalMantenimientoLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h5 class="modal-title" id="modalMantenimientoLabel">
                        <i class="mdi mdi-wrench me-2"></i>Programar Mantenimiento
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Cerrar"></button>
                </div>
                <div class="modal-body">
                    <form id="formMantenimiento">
                        <input type="hidden" id="mantenimientoId" name="id" />

                        <!-- Información del servicio (solo lectura) -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="mantenimientoTipo" class="form-label">Tipo de Servicio</label>
                                <input type="text" class="form-control" id="mantenimientoTipo" readonly>
                            </div>
                            <div class="col-md-6">
                                <label for="mantenimientoNombre" class="form-label">Nombre del Lugar</label>
                                <input type="text" class="form-control" id="mantenimientoNombre" readonly>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="mantenimientoUbicacion" class="form-label">Ubicación</label>
                            <input type="text" class="form-control" id="mantenimientoUbicacion" readonly>
                        </div>

                        <!-- Información del mantenimiento -->
                        <hr>
                        <h6 class="text-info mb-3">
                            <i class="mdi mdi-calendar-clock me-2"></i>Detalles del Mantenimiento
                        </h6>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="fechaMantenimiento" class="form-label">Fecha de Mantenimiento <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="fechaMantenimiento" name="fechaMantenimiento" required>
                            </div>
                            <div class="col-md-6">
                                <label for="tipoMantenimiento" class="form-label">Tipo de Mantenimiento <span class="text-danger">*</span></label>
                                <select class="form-select" id="tipoMantenimiento" name="tipoMantenimiento" required>
                                    <option value="">Seleccione tipo</option>
                                    <option value="preventivo">🔧 Preventivo</option>
                                    <option value="correctivo">⚠️ Correctivo</option>
                                    <option value="limpieza">🧹 Limpieza Profunda</option>
                                    <option value="inspeccion">🔍 Inspección</option>
                                    <option value="reparacion">🔨 Reparación</option>
                                </select>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="horaInicioMantenimiento" class="form-label">Hora de Inicio <span class="text-danger">*</span></label>
                                <input type="time" class="form-control" id="horaInicioMantenimiento" name="horaInicio" required>
                            </div>
                            <div class="col-md-6">
                                <label for="horaFinMantenimiento" class="form-label">Hora de Fin <span class="text-danger">*</span></label>
                                <input type="time" class="form-control" id="horaFinMantenimiento" name="horaFin" required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="responsableMantenimiento" class="form-label">Responsable <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="responsableMantenimiento" name="responsable"
                                   placeholder="Nombre del responsable del mantenimiento" required>
                        </div>

                        <div class="mb-3">
                            <label for="descripcionMantenimiento" class="form-label">Descripción del Trabajo <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="descripcionMantenimiento" name="descripcion" rows="3"
                                      placeholder="Describa las actividades de mantenimiento a realizar..." required></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="prioridadMantenimiento" class="form-label">Prioridad</label>
                            <select class="form-select" id="prioridadMantenimiento" name="prioridad">
                                <option value="baja">🟢 Baja</option>
                                <option value="media" selected>🟡 Media</option>
                                <option value="alta">🟠 Alta</option>
                                <option value="urgente">🔴 Urgente</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="suspenderServicio" name="suspenderServicio">
                                <label class="form-check-label" for="suspenderServicio">
                                    <strong>Suspender servicio durante el mantenimiento</strong>
                                    <small class="text-muted d-block">El servicio no estará disponible para reservas durante este período</small>
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="mdi mdi-close me-1"></i>Cancelar
                    </button>
                    <button type="button" class="btn btn-success" id="btnProgramarMantenimiento" onclick="programarMantenimiento()">
                        <i class="mdi mdi-calendar-check me-1"></i>Programar Mantenimiento
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add the sidebar's background. This div must be placed immediately after the control sidebar -->
    <div class="control-sidebar-bg"></div>

</div>
<!-- ./wrapper -->

<!-- ./side demo panel -->

<!-- Sidebar -->

<script>

    // Función para abrir el modal de mantenimiento
    function abrirMantenimiento(boton) {
        const fila = boton.closest('tr');
        const celdas = fila.querySelectorAll('td');
        const id = fila.getAttribute('data-id');

        // Llenar los campos de información del servicio (solo lectura)
        document.getElementById('mantenimientoId').value = id;
        document.getElementById('mantenimientoTipo').value = celdas[0].textContent.trim();
        document.getElementById('mantenimientoNombre').value = celdas[1].textContent.trim();
        document.getElementById('mantenimientoUbicacion').value = celdas[2].textContent.trim();

        // Limpiar los campos del formulario de mantenimiento
        document.getElementById('fechaMantenimiento').value = '';
        document.getElementById('tipoMantenimiento').value = '';
        document.getElementById('horaInicioMantenimiento').value = '';
        document.getElementById('horaFinMantenimiento').value = '';
        document.getElementById('responsableMantenimiento').value = '';
        document.getElementById('descripcionMantenimiento').value = '';
        document.getElementById('prioridadMantenimiento').value = 'media';
        document.getElementById('suspenderServicio').checked = false;

        // Establecer fecha mínima como hoy
        const hoy = new Date().toISOString().split('T')[0];
        document.getElementById('fechaMantenimiento').min = hoy;

        // Abrir el modal
        const modal = new bootstrap.Modal(document.getElementById('modalMantenimiento'));
        modal.show();
    }

    // Función para programar el mantenimiento
    function programarMantenimiento() {
        // Validar campos requeridos
        const campos = {
            fecha: document.getElementById('fechaMantenimiento').value,
            tipo: document.getElementById('tipoMantenimiento').value,
            horaInicio: document.getElementById('horaInicioMantenimiento').value,
            horaFin: document.getElementById('horaFinMantenimiento').value,
            responsable: document.getElementById('responsableMantenimiento').value.trim(),
            descripcion: document.getElementById('descripcionMantenimiento').value.trim()
        };

        // Validaciones
        if (!campos.fecha || !campos.tipo || !campos.horaInicio || !campos.horaFin || !campos.responsable || !campos.descripcion) {
            alert('Por favor complete todos los campos');
            return;
        }

        if (campos.horaInicio >= campos.horaFin) {
            alert('La hora de fin debe ser posterior a la hora de inicio');
            return;
        }

        const datosMantenimiento = {
            servicioId: document.getElementById('mantenimientoId').value,
            servicioNombre: document.getElementById('mantenimientoNombre').value,
            servicioTipo: document.getElementById('mantenimientoTipo').value,
            servicioUbicacion: document.getElementById('mantenimientoUbicacion').value,
            fecha: campos.fecha,
            tipo: campos.tipo,
            horaInicio: campos.horaInicio,
            horaFin: campos.horaFin,
            responsable: campos.responsable,
            descripcion: campos.descripcion,
            prioridad: document.getElementById('prioridadMantenimiento').value,
            suspenderServicio: document.getElementById('suspenderServicio').checked
        };

        const params = new URLSearchParams();
        params.append('servicioId', datosMantenimiento.servicioId);
        params.append('fecha', datosMantenimiento.fecha);
        params.append('tipo', datosMantenimiento.tipo);
        params.append('horaInicio', datosMantenimiento.horaInicio);
        params.append('horaFin', datosMantenimiento.horaFin);
        params.append('responsable', datosMantenimiento.responsable);
        params.append('descripcion', datosMantenimiento.descripcion);
        params.append('prioridad', datosMantenimiento.prioridad);
        params.append('suspenderServicio', datosMantenimiento.suspenderServicio);

        const btnProgramar = document.getElementById('btnProgramarMantenimiento');
        const textoOriginal = btnProgramar.textContent;
        btnProgramar.disabled = true;
        btnProgramar.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Programando...';

        fetch('/admin/programar-mantenimiento', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: params
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const tipoTexto = document.getElementById('tipoMantenimiento').selectedOptions[0].textContent;
                    const prioridadTexto = document.getElementById('prioridadMantenimiento').selectedOptions[0].textContent;
                    mostrarConfirmacionMantenimiento(datosMantenimiento, tipoTexto, prioridadTexto, data);

                    const modal = bootstrap.Modal.getInstance(document.getElementById('modalMantenimiento'));
                    modal.hide();

                    document.getElementById('formMantenimiento').reset();
                } else {
                    alert('❌ Error al programar mantenimiento: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('❌ Error de conexión al programar mantenimiento');
            })
            .finally(() => {
                btnProgramar.disabled = false;
                btnProgramar.innerHTML = textoOriginal;
            });
    }


    /**
     * Muestra confirmación de mantenimiento con formato similar a reservas
     */
    function mostrarConfirmacionMantenimiento(datos, tipoTexto, prioridadTexto, response) {
        // Crear modal de confirmación
        const modalHtml = `
            <div class="modal fade" id="modalConfirmacionMantenimiento" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-success text-white">
                            <h5 class="modal-title">
                                <i class="fas fa-check-circle me-2"></i>
                                Mantenimiento Programado Exitosamente
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card border-0 bg-light">
                                        <div class="card-body">
                                            <h6 class="card-title text-primary">
                                                <i class="fas fa-info-circle me-2"></i>Detalles del Mantenimiento
                                            </h6>
                                            <div class="mb-2">
                                                <strong>🏢 Servicio:</strong> ${datos.servicioNombre}
                                            </div>
                                            <div class="mb-2">
                                                <strong>📅 Fecha:</strong> ${datos.fecha}
                                            </div>
                                            <div class="mb-2">
                                                <strong>⏰ Horario:</strong> ${datos.horaInicio} - ${datos.horaFin}
                                            </div>
                                            <div class="mb-2">
                                                <strong>🔧 Tipo:</strong> ${tipoTexto}
                                            </div>
                                            <div class="mb-2">
                                                <strong>⚡ Prioridad:</strong> ${prioridadTexto}
                                            </div>
                                            <div class="mb-2">
                                                <strong>👤 Responsable:</strong> ${datos.responsable}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card border-0 bg-light">
                                        <div class="card-body">
                                            <h6 class="card-title text-info">
                                                <i class="fas fa-chart-bar me-2"></i>Resumen de Acciones
                                            </h6>
                                            <div class="mb-2">
                                                <strong>📋 ID Mantenimiento:</strong> #${response.mantenimientoId}
                                            </div>
                                            <div class="mb-2">
                                                <strong>🚫 Reservas Canceladas:</strong> ${response.reservasCanceladas || 0}
                                            </div>
                                            <div class="mb-2">
                                                <strong>📧 Notificaciones:</strong>
                                                ${response.notificacionesEnviadas ?
                                                    '<span class="text-success">✅ Enviadas</span>' :
                                                    '<span class="text-muted">➖ No requeridas</span>'}
                                            </div>
                                            <div class="mb-2">
                                                <strong>🔒 Estado Servicio:</strong>
                                                ${datos.suspenderServicio ?
                                                    '<span class="text-warning">⚠️ Suspendido</span>' :
                                                    '<span class="text-success">✅ Disponible</span>'}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            ${datos.descripcion ? `
                                <div class="mt-3">
                                    <div class="card border-0 bg-light">
                                        <div class="card-body">
                                            <h6 class="card-title text-secondary">
                                                <i class="fas fa-file-alt me-2"></i>Descripción
                                            </h6>
                                            <p class="mb-0">${datos.descripcion}</p>
                                        </div>
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-success" data-bs-dismiss="modal">
                                <i class="fas fa-check me-2"></i>Entendido
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Agregar modal al DOM
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Mostrar modal
        const modal = new bootstrap.Modal(document.getElementById('modalConfirmacionMantenimiento'));
        modal.show();

        // Limpiar modal del DOM cuando se cierre
        document.getElementById('modalConfirmacionMantenimiento').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    }


</script>



<!-- Page Content overlay -->
<!-- Script de DataTables si no está activado -->
<!-- jQuery -->



<!-- Vendor JS -->
<script src="/js/vendors.min.js"></script>
<script src="/js/pages/chat-popup.js"></script>
<script src="/assets/icons/feather-icons/feather.min.js"></script>

<script src="/assets/vendor_components/datatable/datatables.min.js"></script>

<!-- Rhythm Admin App -->
<script src="/js/template.js"></script>
<script src="/js/pages/patients.js"></script>

<script>
    $(document).ready(function () {
        $('#tablaServicios').DataTable({
            pageLength: 5,
            lengthChange: false,
            info: false,
            language: {
                url: "//cdn.datatables.net/plug-ins/1.13.6/i18n/es-ES.json",
                search: "Buscar:",
                paginate: {
                    previous: "Anterior",
                    next: "Siguiente"
                }
            },
            dom: '<"datatable-header"f>rt<"datatable-footer"p><"clear">'
        });
    });
</script>

<script>
    feather.replace();
</script>


<script>
    document.addEventListener("DOMContentLoaded", function () {
        const loader = document.getElementById('loader');
        if (loader) {
            loader.style.display = 'none';  // Oculta el fondo negro
        }
    });
</script>




</body>
</html>
