<!DOCTYPE html>
<html lang="es" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="/images/san_miguel_logo.ico">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <title>Reservar un Espacio </title>

    <!-- Vendors Style-->
    <link rel="stylesheet" href="/css/vendors_css.css">

    <!-- Style-->
    <link rel="stylesheet" href="/css/style.css">
    <link rel="stylesheet" href="/css/skin_color.css">
</head>
<body class="hold-transition light-skin sidebar-mini theme-success fixed">

<div class="wrapper">
    <div id="loader"></div>

    <header class="main-header">
        <div class="d-flex align-items-center logo-box justify-content-center">
            <!-- Logo -->
            <a href="/static/assets/vendor_components/jquery-validation-1.17.0/demo/requirejs/index.html" class="logo">
                <!-- logo-->
                <div class="logo-mini w-150 text-center">
                    <span class="light-logo"><img th:src="@{/images/logo-sanMiguel.png}" alt="logo"></span>
                </div>
            </a>
        </div>
        <!-- Header Navbar -->
        <nav class="navbar navbar-static-top">
            <div class="app-menu">
                <ul class="header-megamenu nav">
                    <li class="btn-group nav-item">
                        <a href="#" class="waves-effect waves-light nav-link push-btn btn-primary-light" data-toggle="push-menu" role="button">
                            <i data-feather="align-left"></i>
                        </a>
                    </li>
                </ul>
            </div>

            <div th:replace="fragments :: navbarFragment"></div>
        </nav>
    </header>

    <!-- Left side column. contains the logo and sidebar -->
    <aside th:replace="fragments :: sidebarFragment"></aside>

    <!-- Content Wrapper. Contains page content -->
    <div class="content-wrapper">
        <div class="container-full">
            <!-- Content Header (Page header) -->
            <div class="content-header">
                <div class="d-flex align-items-center">
                    <div class="me-auto">
                        <h4 class="page-title">Realizar una Reserva</h4>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main content -->
        <section class="content">
            <div class="box">
                <div class="box-header with-border">
                    <h4 class="box-title">Completa tu Reserva</h4>
                    <h6 class="box-subtitle">Rellena el siguiente formulario con tu información para proceder con la reserva.</h6>
                </div>
                <!-- /.box-header -->
                <div class="box-body wizard-content">
                    <form th:action="@{/vecino/guardarreserva}" th:object="${reserva}" method="post" enctype="multipart/form-data" class="validation-wizard wizard-circle">
                        <input type="hidden" th:value="${reserva.getUsuario().getId()}" th:field="*{usuario}">
                        <input type="hidden" th:value="${reserva.getEspacioDeportivo().getId()}" th:field="*{espacioDeportivo}">
                        <!-- Step 1 -->
                        <h6>Información Personal</h6>
                        <section>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="wfirstName2" class="form-label"> Nombre : <span class="danger">*</span> </label>
                                        <input disabled type="text"  th:field="*{usuario.nombres}" class="form-control required" id="wfirstName2" name="nombre">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="wlastName2" class="form-label"> Apellido : <span class="danger">*</span> </label>
                                        <input disabled type="text" th:field="*{usuario.apellidos}" class="form-control required" id="wlastName2" name="apellido">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="wemailAddress2" class="form-label"> Correo electrónico:<span class="danger">*</span> </label>
                                        <input disabled type="email" th:field="*{usuario.correo}" class="form-control required" id="wemailAddress2" name="correo">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="wphoneNumber2" class="form-label">Celular :</label>
                                        <input disabled type="tel" th:field="*{usuario.numCelular}" class="form-control" id="wphoneNumber2" name="celular">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="wdate1" class="form-label">Fecha de Nacimiento :</label>
                                        <input disabled th:field="*{usuario.fechaNacimiento}" type="date" class="form-control required" id="wdate1">
                                    </div>
                                </div>
                            </div>
                        </section>
                        <!-- Step 2 -->
                        <h6>Fecha y Horario</h6>
                        <section>
                            <div class="row">
                                <div class="col-md-3">
                                    <h6>Elija la fecha de su reserva</h6>
                                        <div class="form-group">
                                            <label for="wdate2" class="form-label">Fecha :</label>
                                            <input th:attr="min=${minDate}" type="date" th:field="*{fechaReserva}" class="form-control required" id="wdate2" onchange="cargarHorarios()">
                                            <input type="hidden" id="idEspacio" th:value="${reserva.espacioDeportivo.id}" />

                                        </div>
                                </div>
                                <div class="col-md-9">
                                    <h5>Elija el horario de su reserva</h5>
                                    <div id="horarios-container" class="clearfix" aria-label="Horarios">
                                        <th:block th:each="horariosConsulta, idx : ${listaHorarios}">
                                            <button type="button"
                                                    class="waves-effect waves-light btn "
                                                    th:id="'btn-horario-' + ${idx.index}"
                                                    th:text="${horariosConsulta.getHoraInicio()} + '-' + ${horariosConsulta.getHoraFin()}"
                                                    th:disabled="${horariosConsulta.getReservado() != null and horariosConsulta.getReservado() == 1}"
                                                    th:classappend="${horariosConsulta.getReservado() != null and horariosConsulta.getReservado() == 1} ? ' btn-danger-light mb-5' : ' btn-primary-light mb-5'"
                                                    th:onclick="'selectHorario(this, ' + ${horariosConsulta.getIdHorarios()} + ')'">
                                            </button>
                                            <span th:if="${((idx.index + 1) % 3 == 0)}"><br/></span>
                                        </th:block>
                                    </div>

                                    <!-- Campo hidden para enviar el horario seleccionado -->
                                    <input type="hidden" id="horarioSeleccionado" th:field="*{horario}" required />
                                </div>
                            </div>
                        </section>
                        <!-- Step 3 -->
                        <h6>Pago</h6>
                        <section>
                            <div class="row">
                                <div class="col-12">
                                    <h5 class="mb-3">Seleccione su método de pago</h5>

                                    <!-- Medios de Pago -->
                                    <div class="row" id="medios-pago-container">
                                        <th:block th:each="medio : ${mediosPago}">
                                            <div class="col-md-6 mb-3">
                                                <div class="card payment-method-card"
                                                     onclick="selectPaymentMethod(this)"
                                                     th:attr="data-id=${medio.id}, data-tipo=${medio.tipoPago}, data-datos-cuenta=${medio.datosCuenta}">
                                                    <div class="card-body text-center">
                                                        <i class="fa fa-credit-card fa-2x mb-2" th:if="${medio.tipoPago.name() == 'AUTOMATICO'}"></i>
                                                        <i class="fa fa-bank fa-2x mb-2" th:if="${medio.tipoPago.name() == 'MANUAL'}"></i>
                                                        <h5 th:text="${medio.nombre}"></h5>
                                                        <p class="text-muted" th:text="${medio.descripcion}"></p>
                                                    </div>
                                                </div>
                                            </div>
                                        </th:block>
                                    </div>

                                    <!-- Campo hidden para el medio de pago seleccionado -->
                                    <input type="hidden" id="medioPagoSeleccionado" name="medioPagoId" required />

                                    <!-- Formulario de Tarjeta de Crédito/Débito -->
                                    <div id="formulario-tarjeta" class="payment-form" style="display: none;">
                                        <h5 class="mb-3">Datos de la Tarjeta</h5>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="numeroTarjeta" class="form-label">Número de Tarjeta *</label>
                                                    <input type="text" class="form-control" id="numeroTarjeta" name="numeroTarjeta"
                                                           placeholder="1234 5678 9012 3456" maxlength="19">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="nombreTarjeta" class="form-label">Nombre en la Tarjeta *</label>
                                                    <input type="text" class="form-control" id="nombreTarjeta" name="nombreTarjeta"
                                                           placeholder="JUAN PEREZ">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="fechaExpiracion" class="form-label">Fecha de Expiración *</label>
                                                    <input type="text" class="form-control" id="fechaExpiracion" name="fechaExpiracion"
                                                           placeholder="MM/AA" maxlength="5">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="cvv" class="form-label">CVV *</label>
                                                    <input type="text" class="form-control" id="cvv" name="cvv"
                                                           placeholder="123" maxlength="4">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Formulario de Transferencia/Yape/Plin -->
                                    <div id="formulario-transferencia" class="payment-form" style="display: none;">
                                        <h5 class="mb-3">Datos de Transferencia</h5>
                                        <div id="datos-cuenta-container" class="alert alert-info mb-3">
                                            <!-- Se llenará dinámicamente con JavaScript -->
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="numeroTransaccion" class="form-label">Número de Operación/Transacción *</label>
                                                    <input type="text" class="form-control" id="numeroTransaccion" name="numeroTransaccion"
                                                           placeholder="Ingrese el número de operación">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="comprobantes" class="form-label">Comprobante de Pago *</label>
                                                    <input type="file" class="form-control" id="comprobantes" name="comprobantes"
                                                           accept="image/*" multiple>
                                                    <small class="form-text text-muted">Suba una foto del comprobante de pago (máximo 4 imágenes)</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Resumen del Pago -->
                                    <div class="card mt-4">
                                        <div class="card-header">
                                            <h5>Resumen del Pago</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-6">
                                                    <strong>Costo por hora:</strong>
                                                </div>
                                                <div class="col-6 text-end">
                                                    <span th:text="'S/. ' + ${reserva.espacioDeportivo.costoHorario}"></span>
                                                </div>
                                            </div>
                                            <hr>
                                            <div class="row">
                                                <div class="col-6">
                                                    <strong>Total a pagar:</strong>
                                                </div>
                                                <div class="col-6 text-end">
                                                    <strong th:text="'S/. ' + ${reserva.espacioDeportivo.costoHorario}"></strong>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                    </form>
                </div>
                <!-- /.box-body -->
            </div>
        </section>
        <!-- /.content -->
    </div>
</div>
<!-- /.content-wrapper -->

<!-- Modal -->
<div class="modal fade" id="pagoModal" tabindex="-1" aria-labelledby="pagoModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content p-4">
            <div class="modal-header">
                <h5 class="modal-title">Reserva</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Cerrar"></button>
            </div>
            <div class="modal-body">

                <p class="text-center">Puedes pagar usando:</p>

                <div class="d-flex justify-content-between mb-3">
                    <div class="pay-option active" onclick="selectPayment(this)">Tarjeta</div>
                    <div class="pay-option" onclick="selectPayment(this)">QR</div>
                    <div class="pay-option" onclick="selectPayment(this)">Yape</div>
                </div>

                <small class="text-muted d-block mb-3">Recuerda activar tus compras por internet</small>

                <form>
                    <div class="mb-2">
                        <input type="text" class="form-control" placeholder="Número de tarjeta">
                    </div>
                    <div class="row g-2 mb-2">
                        <div class="col">
                            <input type="text" class="form-control" placeholder="Caducidad">
                        </div>
                        <div class="col">
                            <input type="text" class="form-control" placeholder="CVV">
                        </div>
                    </div>
                    <div class="row g-2 mb-2">
                        <div class="col">
                            <input type="text" class="form-control" placeholder="Nombres">
                        </div>
                        <div class="col">
                            <input type="text" class="form-control" placeholder="Apellidos">
                        </div>
                    </div>
                    <div class="mb-3">
                        <input type="email" class="form-control" placeholder="Correo">
                    </div>
                    <button type="button" class="btn btn-teal w-100" style="background-color:#00bfa6; color:white;">
                        Pagar S/25.00
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<footer class="main-footer">
    &copy; <script>document.write(new Date().getFullYear())</script> <a href="https://munisanmiguel.gob.pe/">Municipalidad de San Miguel</a>. Todos los derechos reservados.
</footer>
<!-- Control Sidebar -->
<aside class="control-sidebar">

    <div class="rpanel-title"><span class="pull-right btn btn-circle btn-danger" data-toggle="control-sidebar"><i class="ion ion-close text-white"></i></span> </div>  <!-- Create the tabs -->
    <ul class="nav nav-tabs control-sidebar-tabs">
        <li class="nav-item"><a href="#control-sidebar-home-tab" data-bs-toggle="tab" class="active"><i class="mdi mdi-message-text"></i></a></li>
        <li class="nav-item"><a href="#control-sidebar-settings-tab" data-bs-toggle="tab"><i class="mdi mdi-playlist-check"></i></a></li>
    </ul>
    <!-- Tab panes -->
    <div class="tab-content">
        <!-- Home tab content -->
        <div class="tab-pane active" id="control-sidebar-home-tab">
            <div class="flexbox">
                <a href="javascript:void(0)" class="text-grey">
                    <i class="ti-more"></i>
                </a>
                <p>Users</p>
                <a href="javascript:void(0)" class="text-end text-grey"><i class="ti-plus"></i></a>
            </div>
            <div class="media-list media-list-hover mt-20">
                <div class="media py-10 px-0">
                    <a class="avatar avatar-lg status-success" href="#">
                        <img src="../images/avatar/1.jpg" alt="...">
                    </a>
                    <div class="media-body">
                        <p class="fs-16">
                            <a class="hover-primary" href="#"><strong>Tyler</strong></a>
                        </p>
                        <p>Praesent tristique diam...</p>
                        <span>Just now</span>
                    </div>
                </div>

                <div class="media py-10 px-0">
                    <a class="avatar avatar-lg status-danger" href="#">
                        <img src="../images/avatar/2.jpg" alt="...">
                    </a>
                    <div class="media-body">
                        <p class="fs-16">
                            <a class="hover-primary" href="#"><strong>Luke</strong></a>
                        </p>
                        <p>Cras tempor diam ...</p>
                        <span>33 min ago</span>
                    </div>
                </div>

                <div class="media py-10 px-0">
                    <a class="avatar avatar-lg status-warning" href="#">
                        <img src="../images/avatar/3.jpg" alt="...">
                    </a>
                    <div class="media-body">
                        <p class="fs-16">
                            <a class="hover-primary" href="#"><strong>Evan</strong></a>
                        </p>
                        <p>In posuere tortor vel...</p>
                        <span>42 min ago</span>
                    </div>
                </div>

                <div class="media py-10 px-0">
                    <a class="avatar avatar-lg status-primary" href="#">
                        <img src="../images/avatar/4.jpg" alt="...">
                    </a>
                    <div class="media-body">
                        <p class="fs-16">
                            <a class="hover-primary" href="#"><strong>Evan</strong></a>
                        </p>
                        <p>In posuere tortor vel...</p>
                        <span>42 min ago</span>
                    </div>
                </div>

                <div class="media py-10 px-0">
                    <a class="avatar avatar-lg status-success" href="#">
                        <img src="../images/avatar/1.jpg" alt="...">
                    </a>
                    <div class="media-body">
                        <p class="fs-16">
                            <a class="hover-primary" href="#"><strong>Tyler</strong></a>
                        </p>
                        <p>Praesent tristique diam...</p>
                        <span>Just now</span>
                    </div>
                </div>

                <div class="media py-10 px-0">
                    <a class="avatar avatar-lg status-danger" href="#">
                        <img src="../images/avatar/2.jpg" alt="...">
                    </a>
                    <div class="media-body">
                        <p class="fs-16">
                            <a class="hover-primary" href="#"><strong>Luke</strong></a>
                        </p>
                        <p>Cras tempor diam ...</p>
                        <span>33 min ago</span>
                    </div>
                </div>

                <div class="media py-10 px-0">
                    <a class="avatar avatar-lg status-warning" href="#">
                        <img src="../images/avatar/3.jpg" alt="...">
                    </a>
                    <div class="media-body">
                        <p class="fs-16">
                            <a class="hover-primary" href="#"><strong>Evan</strong></a>
                        </p>
                        <p>In posuere tortor vel...</p>
                        <span>42 min ago</span>
                    </div>
                </div>

                <div class="media py-10 px-0">
                    <a class="avatar avatar-lg status-primary" href="#">
                        <img src="../images/avatar/4.jpg" alt="...">
                    </a>
                    <div class="media-body">
                        <p class="fs-16">
                            <a class="hover-primary" href="#"><strong>Evan</strong></a>
                        </p>
                        <p>In posuere tortor vel...</p>
                        <span>42 min ago</span>
                    </div>
                </div>

            </div>

        </div>
        <!-- /.tab-pane -->
        <!-- Settings tab content -->
        <div class="tab-pane" id="control-sidebar-settings-tab">
            <div class="flexbox">
                <a href="javascript:void(0)" class="text-grey">
                    <i class="ti-more"></i>
                </a>
                <p>Todo List</p>
                <a href="javascript:void(0)" class="text-end text-grey"><i class="ti-plus"></i></a>
            </div>
            <ul class="todo-list mt-20">
                <li class="py-15 px-5 by-1">
                    <!-- checkbox -->
                    <input type="checkbox" id="basic_checkbox_1" class="filled-in">
                    <label for="basic_checkbox_1" class="mb-0 h-15"></label>
                    <!-- todo text -->
                    <span class="text-line">Nulla vitae purus</span>
                    <!-- Emphasis label -->
                    <small class="badge bg-danger"><i class="fa fa-clock-o"></i> 2 mins</small>
                    <!-- General tools such as edit or delete-->
                    <div class="tools">
                        <i class="fa fa-edit"></i>
                        <i class="fa fa-trash-o"></i>
                    </div>
                </li>
                <li class="py-15 px-5">
                    <!-- checkbox -->
                    <input type="checkbox" id="basic_checkbox_2" class="filled-in">
                    <label for="basic_checkbox_2" class="mb-0 h-15"></label>
                    <span class="text-line">Phasellus interdum</span>
                    <small class="badge bg-info"><i class="fa fa-clock-o"></i> 4 hours</small>
                    <div class="tools">
                        <i class="fa fa-edit"></i>
                        <i class="fa fa-trash-o"></i>
                    </div>
                </li>
                <li class="py-15 px-5 by-1">
                    <!-- checkbox -->
                    <input type="checkbox" id="basic_checkbox_3" class="filled-in">
                    <label for="basic_checkbox_3" class="mb-0 h-15"></label>
                    <span class="text-line">Quisque sodales</span>
                    <small class="badge bg-warning"><i class="fa fa-clock-o"></i> 1 day</small>
                    <div class="tools">
                        <i class="fa fa-edit"></i>
                        <i class="fa fa-trash-o"></i>
                    </div>
                </li>
                <li class="py-15 px-5">
                    <!-- checkbox -->
                    <input type="checkbox" id="basic_checkbox_4" class="filled-in">
                    <label for="basic_checkbox_4" class="mb-0 h-15"></label>
                    <span class="text-line">Proin nec mi porta</span>
                    <small class="badge bg-success"><i class="fa fa-clock-o"></i> 3 days</small>
                    <div class="tools">
                        <i class="fa fa-edit"></i>
                        <i class="fa fa-trash-o"></i>
                    </div>
                </li>
                <li class="py-15 px-5 by-1">
                    <!-- checkbox -->
                    <input type="checkbox" id="basic_checkbox_5" class="filled-in">
                    <label for="basic_checkbox_5" class="mb-0 h-15"></label>
                    <span class="text-line">Maecenas scelerisque</span>
                    <small class="badge bg-primary"><i class="fa fa-clock-o"></i> 1 week</small>
                    <div class="tools">
                        <i class="fa fa-edit"></i>
                        <i class="fa fa-trash-o"></i>
                    </div>
                </li>
                <li class="py-15 px-5">
                    <!-- checkbox -->
                    <input type="checkbox" id="basic_checkbox_6" class="filled-in">
                    <label for="basic_checkbox_6" class="mb-0 h-15"></label>
                    <span class="text-line">Vivamus nec orci</span>
                    <small class="badge bg-info"><i class="fa fa-clock-o"></i> 1 month</small>
                    <div class="tools">
                        <i class="fa fa-edit"></i>
                        <i class="fa fa-trash-o"></i>
                    </div>
                </li>
                <li class="py-15 px-5 by-1">
                    <!-- checkbox -->
                    <input type="checkbox" id="basic_checkbox_7" class="filled-in">
                    <label for="basic_checkbox_7" class="mb-0 h-15"></label>
                    <!-- todo text -->
                    <span class="text-line">Nulla vitae purus</span>
                    <!-- Emphasis label -->
                    <small class="badge bg-danger"><i class="fa fa-clock-o"></i> 2 mins</small>
                    <!-- General tools such as edit or delete-->
                    <div class="tools">
                        <i class="fa fa-edit"></i>
                        <i class="fa fa-trash-o"></i>
                    </div>
                </li>
                <li class="py-15 px-5">
                    <!-- checkbox -->
                    <input type="checkbox" id="basic_checkbox_8" class="filled-in">
                    <label for="basic_checkbox_8" class="mb-0 h-15"></label>
                    <span class="text-line">Phasellus interdum</span>
                    <small class="badge bg-info"><i class="fa fa-clock-o"></i> 4 hours</small>
                    <div class="tools">
                        <i class="fa fa-edit"></i>
                        <i class="fa fa-trash-o"></i>
                    </div>
                </li>
                <li class="py-15 px-5 by-1">
                    <!-- checkbox -->
                    <input type="checkbox" id="basic_checkbox_9" class="filled-in">
                    <label for="basic_checkbox_9" class="mb-0 h-15"></label>
                    <span class="text-line">Quisque sodales</span>
                    <small class="badge bg-warning"><i class="fa fa-clock-o"></i> 1 day</small>
                    <div class="tools">
                        <i class="fa fa-edit"></i>
                        <i class="fa fa-trash-o"></i>
                    </div>
                </li>
                <li class="py-15 px-5">
                    <!-- checkbox -->
                    <input type="checkbox" id="basic_checkbox_10" class="filled-in">
                    <label for="basic_checkbox_10" class="mb-0 h-15"></label>
                    <span class="text-line">Proin nec mi porta</span>
                    <small class="badge bg-success"><i class="fa fa-clock-o"></i> 3 days</small>
                    <div class="tools">
                        <i class="fa fa-edit"></i>
                        <i class="fa fa-trash-o"></i>
                    </div>
                </li>
            </ul>
        </div>
        <!-- /.tab-pane -->
    </div>
</aside>
<!-- /.control-sidebar -->

<!-- Add the sidebar's background. This div must be placed immediately after the control sidebar -->
<div class="control-sidebar-bg"></div>
</div>
<!-- ./wrapper -->


<!-- Page Content overlay -->


<!-- Vendor JS -->
<script src="/js/vendors.min.js"></script>
<script src="/js/pages/chat-popup.js"></script>
<script src="/assets/icons/feather-icons/feather.min.js"></script>	<script src="/assets/vendor_components/jquery-steps-master/build/jquery.steps.js"></script>
<script src="/assets/vendor_components/jquery-validation-1.17.0/dist/jquery.validate.min.js"></script>
<script src="/assets/vendor_components/sweetalert/sweetalert.min.js"></script>

<!-- Rhythm Admin App -->
<script src="/js/template.js"></script>
<script src="/js/pages/steps.js"></script>

<script src="/js/pages/advanced-form-element.js"></script>

<style>
    .payment-method-card {
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid #e9ecef;
    }

    .payment-method-card:hover {
        border-color: #1DBFC1;
        box-shadow: 0 4px 8px rgba(0,123,255,0.1);
    }

    .payment-method-card.selected {
        border-color: #1DBFC1;
        background-color: #f8f9ff;
        box-shadow: 0 4px 12px rgba(0,123,255,0.2);
    }

    .payment-form {
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 1.5rem;
        margin-top: 1rem;
        background-color: #f8f9fa;
    }

    .alert-info {
        background-color: #d1ecf1;
        border-color: #bee5eb;
        color: #0c5460;
    }
</style>
<script>
    // Datos de medios de pago se obtendrán del DOM
    const mediosPagoData = {};

    function cargarHorarios() {
        const fecha = document.getElementById('wdate2').value;
        const idEspacio = document.getElementById('idEspacio').value;

        if (!fecha || !idEspacio) return;

        fetch(`/vecino/horarios-disponibles?fecha=${fecha}&idEspacio=${idEspacio}`)
            .then(response => response.json())
            .then(data => {
                const container = document.getElementById('horarios-container');
                container.innerHTML = '';

                data.forEach((horario, idx) => {
                    const btn = document.createElement('button');
                    btn.type = 'button';
                    btn.className = 'waves-effect waves-light btn mb-5 ' +
                        (horario.reservado === 1 ? 'btn-danger-light' : 'btn-primary-light');
                    btn.id = 'btn-horario-' + idx;
                    btn.textContent = horario.horaInicio + ' - ' + horario.horaFin;
                    btn.disabled = horario.reservado === 1;
                    btn.onclick = function () {
                        selectHorario(this, horario.idHorarios);
                    };

                    container.appendChild(btn);

                    // Salto de línea cada 3 botones
                    if ((idx + 1) % 3 === 0) container.appendChild(document.createElement('br'));
                });
            })
            .catch(error => console.error('Error al cargar horarios:', error));
    }

    function selectHorario(button, horarioId) {
        // Quitar clase 'active' de todos los botones
        document.querySelectorAll('#horarios-container .btn').forEach(btn => btn.classList.remove('active'));

        // Marcar el botón clickeado como activo
        button.classList.add('active');

        // Actualizar el valor del input hidden con el id del horario seleccionado
        document.getElementById('horarioSeleccionado').value = horarioId;
    }


    function selectPaymentMethod(cardElement) {
        // Quitar selección anterior
        document.querySelectorAll('.payment-method-card').forEach(card => {
            card.classList.remove('selected');
        });

        // Marcar como seleccionado
        cardElement.classList.add('selected');

        // Obtener datos del elemento
        const medioPagoId = cardElement.getAttribute('data-id');
        const tipoPago = cardElement.getAttribute('data-tipo');

        // Actualizar campo hidden
        document.getElementById('medioPagoSeleccionado').value = medioPagoId;

        // Ocultar todos los formularios de pago
        document.querySelectorAll('.payment-form').forEach(form => {
            form.style.display = 'none';
        });

        // Mostrar formulario correspondiente
        if (tipoPago === 'AUTOMATICO') {
            document.getElementById('formulario-tarjeta').style.display = 'block';
            // Hacer campos requeridos
            setRequiredFields(['numeroTarjeta', 'nombreTarjeta', 'fechaExpiracion', 'cvv'], true);
            setRequiredFields(['numeroTransaccion', 'comprobantes'], false);
        } else {
            document.getElementById('formulario-transferencia').style.display = 'block';
            // Mostrar datos de cuenta
            mostrarDatosCuenta();
            // Hacer campos requeridos
            setRequiredFields(['numeroTransaccion', 'comprobantes'], true);
            setRequiredFields(['numeroTarjeta', 'nombreTarjeta', 'fechaExpiracion', 'cvv'], false);
        }
    }

    function setRequiredFields(fieldIds, required) {
        fieldIds.forEach(id => {
            const field = document.getElementById(id);
            if (field) {
                field.required = required;
            }
        });
    }

    function mostrarDatosCuenta() {
        // Buscar la tarjeta seleccionada para obtener los datos
        const selectedCard = document.querySelector('.payment-method-card.selected');
        if (selectedCard) {
            const datosCuenta = selectedCard.getAttribute('data-datos-cuenta');
            if (datosCuenta && datosCuenta !== 'null' && datosCuenta !== '') {
                const container = document.getElementById('datos-cuenta-container');
                container.innerHTML = `
                    <h6>Datos para transferencia:</h6>
                    <pre>${datosCuenta}</pre>
                    <p><strong>Importante:</strong> Realice la transferencia y suba el comprobante para verificar su pago.</p>
                `;
            }
        }
    }

    // Formatear número de tarjeta
    document.addEventListener('DOMContentLoaded', function() {
        const numeroTarjeta = document.getElementById('numeroTarjeta');
        if (numeroTarjeta) {
            numeroTarjeta.addEventListener('input', function(e) {
                let value = e.target.value.replace(/\s/g, '').replace(/[^0-9]/gi, '');
                let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
                e.target.value = formattedValue;
            });
        }

        const fechaExpiracion = document.getElementById('fechaExpiracion');
        if (fechaExpiracion) {
            fechaExpiracion.addEventListener('input', function(e) {
                let value = e.target.value.replace(/\D/g, '');
                if (value.length >= 2) {
                    value = value.substring(0, 2) + '/' + value.substring(2, 4);
                }
                e.target.value = value;
            });
        }

        const cvv = document.getElementById('cvv');
        if (cvv) {
            cvv.addEventListener('input', function(e) {
                e.target.value = e.target.value.replace(/[^0-9]/g, '');
            });
        }
    });

</script>



</body>
</html>
