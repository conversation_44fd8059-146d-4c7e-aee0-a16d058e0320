<!DOCTYPE html>
<html lang="es">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<meta name="description" content="">
	<meta name="author" content="">
	<link rel="icon" href="/images/san_miguel_logo.ico">

	<title>Espacios Deportivos - Detalles </title>

	<!-- Vendors Style-->
	<link rel="stylesheet" href="/css/vendors_css.css">

	<!-- Style-->
	<link rel="stylesheet" href="/css/style.css">
	<link rel="stylesheet" href="/css/skin_color.css">

</head>

<body class="hold-transition light-skin sidebar-mini theme-success fixed">

<div class="wrapper">
	<div id="loader"></div>

	<header class="main-header">
		<div class="d-flex align-items-center logo-box justify-content-center">
			<!-- Logo -->
			<a href="/static/assets/vendor_components/jquery-validation-1.17.0/demo/requirejs/index.html" class="logo">
				<!-- logo-->
				<div class="logo-mini w-150 text-center">
					<span class="light-logo"><img th:src="@{/images/logo-sanMiguel.png}" alt="logo"></span>
				</div>
			</a>
		</div>
		<!-- Header Navbar -->
		<nav class="navbar navbar-static-top">
			<!-- Sidebar toggle button-->
			<div class="app-menu">
				<ul class="header-megamenu nav">
					<li class="btn-group nav-item">
						<a href="#" class="waves-effect waves-light nav-link push-btn btn-primary-light" data-toggle="push-menu" role="button">
							<i data-feather="align-left"></i>
						</a>
					</li>
				</ul>
			</div>

			<div th:replace="fragments :: navbarFragment"></div>
		</nav>
	</header>

	<!-- Left side column. contains the logo and sidebar -->
	<aside th:replace="fragments :: sidebarFragment"></aside>

	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<div class="container-full">
			<!-- Content Header (Page header) -->

			<!-- Main content -->
			<section class="content">
				<div class="row">
					<div class="col-md-8 col-12">
						<div class="card bg-dark text-white">
							<img class="card-img"
								 th:src="@{|image/${fotoId}|}"
								 onerror="this.onerror=null;this.src='https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRzzuCP3cOsY_IqKeLxrY-KQOCiUabgxyZqHw&s';"
								 alt="Card image">
							<div class="card-img-overlay">
								<h4 class="card-title" th:text="${espacio.getNombre()}"></h4>
							</div>
						</div>
					</div>
					<!-- /.col -->
					<div class="col-md-4 col-12">
						<div class="card box-info">
							<div class="card-header align-content-center">
								<h3 th:text="${espacio.getNombre()}"></h3>
							</div>
						</div>
						<div class="card">
							<div class="card-body">
								<p class="card-text" th:text="${espacio.getDescripcionLarga()}" ></p>
								<p class="card-title" th:text="'Número de Contacto: ' + ${espacio.getNumContacto()}"></p>
								<p class="card-title" th:text="'Correo de Contacto: ' + ${espacio.getCorreoContacto()}"></p>
								<p class="card-title" th:text="'Precio por Horario: S/.' + ${espacio.getCostoHorario()}"></p>
							</div>
						</div>
						<div th:if="${espacio.getTipoEspacio().getId()==1}" class="card">
							<div class="card-body">
								<p class="card-title" th:text="'Tipo Piscina: ' + ${piscina.getTipoPiscina()}"></p>
								<p class="card-title" th:text="'Profundidad Mínima: ' + ${piscina.getProfundidadMin()} + 'm'"></p>
								<p class="card-title" th:text="'Profundidad Máxima: ' + ${piscina.getProfundidadMax()} + 'm'"></p>
								<p class="card-title" th:if="${piscina.isClimatizada()}">Climatizada: Sí</p>
								<p class="card-title" th:unless="${piscina.isClimatizada()}">Climatizada: No</p>
								<p class="card-title" th:text="'Requisitos: ' + ${piscina.getRequisitos()}"></p>
								<p class="card-title" th:text="'Número máximo de carriles: ' + ${piscina.getNumCarrilMax()}"></p>
							</div>
						</div>
						<div th:if="${espacio.getTipoEspacio().getId()==2}" class="card">
							<div class="card-body">
								<p class="card-title" th:text="'Tipo de Superficie: ' + ${canchaFutbol.getTipoSuperficie()}"></p>
								<p class="card-title" th:if="${canchaFutbol.isIlumacionNocturna()}">Iluminación Nocturna: Sí</p>
								<p class="card-title" th:unless="${canchaFutbol.isIlumacionNocturna()}">Iluminación Nocturna: No</p>
								<p class="card-title" th:if="${canchaFutbol.isBalonesDisponibles()}">Balones Disponibles: Sí</p>
								<p class="card-title" th:unless="${canchaFutbol.isBalonesDisponibles()}">Balones Disponibles: No</p>
								<p class="card-title" th:text="'Ancho del Campo: ' + ${canchaFutbol.getAncho()}"></p>
								<p class="card-title" th:text="'Alto del Campo: ' + ${canchaFutbol.getAlto()}"></p>
								<p class="card-title" th:text="'Aforo: ' + ${espacio.getAforo()}"></p>
							</div>
						</div>
						<div th:if="${espacio.getTipoEspacio().getId()==3}" class="card">
							<div class="card-body">
								<p class="card-title" th:text="'Tipo de Superficie: ' + ${pista.getTipoSuperficie()}"></p>
								<p class="card-title" th:text="'Longitud: ' + ${pista.getLongitud()}"></p>
								<p class="card-title" th:text="'Implementos: ' + ${pista.getImplementos()}"></p>
								<p class="card-title" th:text="'Aforo: ' + ${espacio.getAforo()}"></p>
							</div>
						</div>
						<div th:if="${espacio.getTipoEspacio().getId()==4}" class="card">
							<div class="card-body">
								<p class="card-title" th:text="'Aforo: ' + ${espacio.getAforo()}"></p>
								<p class="card-title" th:text="'Uso Permitido: ' + ${estadio.getUsoPermitido()}"></p>
								<p class="card-title" th:if="${estadio.isSeguridadDisponible()}">Seguridad Disponible: Sí</p>
								<p class="card-title" th:unless="${estadio.isSeguridadDisponible()}">Seguridad Disponible: No</p>
								<p class="card-title" th:if="${estadio.isSonidoPantallasDisponible()}">Sonido y Pantallas Disponibles: Sí</p>
								<p class="card-title" th:unless="${estadio.isSonidoPantallasDisponible()}">Sonidos y Pantallas Disponibles: No</p>
								<p class="card-title" th:if="${estadio.isIluminacionProfesionalDisponible()}">Iluminación Profesional Disponible: Sí</p>
								<p class="card-title" th:unless="${estadio.isIluminacionProfesionalDisponible()}">Iluminación Profesional Disponible: No</p>
							</div>
						</div>
						<div th:if="${espacio.getTipoEspacio().getId()==5}" class="card">
							<div class="card-body">
								<p class="card-title" th:text="'Aforo: ' + ${espacio.getAforo()}"></p>
								<p class="card-title" th:text="'Cantidad de Maquinas: ' + ${gimnasio.getCantidadMaquinas()}"></p>
								<p class="card-title" th:text="'Tipos de Maquinas: ' + ${gimnasio.getTiposMaquinas()}"></p>
								<p class="card-title" th:if="${gimnasio.getTieneDuchas()}">Duchas Disponibles: Sí</p>
								<p class="card-title" th:unless="${gimnasio.getTieneDuchas()}">Duchas Disponibles: No</p>
								<p class="card-title" th:if="${gimnasio.getTieneSauna()}">Sauna Disponible: Sí</p>
								<p class="card-title" th:unless="${gimnasio.getTieneSauna()}">Sauna Disponible: No</p>
							</div>
						</div>
						<a th:href="${espacio.getMapsUrl()}" target="_blank" class="waves-effect waves-light btn btn-dark mb-5"><i class="fa fa-map-marker"></i> Ver Ubicacion</a>
						<a th:href="@{/vecino/reservar(idEspacio=${espacio.id}, fecha=${fecha})}" class="waves-effect waves-light btn btn-primary mb-5">Reservar</a>
						<!-- /.card -->
					</div>
					<!-- /.col -->
				</div>
				<!-- /.row -->
				<!-- END Card with image -->

			</section>
			<!-- /.content -->
		</div>
	</div>
	<!-- /.content-wrapper -->

	<footer class="main-footer">
		&copy; <script>document.write(new Date().getFullYear())</script> <a href="https://munisanmiguel.gob.pe/">Municipalidad de San Miguel</a>. Todos los derechos reservados.
	</footer>
	<!-- Control Sidebar -->
	<aside class="control-sidebar">

		<div class="rpanel-title"><span class="pull-right btn btn-circle btn-danger" data-toggle="control-sidebar"><i class="ion ion-close text-white"></i></span> </div>  <!-- Create the tabs -->
		<ul class="nav nav-tabs control-sidebar-tabs">
			<li class="nav-item"><a href="#control-sidebar-home-tab" data-bs-toggle="tab" class="active"><i class="mdi mdi-message-text"></i></a></li>
			<li class="nav-item"><a href="#control-sidebar-settings-tab" data-bs-toggle="tab"><i class="mdi mdi-playlist-check"></i></a></li>
		</ul>
		<!-- Tab panes -->
		<div class="tab-content">
			<!-- Home tab content -->
			<div class="tab-pane active" id="control-sidebar-home-tab">
				<div class="flexbox">
					<a href="javascript:void(0)" class="text-grey">
						<i class="ti-more"></i>
					</a>
					<p>Users</p>
					<a href="javascript:void(0)" class="text-end text-grey"><i class="ti-plus"></i></a>
				</div>
				<div class="lookup lookup-sm lookup-right d-none d-lg-block">
					<input type="text" name="s" placeholder="Search" class="w-p100">
				</div>
				<div class="media-list media-list-hover mt-20">
					<div class="media py-10 px-0">
						<a class="avatar avatar-lg status-success" href="#">
							<img src="/images/avatar/1.jpg" alt="...">
						</a>
						<div class="media-body">
							<p class="fs-16">
								<a class="hover-primary" href="#"><strong>Tyler</strong></a>
							</p>
							<p>Praesent tristique diam...</p>
							<span>Just now</span>
						</div>
					</div>

					<div class="media py-10 px-0">
						<a class="avatar avatar-lg status-danger" href="#">
							<img src="/images/avatar/2.jpg" alt="...">
						</a>
						<div class="media-body">
							<p class="fs-16">
								<a class="hover-primary" href="#"><strong>Luke</strong></a>
							</p>
							<p>Cras tempor diam ...</p>
							<span>33 min ago</span>
						</div>
					</div>

					<div class="media py-10 px-0">
						<a class="avatar avatar-lg status-warning" href="#">
							<img src="/images/avatar/3.jpg" alt="...">
						</a>
						<div class="media-body">
							<p class="fs-16">
								<a class="hover-primary" href="#"><strong>Evan</strong></a>
							</p>
							<p>In posuere tortor vel...</p>
							<span>42 min ago</span>
						</div>
					</div>

					<div class="media py-10 px-0">
						<a class="avatar avatar-lg status-primary" href="#">
							<img src="/images/avatar/4.jpg" alt="...">
						</a>
						<div class="media-body">
							<p class="fs-16">
								<a class="hover-primary" href="#"><strong>Evan</strong></a>
							</p>
							<p>In posuere tortor vel...</p>
							<span>42 min ago</span>
						</div>
					</div>

					<div class="media py-10 px-0">
						<a class="avatar avatar-lg status-success" href="#">
							<img src="/images/avatar/1.jpg" alt="...">
						</a>
						<div class="media-body">
							<p class="fs-16">
								<a class="hover-primary" href="#"><strong>Tyler</strong></a>
							</p>
							<p>Praesent tristique diam...</p>
							<span>Just now</span>
						</div>
					</div>

					<div class="media py-10 px-0">
						<a class="avatar avatar-lg status-danger" href="#">
							<img src="/images/avatar/2.jpg" alt="...">
						</a>
						<div class="media-body">
							<p class="fs-16">
								<a class="hover-primary" href="#"><strong>Luke</strong></a>
							</p>
							<p>Cras tempor diam ...</p>
							<span>33 min ago</span>
						</div>
					</div>

					<div class="media py-10 px-0">
						<a class="avatar avatar-lg status-warning" href="#">
							<img src="/images/avatar/3.jpg" alt="...">
						</a>
						<div class="media-body">
							<p class="fs-16">
								<a class="hover-primary" href="#"><strong>Evan</strong></a>
							</p>
							<p>In posuere tortor vel...</p>
							<span>42 min ago</span>
						</div>
					</div>

					<div class="media py-10 px-0">
						<a class="avatar avatar-lg status-primary" href="#">
							<img src="/images/avatar/4.jpg" alt="...">
						</a>
						<div class="media-body">
							<p class="fs-16">
								<a class="hover-primary" href="#"><strong>Evan</strong></a>
							</p>
							<p>In posuere tortor vel...</p>
							<span>42 min ago</span>
						</div>
					</div>

				</div>

			</div>
			<!-- /.tab-pane -->
			<!-- Settings tab content -->
			<div class="tab-pane" id="control-sidebar-settings-tab">
				<div class="flexbox">
					<a href="javascript:void(0)" class="text-grey">
						<i class="ti-more"></i>
					</a>
					<p>Todo List</p>
					<a href="javascript:void(0)" class="text-end text-grey"><i class="ti-plus"></i></a>
				</div>
				<ul class="todo-list mt-20">
					<li class="py-15 px-5 by-1">
						<!-- checkbox -->
						<input type="checkbox" id="basic_checkbox_1" class="filled-in">
						<label for="basic_checkbox_1" class="mb-0 h-15"></label>
						<!-- todo text -->
						<span class="text-line">Nulla vitae purus</span>
						<!-- Emphasis label -->
						<small class="badge bg-danger"><i class="fa fa-clock-o"></i> 2 mins</small>
						<!-- General tools such as edit or delete-->
						<div class="tools">
							<i class="fa fa-edit"></i>
							<i class="fa fa-trash-o"></i>
						</div>
					</li>
					<li class="py-15 px-5">
						<!-- checkbox -->
						<input type="checkbox" id="basic_checkbox_2" class="filled-in">
						<label for="basic_checkbox_2" class="mb-0 h-15"></label>
						<span class="text-line">Phasellus interdum</span>
						<small class="badge bg-info"><i class="fa fa-clock-o"></i> 4 hours</small>
						<div class="tools">
							<i class="fa fa-edit"></i>
							<i class="fa fa-trash-o"></i>
						</div>
					</li>
					<li class="py-15 px-5 by-1">
						<!-- checkbox -->
						<input type="checkbox" id="basic_checkbox_3" class="filled-in">
						<label for="basic_checkbox_3" class="mb-0 h-15"></label>
						<span class="text-line">Quisque sodales</span>
						<small class="badge bg-warning"><i class="fa fa-clock-o"></i> 1 day</small>
						<div class="tools">
							<i class="fa fa-edit"></i>
							<i class="fa fa-trash-o"></i>
						</div>
					</li>
					<li class="py-15 px-5">
						<!-- checkbox -->
						<input type="checkbox" id="basic_checkbox_4" class="filled-in">
						<label for="basic_checkbox_4" class="mb-0 h-15"></label>
						<span class="text-line">Proin nec mi porta</span>
						<small class="badge bg-success"><i class="fa fa-clock-o"></i> 3 days</small>
						<div class="tools">
							<i class="fa fa-edit"></i>
							<i class="fa fa-trash-o"></i>
						</div>
					</li>
					<li class="py-15 px-5 by-1">
						<!-- checkbox -->
						<input type="checkbox" id="basic_checkbox_5" class="filled-in">
						<label for="basic_checkbox_5" class="mb-0 h-15"></label>
						<span class="text-line">Maecenas scelerisque</span>
						<small class="badge bg-primary"><i class="fa fa-clock-o"></i> 1 week</small>
						<div class="tools">
							<i class="fa fa-edit"></i>
							<i class="fa fa-trash-o"></i>
						</div>
					</li>
					<li class="py-15 px-5">
						<!-- checkbox -->
						<input type="checkbox" id="basic_checkbox_6" class="filled-in">
						<label for="basic_checkbox_6" class="mb-0 h-15"></label>
						<span class="text-line">Vivamus nec orci</span>
						<small class="badge bg-info"><i class="fa fa-clock-o"></i> 1 month</small>
						<div class="tools">
							<i class="fa fa-edit"></i>
							<i class="fa fa-trash-o"></i>
						</div>
					</li>
					<li class="py-15 px-5 by-1">
						<!-- checkbox -->
						<input type="checkbox" id="basic_checkbox_7" class="filled-in">
						<label for="basic_checkbox_7" class="mb-0 h-15"></label>
						<!-- todo text -->
						<span class="text-line">Nulla vitae purus</span>
						<!-- Emphasis label -->
						<small class="badge bg-danger"><i class="fa fa-clock-o"></i> 2 mins</small>
						<!-- General tools such as edit or delete-->
						<div class="tools">
							<i class="fa fa-edit"></i>
							<i class="fa fa-trash-o"></i>
						</div>
					</li>
					<li class="py-15 px-5">
						<!-- checkbox -->
						<input type="checkbox" id="basic_checkbox_8" class="filled-in">
						<label for="basic_checkbox_8" class="mb-0 h-15"></label>
						<span class="text-line">Phasellus interdum</span>
						<small class="badge bg-info"><i class="fa fa-clock-o"></i> 4 hours</small>
						<div class="tools">
							<i class="fa fa-edit"></i>
							<i class="fa fa-trash-o"></i>
						</div>
					</li>
					<li class="py-15 px-5 by-1">
						<!-- checkbox -->
						<input type="checkbox" id="basic_checkbox_9" class="filled-in">
						<label for="basic_checkbox_9" class="mb-0 h-15"></label>
						<span class="text-line">Quisque sodales</span>
						<small class="badge bg-warning"><i class="fa fa-clock-o"></i> 1 day</small>
						<div class="tools">
							<i class="fa fa-edit"></i>
							<i class="fa fa-trash-o"></i>
						</div>
					</li>
					<li class="py-15 px-5">
						<!-- checkbox -->
						<input type="checkbox" id="basic_checkbox_10" class="filled-in">
						<label for="basic_checkbox_10" class="mb-0 h-15"></label>
						<span class="text-line">Proin nec mi porta</span>
						<small class="badge bg-success"><i class="fa fa-clock-o"></i> 3 days</small>
						<div class="tools">
							<i class="fa fa-edit"></i>
							<i class="fa fa-trash-o"></i>
						</div>
					</li>
				</ul>
			</div>
			<!-- /.tab-pane -->
		</div>
	</aside>
	<!-- /.control-sidebar -->

	<!-- Add the sidebar's background. This div must be placed immediately after the control sidebar -->
	<div class="control-sidebar-bg"></div>
</div>
<!-- ./wrapper -->

<!-- Page Content overlay -->


<!-- Vendor JS -->
<script src="/js/vendors.min.js"></script>
<script src="/js/pages/chat-popup.js"></script>
<script src="/assets/icons/feather-icons/feather.min.js"></script>	<script src="/assets/vendor_components/Magnific-Popup-master/dist/jquery.magnific-popup.min.js"></script>
<script src="/assets/vendor_components/Magnific-Popup-master/dist/jquery.magnific-popup-init.js"></script>

<!-- Rhythm Admin App -->
<script src="/js/template.js"></script>



</body>
</html>