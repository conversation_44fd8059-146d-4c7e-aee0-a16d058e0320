/* Alineamos el botón al lado del buscador */
#generate-user-btn {
  margin-top: 20px;
  margin-left: 20px; /* A<PERSON>de espacio a la izquierda del botón */
  padding: 8px 20px; /* Ajusta el tamaño del botón */
  font-size: 14px; /* Controlamos el tamaño del texto */
  border-radius: 5px; /* Esquinas redondeadas */
}

/* Aseguramos que todo se vea alineado y bien organizado */
.app-menu .header-megamenu {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.app-menu .header-megamenu li {
  list-style: none;
  margin-right: 10px;
}

#generate-user-btn:hover {
  background-color: #28a745; /* Cambiar color al pasar el ratón */
  color: white;
}

/* formulario-estilos.css */

/* Ajustar el ancho de los campos para que ocupen todo el espacio disponible */
/* Solo afecta a los elementos dentro de la clase .custom-form-container */
/* Asegurarse de que los campos ocupen todo el espacio disponible */
/* Ajustar el espaciado entre los campos */


/* Aumenta el ancho solo del formulario del coordinador */
.coordinator-form{
  width: 210%;  /* Ajusta al tamaño deseado */
}

.coordinator-form .box {
  width: 100%;
}
.coordinator-form .box-body {
  width: 100%;
  padding: 20px;
}

.coordinator-form .form-control {
  width: 95%;
  height: 60px; /* Ajusta la altura del input */
  font-size: 18px; /* Ajusta el tamaño del texto dentro del input */
  padding: 10px; /* Asegura que haya espacio entre el borde y el texto */
}

/* Si quieres ajustar específicamente el texto de los placeholders */
.coordinator-form .form-control::placeholder {
  font-size: 18px; /* Ajusta el tamaño del texto en los placeholders */
}

.coordinator-form .form-control[type="email"] {
  width: 97.6%; /* Asegura que el campo de correo ocupe todo el ancho disponible */
  max-width: 100%; /* Asegura que el campo de correo no se haga más grande de lo necesario */
  height: 60px; /* Ajusta la altura del input */
  font-size: 18px; /* Ajusta el tamaño del texto dentro del input */
  padding: 10px; /* Asegura que haya espacio entre el borde y el texto */
  box-sizing: border-box; /* Asegura que el padding no afecte al tamaño total del campo */
  margin-bottom: 20px; /* Añade un poco de espacio debajo del campo */
}