<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en" dir="ltr" id="html">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<title>Sizzle Test Suite</title>
	<link rel="Stylesheet" media="screen" href="libs/qunit/qunit.css" />
	<script type="text/javascript" src="libs/qunit/qunit.js"></script>
	<script type="text/javascript" src="data/testinit.js"></script>
	<script type="text/javascript" src="jquery.js"></script>
	<script type="text/javascript" src="../dist/sizzle.js"></script>
	<script type="text/javascript" src="unit/selector.js"></script>
	<script type="text/javascript" src="unit/utilities.js"></script>
	<script type="text/javascript" src="unit/extending.js"></script>
</head>

<body id="body">
	<div id="qunit"></div>

	<!-- Test HTML -->
	<dl id="dl" style="position:absolute;top:-32767px;left:-32767px;width:1px">
	<div id="qunit-fixture">
		<p id="firstp">See <a id="simon1" href="http://simon.incutio.com/archive/2003/03/25/#getElementsBySelector" rel="bookmark">this blog entry</a> for more information.</p>
		<p id="ap">
			Here are some [links] in a normal paragraph: <a id="google" href="http://www.google.com/" title="Google!">Google</a>,
			<a id="groups" href="http://groups.google.com/" class="GROUPS">Google Groups (Link)</a>.
			This link has <code id="code1"><a href="http://smin" id="anchor1">class="blog"</a></code>:
			<a href="http://diveintomark.org/" class="blog" hreflang="en" id="mark">diveintomark</a>

		</p>
		<div id="foo">
			<p id="sndp">Everything inside the red border is inside a div with <code>id="foo"</code>.</p>
			<p lang="en" id="en">This is a normal link: <a id="yahoo" href="http://www.yahoo.com/" class="blogTest">Yahoo</a></p>
			<p id="sap">This link has <code><a href="#2" id="anchor2">class="blog"</a></code>: <a href="http://simon.incutio.com/" class="blog link" id="simon">Simon Willison's Weblog</a></p>

		</div>
		<div id="nothiddendiv" style="height:1px;background:white;" class="nothiddendiv">
			<div id="nothiddendivchild"></div>
		</div>
		<span id="name+value"></span>
		<p id="first">Try them out:</p>
		<ul id="firstUL"></ul>
		<ol id="empty"><!-- comment --></ol>
		<form id="form" action="formaction">
			<label for="action" id="label-for">Action:</label>
			<input type="text" name="action" value="Test" id="text1" maxlength="30"/>
			<input type="text" name="text2" value="Test" id="text2" disabled="disabled"/>
			<input type="radio" name="radio1" id="radio1" value="on"/>

			<input type="radio" name="radio2" id="radio2" checked="checked"/>
			<input type="checkbox" name="check" id="check1" checked="checked"/>
			<input type="checkbox" id="check2" value="on"/>

			<input type="hidden" name="hidden" id="hidden1"/>
			<input type="text" style="display:none;" name="foo[bar]" id="hidden2"/>

			<input type="text" id="name" name="name" value="name" />
			<input type="search" id="search" name="search" value="search" />

			<button id="button" name="button" type="button">Button</button>

			<textarea id="area1" maxlength="30">foobar</textarea>

			<select name="select1" id="select1">
				<option id="option1a" class="emptyopt" value="">Nothing</option>
				<option id="option1b" value="1">1</option>
				<option id="option1c" value="2">2</option>
				<option id="option1d" value="3">3</option>
			</select>
			<select name="select2" id="select2">
				<option id="option2a" class="emptyopt" value="">Nothing</option>
				<option id="option2b" value="1">1</option>
				<option id="option2c" value="2">2</option>
				<option id="option2d" selected="selected" value="3">3</option>
			</select>
			<select name="select3" id="select3" multiple="multiple">
				<option id="option3a" class="emptyopt" value="">Nothing</option>
				<option id="option3b" selected="selected" value="1">1</option>
				<option id="option3c" selected="selected" value="2">2</option>
				<option id="option3d" value="3">3</option>
				<option id="option3e">no value</option>
			</select>
			<select name="select4" id="select4" multiple="multiple">
				<optgroup disabled="disabled">
					<option id="option4a" class="emptyopt" value="">Nothing</option>
					<option id="option4b" disabled="disabled" selected="selected" value="1">1</option>
					<option id="option4c" selected="selected" value="2">2</option>
				</optgroup>
				<option selected="selected" disabled="disabled" id="option4d" value="3">3</option>
				<option id="option4e">no value</option>
			</select>
			<select name="select5" id="select5">
				<option id="option5a" value="3">1</option>
				<option id="option5b" value="2">2</option>
				<option id="option5c" value="1">3</option>
			</select>

			<object id="object1" codebase="stupid">
				<param name="p1" value="x1" />
				<param name="p2" value="x2" />
			</object>

			<span id="台北Táiběi"></span>
			<span id="台北" lang="中文"></span>
			<span id="utf8class1" class="台北Táiběi 台北"></span>
			<span id="utf8class2" class="台北"></span>
			<span id="foo:bar" class="foo:bar"><span id="foo_descendent"></span></span>
			<span id="test.foo[5]bar" class="test.foo[5]bar"></span>

			<foo_bar id="foobar">test element</foo_bar>
		</form>
		<b id="floatTest">Float test.</b>
		<iframe id="iframe" name="iframe"></iframe>
		<form id="lengthtest">
			<input type="text" id="length" name="test"/>
			<input type="text" id="idTest" name="id"/>
		</form>
		<table id="table"></table>

		<form id="name-tests">
			<!-- Inputs with a grouped name attribute. -->
			<input name="types[]" id="types_all" type="checkbox" value="all" />
			<input name="types[]" id="types_anime" type="checkbox" value="anime" />
			<input name="types[]" id="types_movie" type="checkbox" value="movie" />
		</form>

		<form id="testForm" action="#" method="get">
			<textarea name="T3" rows="2" cols="15">?
Z</textarea>
			<input type="hidden" name="H1" value="x" />
			<input type="hidden" name="H2" />
			<input name="PWD" type="password" value="" />
			<input name="T1" type="text" />
			<input name="T2" type="text" value="YES" readonly="readonly" />
			<input type="checkbox" name="C1" value="1" />
			<input type="checkbox" name="C2" />
			<input type="radio" name="R1" value="1" />
			<input type="radio" name="R1" value="2" />
			<input type="text" name="My Name" value="me" />
			<input type="reset" name="reset" value="NO" />
			<select name="S1">
				<option value="abc">ABC</option>
				<option value="abc">ABC</option>
				<option value="abc">ABC</option>
			</select>
			<select name="S2" multiple="multiple" size="3">
				<option value="abc">ABC</option>
				<option value="abc">ABC</option>
				<option value="abc">ABC</option>
			</select>
			<select name="S3">
				<option selected="selected">YES</option>
			</select>
			<select name="S4">
				<option value="" selected="selected">NO</option>
			</select>
			<input type="submit" name="sub1" value="NO" />
			<input type="submit" name="sub2" value="NO" />
			<input type="image" name="sub3" value="NO" />
			<button name="sub4" type="submit" value="NO">NO</button>
			<input name="D1" type="text" value="NO" disabled="disabled" />
			<input type="checkbox" checked="checked" disabled="disabled" name="D2" value="NO" />
			<input type="radio" name="D3" value="NO" checked="checked" disabled="disabled" />
			<select name="D4" disabled="disabled">
				<option selected="selected" value="NO">NO</option>
			</select>
			<input id="list-test" type="text" />
			<datalist id="datalist">
				<option value="option"></option>
			</datalist>
		</form>
		<div id="moretests">
			<form>
				<div id="checkedtest" style="display:none;">
					<input type="radio" name="checkedtestradios" checked="checked"/>
					<input type="radio" name="checkedtestradios" value="on"/>
					<input type="checkbox" name="checkedtestcheckboxes" checked="checked"/>
					<input type="checkbox" name="checkedtestcheckboxes" />
				</div>
			</form>
			<div id="nonnodes"><span>hi</span> there <!-- mon ami --></div>
			<div id="t2037">
				<div><div class="hidden">hidden</div></div>
			</div>
			<div id="t6652">
				<div></div>
			</div>
			<div id="t12087">
				<input type="hidden" id="el12087" data-comma="0,1"/>
			</div>
			<div id="no-clone-exception"><object><embed></embed></object></div>
			<div id="names-group">
				<span id="name-is-example" name="example"></span>
				<span id="name-is-div" name="div"></span>
			</div>
			<script id="script-no-src"></script>
			<script id="script-src" src="data/empty.js"></script>
			<div id="id-name-tests">
				<a id="tName1ID" name="tName1"><span></span></a>
				<a id="tName2ID" name="tName2"><span></span></a>
				<div id="tName1"><span id="tName1-span">C</span></div>
			</div>
		</div>

		<div id="tabindex-tests">
			<ol id="listWithTabIndex" tabindex="5">
				<li id="foodWithNegativeTabIndex" tabindex="-1">Rice</li>
				<li id="foodNoTabIndex">Beans</li>
				<li>Blinis</li>
				<li>Tofu</li>
			</ol>

			<div id="divWithNoTabIndex">I'm hungry. I should...</div>
			<span>...</span><a href="#" id="linkWithNoTabIndex">Eat lots of food</a><span>...</span> |
			<span>...</span><a href="#" id="linkWithTabIndex" tabindex="2">Eat a little food</a><span>...</span> |
			<span>...</span><a href="#" id="linkWithNegativeTabIndex" tabindex="-1">Eat no food</a><span>...</span>
			<span>...</span><a id="linkWithNoHrefWithNoTabIndex">Eat a burger</a><span>...</span>
			<span>...</span><a id="linkWithNoHrefWithTabIndex" tabindex="1">Eat some funyuns</a><span>...</span>
			<span>...</span><a id="linkWithNoHrefWithNegativeTabIndex" tabindex="-1">Eat some funyuns</a><span>...</span>
		</div>

		<div id="liveHandlerOrder">
			<span id="liveSpan1"><a href="#" id="liveLink1"></a></span>
			<span id="liveSpan2"><a href="#" id="liveLink2"></a></span>
		</div>

		<div id="siblingTest">
			<em id="siblingfirst">1</em>
			<em id="siblingnext">2</em>
			<em id="siblingthird">
				<em id="siblingchild">
					<em id="siblinggrandchild">
						<em id="siblinggreatgrandchild"></em>
					</em>
				</em>
			</em>
			<span id="siblingspan"></span>
		</div>​
	</div>
	</dl>
	<br id="last"/>
</body>
</html>
