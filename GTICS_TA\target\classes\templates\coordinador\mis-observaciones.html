<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="es">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="">
  <meta name="author" content="">
  <link rel="icon" th:href="@{/images/logo-solo.png}">

  <title>Mis Observaciones - Coordinador</title>

  <!-- Vendors Style-->
  <link rel="stylesheet" th:href="@{/css/vendors_css.css}" >

  <!-- Font Awesome -->
  <link rel="stylesheet" th:href="@{/assets/icons/font-awesome/css/font-awesome.min.css}">

  <!-- Style-->
  <link rel="stylesheet" th:href="@{/css/style.css}">
  <link rel="stylesheet" th:href="@{/css/skin_color.css}">
  
  <style>
    .observacion-card {
      border-left: 4px solid;
      margin-bottom: 15px;
      transition: transform 0.2s;
    }
    .observacion-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .observacion-reparacion {
      border-left-color: #dc3545;
    }
    .observacion-general {
      border-left-color: #007bff;
    }
    .fecha-observacion {
      font-size: 0.85em;
      color: #6c757d;
    }
    .tipo-badge {
      font-size: 0.75em;
      padding: 0.25em 0.5em;
    }
  </style>
</head>

<body class="hold-transition light-skin sidebar-mini theme-success fixed">

<div class="wrapper">
  <div id="loader"></div>

  <header class="main-header">
    <div class="d-flex align-items-center logo-box justify-content-center">
      <!-- Logo -->
      <a th:href="@{/coordinador/principal}" class="logo">
        <!-- logo-->
        <div class="logo-mini w-150 text-center">
          <span class="light-logo"><img th:src="@{/images/logo-sanMiguel.png}" alt="logo"></span>
        </div>
      </a>
    </div>
    <!-- Header Navbar -->
    <nav class="navbar navbar-static-top">
      <!-- Sidebar toggle button-->
      <div class="app-menu">
        <ul class="header-megamenu nav">
          <li class="btn-group nav-item">
            <a href="#" class="waves-effect waves-light nav-link push-btn btn-primary-light" data-toggle="push-menu" role="button">
              <i data-feather="align-left"></i>
            </a>
          </li>
        </ul>
      </div>

      <div th:replace="fragments :: navbarCoordinador"></div>
    </nav>
  </header>

  <!-- Left side column. contains the logo and sidebar -->
  <aside th:replace="fragments :: sideBarCoordinador"></aside>

  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <div class="container-full">
      <!-- Content Header (Page header) -->
      <div class="content-header">
        <div class="d-flex align-items-center">
          <div class="me-auto">
            <h4 class="page-title">Mis Observaciones</h4>
            <div class="d-inline-block align-items-center">
              <nav>
                <ol class="breadcrumb">
                  <li class="breadcrumb-item"><a href="#"><i class="mdi mdi-home-outline"></i></a></li>
                  <li class="breadcrumb-item"><a th:href="@{/coordinador/principal}">Menú</a></li>
                  <li class="breadcrumb-item active" aria-current="page">Observaciones</li>
                </ol>
              </nav>
            </div>
          </div>
          <div class="text-end">
            <a th:href="@{/coordinador/principal}" class="btn btn-primary">
              <i class="fa fa-plus me-2"></i>Nueva Observación
            </a>
          </div>
        </div>
      </div>

      <!-- Main content -->
      <section class="content">
        <div class="row">
          <div class="col-12">
            <div class="box">
              <div class="box-header with-border">
                <h4 class="box-title">
                  <i class="fa fa-list me-2"></i>Historial de Observaciones
                </h4>
                <div class="box-tools pull-right">
                  <span class="badge badge-info">Total: <span th:text="${#lists.size(comentarios)}">0</span></span>
                </div>
              </div>
              <!-- /.box-header -->
              <div class="box-body">
                
                <!-- Mensaje si no hay observaciones -->
                <div th:if="${#lists.isEmpty(comentarios)}" class="text-center py-5">
                  <i class="fa fa-clipboard fa-4x text-muted mb-3"></i>
                  <h5 class="text-muted">No has registrado observaciones aún</h5>
                  <p class="text-muted">Cuando registres observaciones o reportes de mantenimiento, aparecerán aquí.</p>
                  <a th:href="@{/coordinador/principal}" class="btn btn-primary">
                    <i class="fa fa-plus me-2"></i>Registrar Primera Observación
                  </a>
                </div>

                <!-- Lista de observaciones -->
                <div th:if="${!#lists.isEmpty(comentarios)}">
                  <div th:each="comentario : ${comentarios}"
                       th:class="'card observacion-card ' + (${comentario.tipoComentario.name() == 'REPARACION'} ? 'observacion-reparacion' : 'observacion-general')">
                    <div class="card-body">
                      <div class="d-flex justify-content-between align-items-start mb-2">
                        <div class="flex-grow-1">
                          <h6 class="card-title mb-1">
                            <i th:class="${comentario.tipoComentario.name() == 'REPARACION'} ? 'fa fa-wrench text-danger' : 'fa fa-comment text-primary'" class="me-2"></i>
                            <span th:text="${comentario.espacio.nombre}">Espacio Deportivo</span>
                          </h6>
                          <small class="fecha-observacion">
                            <i class="fa fa-calendar me-1"></i>
                            <span th:text="${#temporals.format(comentario.fechaCreacion, 'dd/MM/yyyy HH:mm')}">Fecha</span>
                          </small>
                        </div>
                        <div>
                          <span th:if="${comentario.tipoComentario.name() == 'REPARACION'}"
                                class="badge bg-danger tipo-badge">
                            🔧 Mantenimiento
                          </span>
                          <span th:if="${comentario.tipoComentario.name() == 'COMENTARIO'}"
                                class="badge bg-primary tipo-badge">
                            📝 Observación
                          </span>
                        </div>
                      </div>
                      
                      <div class="card-text">
                        <p class="mb-2" th:text="${comentario.contenido}">Contenido de la observación</p>

                        <!-- Mostrar imágenes si existen -->
                        <div th:if="${comentario.listaFotos != null and !#lists.isEmpty(comentario.listaFotos.fotos)}" class="mb-3">
                          <h6 class="text-muted mb-2">
                            <i class="fa fa-image me-1"></i>Imágenes adjuntas:
                          </h6>
                          <div class="row">
                            <div th:each="foto : ${comentario.listaFotos.fotos}" class="col-md-3 col-sm-4 col-6 mb-2">
                              <div class="position-relative">
                                <img th:src="${foto.fotoUrl}"
                                     class="img-thumbnail observacion-imagen"
                                     style="width: 100%; height: 120px; object-fit: cover; cursor: pointer;"
                                     th:alt="'Imagen de observación'"
                                     onclick="mostrarImagenCompleta(this.src)">
                                <small class="text-muted d-block text-center mt-1"
                                       th:text="${foto.fotoNombre}">Nombre imagen</small>
                              </div>
                            </div>
                          </div>
                        </div>

                        <small class="text-muted">
                          <i class="fa fa-map-marker me-1"></i>
                          Tipo: <span th:text="${comentario.espacio.tipoEspacio.nombre}">Tipo de espacio</span>
                        </small>
                      </div>
                    </div>
                  </div>
                </div>

              </div>
              <!-- /.box-body -->
            </div>
            <!-- /.box -->
          </div>
        </div>
      </section>
      <!-- /.content -->
    </div>
  </div>
  <!-- /.content-wrapper -->

  <footer class="main-footer">
    &copy; <script>document.write(new Date().getFullYear())</script> <a>New Fibra</a>. All Rights Reserved.
  </footer>

</div>

<!-- Modal para mostrar imagen completa -->
<div class="modal fade" id="imagenModal" tabindex="-1" aria-labelledby="imagenModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="imagenModalLabel">
          <i class="fa fa-image me-2"></i>Imagen de observación
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body text-center">
        <img id="imagenCompleta" src="" class="img-fluid" alt="Imagen completa">
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          <i class="fa fa-times me-1"></i>Cerrar
        </button>
        <a id="descargarImagen" href="" download class="btn btn-primary">
          <i class="fa fa-download me-1"></i>Descargar
        </a>
      </div>
    </div>
  </div>
</div>

<!-- Vendor JS -->
<script th:src="@{/js/vendors.min.js}"></script>
<script th:src="@{/js/pages/chat-popup.js}"></script>
<script th:src="@{/assets/icons/feather-icons/feather.min.js}"></script>

<!-- Rhythm Admin App -->
<script th:src="@{/js/template.js}"></script>

<script>
// Función para mostrar imagen en modal
function mostrarImagenCompleta(src) {
    const modal = new bootstrap.Modal(document.getElementById('imagenModal'));
    const imagenCompleta = document.getElementById('imagenCompleta');
    const descargarImagen = document.getElementById('descargarImagen');

    imagenCompleta.src = src;
    descargarImagen.href = src;

    modal.show();
}

// Agregar efecto hover a las imágenes
document.addEventListener('DOMContentLoaded', function() {
    const imagenes = document.querySelectorAll('.observacion-imagen');
    imagenes.forEach(img => {
        img.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05)';
            this.style.transition = 'transform 0.2s ease';
        });

        img.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });
});
</script>

</body>
</html>
