@import url("https://fonts.googleapis.com/css?family=IBM+Plex+Sans:100,100i,200,200i,300,300i,400,400i,500,500i,600,600i,700,700i|Rubik:300,300i,400,400i,500,500i,700,700i,900,900i&display=swap");
/*Social Media Colors*/
/*Theme Colors*/
/*Lite color*/
/*Theme Colors For Dark*/
/* gray bg */
.bg-gray-100 {
  background-color: #f3f6f9 !important; }

.bg-gray-200 {
  background-color: #ebedf3 !important; }

.bg-gray-300 {
  background-color: #e4e6ef !important; }

.bg-gray-400 {
  background-color: #d1d3e0 !important; }

.bg-gray-500 {
  background-color: #b5b5c3 !important; }

.bg-gray-600 {
  background-color: #7e8299 !important; }

.bg-gray-700 {
  background-color: #5e6278 !important; }

.bg-gray-800 {
  background-color: #3f4254 !important; }

.bg-gray-900 {
  background-color: #181c32 !important; }

/*---Default <PERSON><PERSON>---*/
.btn-default {
  background-color: #f2f2f2;
  border-color: #dedede;
  color: #172b4c; }
  .btn-default:hover, .btn-default:active, .btn-default:focus, .btn-default.active {
    background-color: #e6e6e6 !important;
    border-color: #cccccc !important;
    color: #172b4c; }
  .btn-default:disabled {
    background-color: #f2f2f2;
    border-color: #dedede;
    opacity: 0.5; }
  .btn-default.disabled {
    background-color: #f2f2f2;
    border-color: #dedede;
    opacity: 0.5; }

.show > .btn-default.dropdown-toggle {
  background-color: #e6e6e6 !important;
  border-color: #cccccc !important;
  color: #172b4c; }

.btn-outline.btn-default {
  color: #172b4c;
  background-color: transparent;
  border-color: #cccccc !important; }
  .btn-outline.btn-default:hover, .btn-outline.btn-default:active, .btn-outline.btn-default.active {
    background-color: #e6e6e6 !important;
    border-color: #cccccc !important;
    color: #172b4c; }

.show > .btn-outline.btn-default.dropdown-toggle {
  background-color: #e6e6e6 !important;
  border-color: #cccccc !important;
  color: #172b4c; }

.btn-flat.btn-default {
  color: #172b4c;
  background-color: #e4e6ef;
  border-color: transparent; }
  .btn-flat.btn-default:hover, .btn-flat.btn-default:active, .btn-flat.btn-default.active {
    background-color: #e6e6e6 !important;
    border-color: #cccccc !important;
    color: #172b4c; }

/*---Secondary Button---*/
.btn-secondary {
  background-color: #e4e6ef;
  border-color: #e4e6ef;
  color: #172b4c; }
  .btn-secondary:hover, .btn-secondary:active, .btn-secondary:focus, .btn-secondary.active {
    background-color: #c4c8dc !important;
    border-color: #c4c8dc !important;
    color: #172b4c !important; }
  .btn-secondary:disabled {
    background-color: #747ead;
    border-color: #747ead;
    opacity: 0.5; }
  .btn-secondary.disabled {
    background-color: #747ead;
    border-color: #747ead;
    opacity: 0.5; }

.show > .btn-secondary.dropdown-toggle {
  background-color: #e4e6ef !important;
  border-color: #e4e6ef !important;
  color: #ffffff; }

.btn-outline.btn-secondary {
  color: #172b4c !important;
  background-color: transparent;
  border-color: #e4e6ef !important; }
  .btn-outline.btn-secondary:hover, .btn-outline.btn-secondary:active, .btn-outline.btn-secondary.active {
    background-color: #c4c8dc !important;
    border-color: #c4c8dc !important;
    color: #172b4c !important; }

.show > .btn-outline.btn-secondary.dropdown-toggle {
  background-color: #e4e6ef !important;
  border-color: #e4e6ef !important;
  color: #ffffff; }

.btn-flat.btn-secondary {
  color: #172b4c;
  background-color: #e4e6ef;
  border-color: transparent; }
  .btn-flat.btn-secondary:hover, .btn-flat.btn-secondary:active, .btn-flat.btn-secondary.active {
    background-color: #c4c8dc !important;
    border-color: #c4c8dc !important;
    color: #172b4c !important; }

.btn-outline {
  color: #172b4c;
  background-color: transparent;
  border-color: #234173; }
  .btn-outline:hover, .btn-outline:active, .btn-outline.active {
    background-color: #3246D3;
    border-color: #234173;
    color: #ffffff !important; }

.show > .btn-outline.dropdown-toggle {
  background-color: #234173;
  border-color: #234173;
  color: #ffffff; }

/*---Dark Button---*/
.btn-dark {
  background-color: #172b4c;
  border-color: #172b4c;
  color: #ffffff; }
  .btn-dark:hover, .btn-dark:active, .btn-dark:focus, .btn-dark.active {
    background-color: #234173 !important;
    border-color: #234173 !important;
    color: #ffffff; }
  .btn-dark:disabled {
    background-color: #234173;
    border-color: #234173;
    opacity: 0.5; }
  .btn-dark.disabled {
    background-color: #234173;
    border-color: #234173;
    opacity: 0.5; }

.show > .btn-dark.dropdown-toggle {
  background-color: #234173 !important;
  border-color: #234173 !important;
  color: #ffffff; }

.btn-outline.btn-dark {
  color: #172b4c;
  background-color: transparent;
  border-color: #172b4c !important; }
  .btn-outline.btn-dark:hover, .btn-outline.btn-dark:active, .btn-outline.btn-dark.active {
    background-color: #234173 !important;
    border-color: #234173 !important;
    color: #ffffff; }

.show > .btn-outline.btn-dark.dropdown-toggle {
  background-color: #234173 !important;
  border-color: #234173 !important;
  color: #ffffff; }

.btn-flat.btn-dark {
  color: #172b4c;
  background-color: transparent;
  border-color: transparent; }
  .btn-flat.btn-dark:hover, .btn-flat.btn-dark:active, .btn-flat.btn-dark.active {
    background-color: #234173 !important;
    border-color: #234173 !important;
    color: #ffffff; }

/*---Light Button---*/
.btn-light {
  background-color: #e4e6ef;
  border-color: #e4e6ef;
  color: #000000; }
  .btn-light:hover, .btn-light:active, .btn-light:focus, .btn-light.active {
    background-color: #ffffff !important;
    border-color: #e4e6ef !important;
    color: #000000; }
  .btn-light:disabled {
    background-color: #ffffff;
    border-color: #e4e6ef;
    opacity: 0.5; }
  .btn-light.disabled {
    background-color: #ffffff;
    border-color: white;
    opacity: 0.5; }

.show > .btn-light.dropdown-toggle {
  background-color: #ffffff !important;
  border-color: #e4e6ef !important;
  color: #000000; }

.btn-outline.btn-light {
  color: #e4e6ef;
  background-color: transparent;
  border-color: #e4e6ef !important; }
  .btn-outline.btn-light:hover, .btn-outline.btn-light:active, .btn-outline.btn-light.active {
    background-color: #e4e6ef !important;
    border-color: white !important;
    color: #172b4c !important; }

.show > .btn-outline.btn-light.dropdown-toggle {
  background-color: #e4e6ef !important;
  border-color: #e4e6ef !important;
  color: #ffffff; }

.btn-flat.btn-light {
  color: #172b4c;
  background-color: transparent;
  border-color: #e4e6ef; }
  .btn-flat.btn-light:hover, .btn-flat.btn-light:active, .btn-flat.btn-light.active {
    background-color: #e4e6ef !important;
    border-color: #e4e6ef !important;
    color: #172b4c; }

/*---Background---*/
.bg-pink {
  background-color: #ED6C82 !important;
  color: #ffffff !important; }

.text-pink {
  color: #ED6C82 !important; }

.bg-success2 {
  background-color: #41c1c6 !important;
  color: #ffffff !important; }

.bg-success3 {
  background-color: #58c5cd !important;
  color: #ffffff !important; }

.bg-secondary {
  background-color: #e4e6ef !important;
  color: #172b4c; }

.bg-secondary-light {
  background-color: #e9edf2;
  color: #172b4c; }

.bg-dark {
  background-color: #172b4c !important;
  color: #ffffff !important; }

.bg-dark2 {
  background-color: #566f9e !important;
  color: #ffffff !important; }

.bg-dark3 {
  background-color: #0c1a32 !important;
  color: #ffffff !important; }

.bg-white {
  background-color: #ffffff; }

.bg-transparent {
  background-color: transparent !important; }

.bg-light {
  background-color: #e4e6ef !important; }

.bg-lighter {
  background-color: #ebedf3 !important; }

.bg-lightest {
  background-color: #f3f6f9 !important; }

.bg-white-10 {
  background-color: rgba(255, 255, 255, 0.1) !important; }

.bg-white-20 {
  background-color: rgba(255, 255, 255, 0.2) !important; }

.bg-white-30 {
  background-color: rgba(255, 255, 255, 0.3) !important; }

.bg-white-40 {
  background-color: rgba(255, 255, 255, 0.4) !important; }

.bg-white-50 {
  background-color: rgba(255, 255, 255, 0.5) !important; }

.bg-white-60 {
  background-color: rgba(255, 255, 255, 0.6) !important; }

.bg-white-70 {
  background-color: rgba(255, 255, 255, 0.7) !important; }

.bg-white-80 {
  background-color: rgba(255, 255, 255, 0.8) !important; }

.bg-white-90 {
  background-color: rgba(255, 255, 255, 0.9) !important; }

.bg-black-10 {
  background-color: rgba(0, 0, 0, 0.1) !important; }

.bg-black-20 {
  background-color: rgba(0, 0, 0, 0.2) !important; }

.bg-black-30 {
  background-color: rgba(0, 0, 0, 0.3) !important; }

.bg-black-40 {
  background-color: rgba(0, 0, 0, 0.4) !important; }

.bg-black-50 {
  background-color: rgba(0, 0, 0, 0.5) !important; }

.bg-black-60 {
  background-color: rgba(0, 0, 0, 0.6) !important; }

.bg-black-70 {
  background-color: rgba(0, 0, 0, 0.7) !important; }

.bg-black-80 {
  background-color: rgba(0, 0, 0, 0.8) !important; }

.bg-black-90 {
  background-color: rgba(0, 0, 0, 0.9) !important; }

/*---callout-alert---*/
.callout-secondary, .alert-secondary {
  background-color: #4d7bc9 !important;
  color: #ffffff; }

/*background Patterns*/
.bg-temple-white {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='152' height='152' viewBox='0 0 152 152'%3E%3Cg fill-rule='evenodd'%3E%3Cg id='temple' fill='%23455a64' fill-opacity='0.1'%3E%3Cpath d='M152 150v2H0v-2h28v-8H8v-20H0v-2h8V80h42v20h20v42H30v8h90v-8H80v-42h20V80h42v40h8V30h-8v40h-42V50H80V8h40V0h2v8h20v20h8V0h2v150zm-2 0v-28h-8v20h-20v8h28zM82 30v18h18V30H82zm20 18h20v20h18V30h-20V10H82v18h20v20zm0 2v18h18V50h-18zm20-22h18V10h-18v18zm-54 92v-18H50v18h18zm-20-18H28V82H10v38h20v20h38v-18H48v-20zm0-2V82H30v18h18zm-20 22H10v18h18v-18zm54 0v18h38v-20h20V82h-18v20h-20v20H82zm18-20H82v18h18v-18zm2-2h18V82h-18v18zm20 40v-18h18v18h-18zM30 0h-2v8H8v20H0v2h8v40h42V50h20V8H30V0zm20 48h18V30H50v18zm18-20H48v20H28v20H10V30h20V10h38v18zM30 50h18v18H30V50zm-2-40H10v18h18V10z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E"); }

.bg-temple-dark {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='152' height='152' viewBox='0 0 152 152'%3E%3Cg fill-rule='evenodd'%3E%3Cg id='temple' fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M152 150v2H0v-2h28v-8H8v-20H0v-2h8V80h42v20h20v42H30v8h90v-8H80v-42h20V80h42v40h8V30h-8v40h-42V50H80V8h40V0h2v8h20v20h8V0h2v150zm-2 0v-28h-8v20h-20v8h28zM82 30v18h18V30H82zm20 18h20v20h18V30h-20V10H82v18h20v20zm0 2v18h18V50h-18zm20-22h18V10h-18v18zm-54 92v-18H50v18h18zm-20-18H28V82H10v38h20v20h38v-18H48v-20zm0-2V82H30v18h18zm-20 22H10v18h18v-18zm54 0v18h38v-20h20V82h-18v20h-20v20H82zm18-20H82v18h18v-18zm2-2h18V82h-18v18zm20 40v-18h18v18h-18zM30 0h-2v8H8v20H0v2h8v40h42V50h20V8H30V0zm20 48h18V30H50v18zm18-20H48v20H28v20H10V30h20V10h38v18zM30 50h18v18H30V50zm-2-40H10v18h18V10z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E"); }

.bg-food-white {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='260' height='260' viewBox='0 0 260 260'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23455a64' fill-opacity='0.1'%3E%3Cpath d='M24.37 16c.2.65.39 1.32.54 2H21.17l1.17 2.34.45.9-.24.11V28a5 5 0 0 1-2.23 8.94l-.02.06a8 8 0 0 1-7.75 6h-20a8 8 0 0 1-7.74-6l-.02-.06A5 5 0 0 1-17.45 28v-6.76l-.79-1.58-.44-.9.9-.44.63-.32H-20a23.01 23.01 0 0 1 44.37-2zm-36.82 2a1 1 0 0 0-.44.1l-3.1 1.56.89 1.79 1.31-.66a3 3 0 0 1 2.69 0l2.2 1.1a1 1 0 0 0 .9 0l2.21-1.1a3 3 0 0 1 2.69 0l2.2 1.1a1 1 0 0 0 .9 0l2.21-1.1a3 3 0 0 1 2.69 0l2.2 1.1a1 1 0 0 0 .86.02l2.88-1.27a3 3 0 0 1 2.43 0l2.88 1.27a1 1 0 0 0 .85-.02l3.1-1.55-.89-1.79-1.42.71a3 3 0 0 1-2.56.06l-2.77-1.23a1 1 0 0 0-.4-.09h-.01a1 1 0 0 0-.4.09l-2.78 1.23a3 3 0 0 1-2.56-.06l-2.3-1.15a1 1 0 0 0-.45-.11h-.01a1 1 0 0 0-.44.1L.9 19.22a3 3 0 0 1-2.69 0l-2.2-1.1a1 1 0 0 0-.45-.11h-.01a1 1 0 0 0-.44.1l-2.21 1.11a3 3 0 0 1-2.69 0l-2.2-1.1a1 1 0 0 0-.45-.11h-.01zm0-2h-4.9a21.01 21.01 0 0 1 39.61 0h-2.09l-.06-.13-.26.13h-32.31zm30.35 7.68l1.36-.68h1.3v2h-36v-1.15l.34-.17 1.36-.68h2.59l1.36.68a3 3 0 0 0 2.69 0l1.36-.68h2.59l1.36.68a3 3 0 0 0 2.69 0L2.26 23h2.59l1.36.68a3 3 0 0 0 2.56.06l1.67-.74h3.23l1.67.74a3 3 0 0 0 2.56-.06zM-13.82 27l16.37 4.91L18.93 27h-32.75zm-.63 2h.34l16.66 5 16.67-5h.33a3 3 0 1 1 0 6h-34a3 3 0 1 1 0-6zm1.35 8a6 6 0 0 0 5.65 4h20a6 6 0 0 0 5.66-4H-13.1z'/%3E%3Cpath id='path6_fill-copy' d='M284.37 16c.2.65.39 1.32.54 2H281.17l1.17 2.34.45.9-.24.11V28a5 5 0 0 1-2.23 8.94l-.02.06a8 8 0 0 1-7.75 6h-20a8 8 0 0 1-7.74-6l-.02-.06a5 5 0 0 1-2.24-8.94v-6.76l-.79-1.58-.44-.9.9-.44.63-.32H240a23.01 23.01 0 0 1 44.37-2zm-36.82 2a1 1 0 0 0-.44.1l-3.1 1.56.89 1.79 1.31-.66a3 3 0 0 1 2.69 0l2.2 1.1a1 1 0 0 0 .9 0l2.21-1.1a3 3 0 0 1 2.69 0l2.2 1.1a1 1 0 0 0 .9 0l2.21-1.1a3 3 0 0 1 2.69 0l2.2 1.1a1 1 0 0 0 .86.02l2.88-1.27a3 3 0 0 1 2.43 0l2.88 1.27a1 1 0 0 0 .85-.02l3.1-1.55-.89-1.79-1.42.71a3 3 0 0 1-2.56.06l-2.77-1.23a1 1 0 0 0-.4-.09h-.01a1 1 0 0 0-.4.09l-2.78 1.23a3 3 0 0 1-2.56-.06l-2.3-1.15a1 1 0 0 0-.45-.11h-.01a1 1 0 0 0-.44.1l-2.21 1.11a3 3 0 0 1-2.69 0l-2.2-1.1a1 1 0 0 0-.45-.11h-.01a1 1 0 0 0-.44.1l-2.21 1.11a3 3 0 0 1-2.69 0l-2.2-1.1a1 1 0 0 0-.45-.11h-.01zm0-2h-4.9a21.01 21.01 0 0 1 39.61 0h-2.09l-.06-.13-.26.13h-32.31zm30.35 7.68l1.36-.68h1.3v2h-36v-1.15l.34-.17 1.36-.68h2.59l1.36.68a3 3 0 0 0 2.69 0l1.36-.68h2.59l1.36.68a3 3 0 0 0 2.69 0l1.36-.68h2.59l1.36.68a3 3 0 0 0 2.56.06l1.67-.74h3.23l1.67.74a3 3 0 0 0 2.56-.06zM246.18 27l16.37 4.91L278.93 27h-32.75zm-.63 2h.34l16.66 5 16.67-5h.33a3 3 0 1 1 0 6h-34a3 3 0 1 1 0-6zm1.35 8a6 6 0 0 0 5.65 4h20a6 6 0 0 0 5.66-4H246.9z'/%3E%3Cpath d='M159.5 21.02A9 9 0 0 0 151 15h-42a9 9 0 0 0-8.5 6.02 6 6 0 0 0 .02 11.96A8.99 8.99 0 0 0 109 45h42a9 9 0 0 0 8.48-12.02 6 6 0 0 0 .02-11.96zM151 17h-42a7 7 0 0 0-6.33 4h54.66a7 7 0 0 0-6.33-4zm-9.34 26a8.98 8.98 0 0 0 3.34-7h-2a7 7 0 0 1-7 7h-4.34a8.98 8.98 0 0 0 3.34-7h-2a7 7 0 0 1-7 7h-4.34a8.98 8.98 0 0 0 3.34-7h-2a7 7 0 0 1-7 7h-7a7 7 0 1 1 0-14h42a7 7 0 1 1 0 14h-9.34zM109 27a9 9 0 0 0-7.48 4H101a4 4 0 1 1 0-8h58a4 4 0 0 1 0 8h-.52a9 9 0 0 0-7.48-4h-42z'/%3E%3Cpath d='M39 115a8 8 0 1 0 0-16 8 8 0 0 0 0 16zm6-8a6 6 0 1 1-12 0 6 6 0 0 1 12 0zm-3-29v-2h8v-6H40a4 4 0 0 0-4 4v10H22l-1.33 4-.67 2h2.19L26 130h26l3.81-40H58l-.67-2L56 84H42v-6zm-4-4v10h2V74h8v-2h-8a2 2 0 0 0-2 2zm2 12h14.56l.67 2H22.77l.67-2H40zm13.8 4H24.2l3.62 38h22.36l3.62-38z'/%3E%3Cpath d='M129 92h-6v4h-6v4h-6v14h-3l.24 2 3.76 32h36l3.76-32 .24-2h-3v-14h-6v-4h-6v-4h-8zm18 22v-12h-4v4h3v8h1zm-3 0v-6h-4v6h4zm-6 6v-16h-4v19.17c1.6-.7 2.97-1.8 4-3.17zm-6 3.8V100h-4v23.8a10.04 10.04 0 0 0 4 0zm-6-.63V104h-4v16a10.04 10.04 0 0 0 4 3.17zm-6-9.17v-6h-4v6h4zm-6 0v-8h3v-4h-4v12h1zm27-12v-4h-4v4h3v4h1v-4zm-6 0v-8h-4v4h3v4h1zm-6-4v-4h-4v8h1v-4h3zm-6 4v-4h-4v8h1v-4h3zm7 24a12 12 0 0 0 11.83-10h7.92l-3.53 30h-32.44l-3.53-30h7.92A12 12 0 0 0 130 126z'/%3E%3Cpath d='M212 86v2h-4v-2h4zm4 0h-2v2h2v-2zm-20 0v.1a5 5 0 0 0-.56 9.65l.06.25 1.12 4.48a2 2 0 0 0 1.94 1.52h.01l7.02 24.55a2 2 0 0 0 1.92 1.45h4.98a2 2 0 0 0 1.92-1.45l7.02-24.55a2 2 0 0 0 1.95-1.52L224.5 96l.06-.25a5 5 0 0 0-.56-9.65V86a14 14 0 0 0-28 0zm4 0h6v2h-9a3 3 0 1 0 0 6H223a3 3 0 1 0 0-6H220v-2h2a12 12 0 1 0-24 0h2zm-1.44 14l-1-4h24.88l-1 4h-22.88zm8.95 26l-6.86-24h18.7l-6.86 24h-4.98zM150 242a22 22 0 1 0 0-44 22 22 0 0 0 0 44zm24-22a24 24 0 1 1-48 0 24 24 0 0 1 48 0zm-28.38 17.73l2.04-.87a6 6 0 0 1 4.68 0l2.04.87a2 2 0 0 0 2.5-.82l1.14-1.9a6 6 0 0 1 3.79-2.75l2.15-.5a2 2 0 0 0 1.54-2.12l-.19-2.2a6 6 0 0 1 1.45-4.46l1.45-1.67a2 2 0 0 0 0-2.62l-1.45-1.67a6 6 0 0 1-1.45-4.46l.2-2.2a2 2 0 0 0-1.55-2.13l-2.15-.5a6 6 0 0 1-3.8-2.75l-1.13-1.9a2 2 0 0 0-2.5-.8l-2.04.86a6 6 0 0 1-4.68 0l-2.04-.87a2 2 0 0 0-2.5.82l-1.14 1.9a6 6 0 0 1-3.79 2.75l-2.15.5a2 2 0 0 0-1.54 2.12l.19 2.2a6 6 0 0 1-1.45 4.46l-1.45 1.67a2 2 0 0 0 0 2.62l1.45 1.67a6 6 0 0 1 1.45 4.46l-.2 2.2a2 2 0 0 0 1.55 2.13l2.15.5a6 6 0 0 1 3.8 2.75l1.13 1.9a2 2 0 0 0 2.5.8zm2.82.97a4 4 0 0 1 3.12 0l2.04.87a4 4 0 0 0 4.99-1.62l1.14-1.9a4 4 0 0 1 2.53-1.84l2.15-.5a4 4 0 0 0 3.09-4.24l-.2-2.2a4 4 0 0 1 .97-2.98l1.45-1.67a4 4 0 0 0 0-5.24l-1.45-1.67a4 4 0 0 1-.97-2.97l.2-2.2a4 4 0 0 0-3.09-4.25l-2.15-.5a4 4 0 0 1-2.53-1.84l-1.14-1.9a4 4 0 0 0-5-1.62l-2.03.87a4 4 0 0 1-3.12 0l-2.04-.87a4 4 0 0 0-4.99 1.62l-1.14 1.9a4 4 0 0 1-2.53 1.84l-2.15.5a4 4 0 0 0-3.09 4.24l.2 2.2a4 4 0 0 1-.97 2.98l-1.45 1.67a4 4 0 0 0 0 5.24l1.45 1.67a4 4 0 0 1 .97 2.97l-.2 2.2a4 4 0 0 0 3.09 4.25l2.15.5a4 4 0 0 1 2.53 1.84l1.14 1.9a4 4 0 0 0 5 1.62l2.03-.87zM152 207a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm6 2a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm-11 1a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm-6 0a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm3-5a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm-8 8a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm3 6a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm0 6a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm4 7a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm5-2a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm5 4a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm4-6a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm6-4a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm-4-3a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm4-3a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm-5-4a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm-24 6a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm16 5a5 5 0 1 0 0-10 5 5 0 0 0 0 10zm7-5a7 7 0 1 1-14 0 7 7 0 0 1 14 0zm86-29a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm19 9a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2h-2a1 1 0 0 1-1-1zm-14 5a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm-25 1a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm5 4a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm9 0a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2h-2a1 1 0 0 1-1-1zm15 1a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2h-2a1 1 0 0 1-1-1zm12-2a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm-11-14a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2h-2a1 1 0 0 1-1-1zm-19 0a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm6 5a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2h-2a1 1 0 0 1-1-1zm-25 15c0-.47.01-.94.03-1.4a5 5 0 0 1-1.7-8 3.99 3.99 0 0 1 1.88-5.18 5 5 0 0 1 3.4-6.22 3 3 0 0 1 1.46-1.05 5 5 0 0 1 7.76-3.27A30.86 30.86 0 0 1 246 184c6.79 0 13.06 2.18 18.17 5.88a5 5 0 0 1 7.76 3.27 3 3 0 0 1 1.47 1.05 5 5 0 0 1 3.4 6.22 4 4 0 0 1 1.87 5.18 4.98 4.98 0 0 1-1.7 8c.02.46.03.93.03 1.4v1h-62v-1zm.83-7.17a30.9 30.9 0 0 0-.62 3.57 3 3 0 0 1-.61-4.2c.37.28.78.49 1.23.63zm1.49-4.61c-.36.87-.68 1.76-.96 2.68a2 2 0 0 1-.21-3.71c.33.4.73.75 1.17 1.03zm2.32-4.54c-.54.86-1.03 1.76-1.49 2.68a3 3 0 0 1-.07-4.67 3 3 0 0 0 1.56 1.99zm1.14-1.7c.35-.5.72-.98 1.1-1.46a1 1 0 1 0-1.1 1.45zm5.34-5.77c-1.03.86-2 1.79-2.9 2.77a3 3 0 0 0-1.11-.77 3 3 0 0 1 4-2zm42.66 2.77c-.9-.98-1.87-1.9-2.9-2.77a3 3 0 0 1 4.01 2 3 3 0 0 0-1.1.77zm1.34 1.54c.38.48.75.96 1.1 1.45a1 1 0 1 0-1.1-1.45zm3.73 5.84c-.46-.92-.95-1.82-1.5-2.68a3 3 0 0 0 1.57-1.99 3 3 0 0 1-.07 4.67zm1.8 4.53c-.29-.9-.6-1.8-.97-2.67.44-.28.84-.63 1.17-1.03a2 2 0 0 1-.2 3.7zm1.14 5.51c-.14-1.21-.35-2.4-.62-3.57.45-.14.86-.35 1.23-.63a2.99 2.99 0 0 1-.6 4.2zM275 214a29 29 0 0 0-57.97 0h57.96zM72.33 198.12c-.21-.32-.34-.7-.34-1.12v-12h-2v12a4.01 4.01 0 0 0 7.09 2.54c.57-.69.91-1.57.91-2.54v-12h-2v12a1.99 1.99 0 0 1-2 2 2 2 0 0 1-1.66-.88zM75 176c.38 0 .74-.04 1.1-.12a4 4 0 0 0 6.19 2.4A13.94 13.94 0 0 1 84 185v24a6 6 0 0 1-6 6h-3v9a5 5 0 1 1-10 0v-9h-3a6 6 0 0 1-6-6v-24a14 14 0 0 1 14-14 5 5 0 0 0 5 5zm-17 15v12a1.99 1.99 0 0 0 1.22 1.84 2 2 0 0 0 2.44-.72c.21-.32.34-.7.34-1.12v-12h2v12a3.98 3.98 0 0 1-5.35 3.77 3.98 3.98 0 0 1-.65-.3V209a4 4 0 0 0 4 4h16a4 4 0 0 0 4-4v-24c.01-1.53-.23-2.88-.72-4.17-.43.1-.87.16-1.28.17a6 6 0 0 1-5.2-3 7 7 0 0 1-6.47-4.88A12 12 0 0 0 58 185v6zm9 24v9a3 3 0 1 0 6 0v-9h-6z'/%3E%3Cpath d='M-17 191a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm19 9a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2H3a1 1 0 0 1-1-1zm-14 5a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm-25 1a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm5 4a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm9 0a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2h-2a1 1 0 0 1-1-1zm15 1a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2h-2a1 1 0 0 1-1-1zm12-2a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2H4zm-11-14a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2h-2a1 1 0 0 1-1-1zm-19 0a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm6 5a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2h-2a1 1 0 0 1-1-1zm-25 15c0-.47.01-.94.03-1.4a5 5 0 0 1-1.7-8 3.99 3.99 0 0 1 1.88-5.18 5 5 0 0 1 3.4-6.22 3 3 0 0 1 1.46-1.05 5 5 0 0 1 7.76-3.27A30.86 30.86 0 0 1-14 184c6.79 0 13.06 2.18 18.17 5.88a5 5 0 0 1 7.76 3.27 3 3 0 0 1 1.47 1.05 5 5 0 0 1 3.4 6.22 4 4 0 0 1 1.87 5.18 4.98 4.98 0 0 1-1.7 8c.02.46.03.93.03 1.4v1h-62v-1zm.83-7.17a30.9 30.9 0 0 0-.62 3.57 3 3 0 0 1-.61-4.2c.37.28.78.49 1.23.63zm1.49-4.61c-.36.87-.68 1.76-.96 2.68a2 2 0 0 1-.21-3.71c.33.4.73.75 1.17 1.03zm2.32-4.54c-.54.86-1.03 1.76-1.49 2.68a3 3 0 0 1-.07-4.67 3 3 0 0 0 1.56 1.99zm1.14-1.7c.35-.5.72-.98 1.1-1.46a1 1 0 1 0-1.1 1.45zm5.34-5.77c-1.03.86-2 1.79-2.9 2.77a3 3 0 0 0-1.11-.77 3 3 0 0 1 4-2zm42.66 2.77c-.9-.98-1.87-1.9-2.9-2.77a3 3 0 0 1 4.01 2 3 3 0 0 0-1.1.77zm1.34 1.54c.38.48.75.96 1.1 1.45a1 1 0 1 0-1.1-1.45zm3.73 5.84c-.46-.92-.95-1.82-1.5-2.68a3 3 0 0 0 1.57-1.99 3 3 0 0 1-.07 4.67zm1.8 4.53c-.29-.9-.6-1.8-.97-2.67.44-.28.84-.63 1.17-1.03a2 2 0 0 1-.2 3.7zm1.14 5.51c-.14-1.21-.35-2.4-.62-3.57.45-.14.86-.35 1.23-.63a2.99 2.99 0 0 1-.6 4.2zM15 214a29 29 0 0 0-57.97 0h57.96z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E"); }

.bg-food-dark {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='260' height='260' viewBox='0 0 260 260'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M24.37 16c.2.65.39 1.32.54 2H21.17l1.17 2.34.45.9-.24.11V28a5 5 0 0 1-2.23 8.94l-.02.06a8 8 0 0 1-7.75 6h-20a8 8 0 0 1-7.74-6l-.02-.06A5 5 0 0 1-17.45 28v-6.76l-.79-1.58-.44-.9.9-.44.63-.32H-20a23.01 23.01 0 0 1 44.37-2zm-36.82 2a1 1 0 0 0-.44.1l-3.1 1.56.89 1.79 1.31-.66a3 3 0 0 1 2.69 0l2.2 1.1a1 1 0 0 0 .9 0l2.21-1.1a3 3 0 0 1 2.69 0l2.2 1.1a1 1 0 0 0 .9 0l2.21-1.1a3 3 0 0 1 2.69 0l2.2 1.1a1 1 0 0 0 .86.02l2.88-1.27a3 3 0 0 1 2.43 0l2.88 1.27a1 1 0 0 0 .85-.02l3.1-1.55-.89-1.79-1.42.71a3 3 0 0 1-2.56.06l-2.77-1.23a1 1 0 0 0-.4-.09h-.01a1 1 0 0 0-.4.09l-2.78 1.23a3 3 0 0 1-2.56-.06l-2.3-1.15a1 1 0 0 0-.45-.11h-.01a1 1 0 0 0-.44.1L.9 19.22a3 3 0 0 1-2.69 0l-2.2-1.1a1 1 0 0 0-.45-.11h-.01a1 1 0 0 0-.44.1l-2.21 1.11a3 3 0 0 1-2.69 0l-2.2-1.1a1 1 0 0 0-.45-.11h-.01zm0-2h-4.9a21.01 21.01 0 0 1 39.61 0h-2.09l-.06-.13-.26.13h-32.31zm30.35 7.68l1.36-.68h1.3v2h-36v-1.15l.34-.17 1.36-.68h2.59l1.36.68a3 3 0 0 0 2.69 0l1.36-.68h2.59l1.36.68a3 3 0 0 0 2.69 0L2.26 23h2.59l1.36.68a3 3 0 0 0 2.56.06l1.67-.74h3.23l1.67.74a3 3 0 0 0 2.56-.06zM-13.82 27l16.37 4.91L18.93 27h-32.75zm-.63 2h.34l16.66 5 16.67-5h.33a3 3 0 1 1 0 6h-34a3 3 0 1 1 0-6zm1.35 8a6 6 0 0 0 5.65 4h20a6 6 0 0 0 5.66-4H-13.1z'/%3E%3Cpath id='path6_fill-copy' d='M284.37 16c.2.65.39 1.32.54 2H281.17l1.17 2.34.45.9-.24.11V28a5 5 0 0 1-2.23 8.94l-.02.06a8 8 0 0 1-7.75 6h-20a8 8 0 0 1-7.74-6l-.02-.06a5 5 0 0 1-2.24-8.94v-6.76l-.79-1.58-.44-.9.9-.44.63-.32H240a23.01 23.01 0 0 1 44.37-2zm-36.82 2a1 1 0 0 0-.44.1l-3.1 1.56.89 1.79 1.31-.66a3 3 0 0 1 2.69 0l2.2 1.1a1 1 0 0 0 .9 0l2.21-1.1a3 3 0 0 1 2.69 0l2.2 1.1a1 1 0 0 0 .9 0l2.21-1.1a3 3 0 0 1 2.69 0l2.2 1.1a1 1 0 0 0 .86.02l2.88-1.27a3 3 0 0 1 2.43 0l2.88 1.27a1 1 0 0 0 .85-.02l3.1-1.55-.89-1.79-1.42.71a3 3 0 0 1-2.56.06l-2.77-1.23a1 1 0 0 0-.4-.09h-.01a1 1 0 0 0-.4.09l-2.78 1.23a3 3 0 0 1-2.56-.06l-2.3-1.15a1 1 0 0 0-.45-.11h-.01a1 1 0 0 0-.44.1l-2.21 1.11a3 3 0 0 1-2.69 0l-2.2-1.1a1 1 0 0 0-.45-.11h-.01a1 1 0 0 0-.44.1l-2.21 1.11a3 3 0 0 1-2.69 0l-2.2-1.1a1 1 0 0 0-.45-.11h-.01zm0-2h-4.9a21.01 21.01 0 0 1 39.61 0h-2.09l-.06-.13-.26.13h-32.31zm30.35 7.68l1.36-.68h1.3v2h-36v-1.15l.34-.17 1.36-.68h2.59l1.36.68a3 3 0 0 0 2.69 0l1.36-.68h2.59l1.36.68a3 3 0 0 0 2.69 0l1.36-.68h2.59l1.36.68a3 3 0 0 0 2.56.06l1.67-.74h3.23l1.67.74a3 3 0 0 0 2.56-.06zM246.18 27l16.37 4.91L278.93 27h-32.75zm-.63 2h.34l16.66 5 16.67-5h.33a3 3 0 1 1 0 6h-34a3 3 0 1 1 0-6zm1.35 8a6 6 0 0 0 5.65 4h20a6 6 0 0 0 5.66-4H246.9z'/%3E%3Cpath d='M159.5 21.02A9 9 0 0 0 151 15h-42a9 9 0 0 0-8.5 6.02 6 6 0 0 0 .02 11.96A8.99 8.99 0 0 0 109 45h42a9 9 0 0 0 8.48-12.02 6 6 0 0 0 .02-11.96zM151 17h-42a7 7 0 0 0-6.33 4h54.66a7 7 0 0 0-6.33-4zm-9.34 26a8.98 8.98 0 0 0 3.34-7h-2a7 7 0 0 1-7 7h-4.34a8.98 8.98 0 0 0 3.34-7h-2a7 7 0 0 1-7 7h-4.34a8.98 8.98 0 0 0 3.34-7h-2a7 7 0 0 1-7 7h-7a7 7 0 1 1 0-14h42a7 7 0 1 1 0 14h-9.34zM109 27a9 9 0 0 0-7.48 4H101a4 4 0 1 1 0-8h58a4 4 0 0 1 0 8h-.52a9 9 0 0 0-7.48-4h-42z'/%3E%3Cpath d='M39 115a8 8 0 1 0 0-16 8 8 0 0 0 0 16zm6-8a6 6 0 1 1-12 0 6 6 0 0 1 12 0zm-3-29v-2h8v-6H40a4 4 0 0 0-4 4v10H22l-1.33 4-.67 2h2.19L26 130h26l3.81-40H58l-.67-2L56 84H42v-6zm-4-4v10h2V74h8v-2h-8a2 2 0 0 0-2 2zm2 12h14.56l.67 2H22.77l.67-2H40zm13.8 4H24.2l3.62 38h22.36l3.62-38z'/%3E%3Cpath d='M129 92h-6v4h-6v4h-6v14h-3l.24 2 3.76 32h36l3.76-32 .24-2h-3v-14h-6v-4h-6v-4h-8zm18 22v-12h-4v4h3v8h1zm-3 0v-6h-4v6h4zm-6 6v-16h-4v19.17c1.6-.7 2.97-1.8 4-3.17zm-6 3.8V100h-4v23.8a10.04 10.04 0 0 0 4 0zm-6-.63V104h-4v16a10.04 10.04 0 0 0 4 3.17zm-6-9.17v-6h-4v6h4zm-6 0v-8h3v-4h-4v12h1zm27-12v-4h-4v4h3v4h1v-4zm-6 0v-8h-4v4h3v4h1zm-6-4v-4h-4v8h1v-4h3zm-6 4v-4h-4v8h1v-4h3zm7 24a12 12 0 0 0 11.83-10h7.92l-3.53 30h-32.44l-3.53-30h7.92A12 12 0 0 0 130 126z'/%3E%3Cpath d='M212 86v2h-4v-2h4zm4 0h-2v2h2v-2zm-20 0v.1a5 5 0 0 0-.56 9.65l.06.25 1.12 4.48a2 2 0 0 0 1.94 1.52h.01l7.02 24.55a2 2 0 0 0 1.92 1.45h4.98a2 2 0 0 0 1.92-1.45l7.02-24.55a2 2 0 0 0 1.95-1.52L224.5 96l.06-.25a5 5 0 0 0-.56-9.65V86a14 14 0 0 0-28 0zm4 0h6v2h-9a3 3 0 1 0 0 6H223a3 3 0 1 0 0-6H220v-2h2a12 12 0 1 0-24 0h2zm-1.44 14l-1-4h24.88l-1 4h-22.88zm8.95 26l-6.86-24h18.7l-6.86 24h-4.98zM150 242a22 22 0 1 0 0-44 22 22 0 0 0 0 44zm24-22a24 24 0 1 1-48 0 24 24 0 0 1 48 0zm-28.38 17.73l2.04-.87a6 6 0 0 1 4.68 0l2.04.87a2 2 0 0 0 2.5-.82l1.14-1.9a6 6 0 0 1 3.79-2.75l2.15-.5a2 2 0 0 0 1.54-2.12l-.19-2.2a6 6 0 0 1 1.45-4.46l1.45-1.67a2 2 0 0 0 0-2.62l-1.45-1.67a6 6 0 0 1-1.45-4.46l.2-2.2a2 2 0 0 0-1.55-2.13l-2.15-.5a6 6 0 0 1-3.8-2.75l-1.13-1.9a2 2 0 0 0-2.5-.8l-2.04.86a6 6 0 0 1-4.68 0l-2.04-.87a2 2 0 0 0-2.5.82l-1.14 1.9a6 6 0 0 1-3.79 2.75l-2.15.5a2 2 0 0 0-1.54 2.12l.19 2.2a6 6 0 0 1-1.45 4.46l-1.45 1.67a2 2 0 0 0 0 2.62l1.45 1.67a6 6 0 0 1 1.45 4.46l-.2 2.2a2 2 0 0 0 1.55 2.13l2.15.5a6 6 0 0 1 3.8 2.75l1.13 1.9a2 2 0 0 0 2.5.8zm2.82.97a4 4 0 0 1 3.12 0l2.04.87a4 4 0 0 0 4.99-1.62l1.14-1.9a4 4 0 0 1 2.53-1.84l2.15-.5a4 4 0 0 0 3.09-4.24l-.2-2.2a4 4 0 0 1 .97-2.98l1.45-1.67a4 4 0 0 0 0-5.24l-1.45-1.67a4 4 0 0 1-.97-2.97l.2-2.2a4 4 0 0 0-3.09-4.25l-2.15-.5a4 4 0 0 1-2.53-1.84l-1.14-1.9a4 4 0 0 0-5-1.62l-2.03.87a4 4 0 0 1-3.12 0l-2.04-.87a4 4 0 0 0-4.99 1.62l-1.14 1.9a4 4 0 0 1-2.53 1.84l-2.15.5a4 4 0 0 0-3.09 4.24l.2 2.2a4 4 0 0 1-.97 2.98l-1.45 1.67a4 4 0 0 0 0 5.24l1.45 1.67a4 4 0 0 1 .97 2.97l-.2 2.2a4 4 0 0 0 3.09 4.25l2.15.5a4 4 0 0 1 2.53 1.84l1.14 1.9a4 4 0 0 0 5 1.62l2.03-.87zM152 207a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm6 2a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm-11 1a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm-6 0a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm3-5a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm-8 8a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm3 6a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm0 6a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm4 7a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm5-2a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm5 4a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm4-6a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm6-4a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm-4-3a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm4-3a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm-5-4a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm-24 6a1 1 0 1 1 2 0 1 1 0 0 1-2 0zm16 5a5 5 0 1 0 0-10 5 5 0 0 0 0 10zm7-5a7 7 0 1 1-14 0 7 7 0 0 1 14 0zm86-29a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm19 9a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2h-2a1 1 0 0 1-1-1zm-14 5a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm-25 1a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm5 4a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm9 0a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2h-2a1 1 0 0 1-1-1zm15 1a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2h-2a1 1 0 0 1-1-1zm12-2a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm-11-14a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2h-2a1 1 0 0 1-1-1zm-19 0a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm6 5a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2h-2a1 1 0 0 1-1-1zm-25 15c0-.47.01-.94.03-1.4a5 5 0 0 1-1.7-8 3.99 3.99 0 0 1 1.88-5.18 5 5 0 0 1 3.4-6.22 3 3 0 0 1 1.46-1.05 5 5 0 0 1 7.76-3.27A30.86 30.86 0 0 1 246 184c6.79 0 13.06 2.18 18.17 5.88a5 5 0 0 1 7.76 3.27 3 3 0 0 1 1.47 1.05 5 5 0 0 1 3.4 6.22 4 4 0 0 1 1.87 5.18 4.98 4.98 0 0 1-1.7 8c.02.46.03.93.03 1.4v1h-62v-1zm.83-7.17a30.9 30.9 0 0 0-.62 3.57 3 3 0 0 1-.61-4.2c.37.28.78.49 1.23.63zm1.49-4.61c-.36.87-.68 1.76-.96 2.68a2 2 0 0 1-.21-3.71c.33.4.73.75 1.17 1.03zm2.32-4.54c-.54.86-1.03 1.76-1.49 2.68a3 3 0 0 1-.07-4.67 3 3 0 0 0 1.56 1.99zm1.14-1.7c.35-.5.72-.98 1.1-1.46a1 1 0 1 0-1.1 1.45zm5.34-5.77c-1.03.86-2 1.79-2.9 2.77a3 3 0 0 0-1.11-.77 3 3 0 0 1 4-2zm42.66 2.77c-.9-.98-1.87-1.9-2.9-2.77a3 3 0 0 1 4.01 2 3 3 0 0 0-1.1.77zm1.34 1.54c.38.48.75.96 1.1 1.45a1 1 0 1 0-1.1-1.45zm3.73 5.84c-.46-.92-.95-1.82-1.5-2.68a3 3 0 0 0 1.57-1.99 3 3 0 0 1-.07 4.67zm1.8 4.53c-.29-.9-.6-1.8-.97-2.67.44-.28.84-.63 1.17-1.03a2 2 0 0 1-.2 3.7zm1.14 5.51c-.14-1.21-.35-2.4-.62-3.57.45-.14.86-.35 1.23-.63a2.99 2.99 0 0 1-.6 4.2zM275 214a29 29 0 0 0-57.97 0h57.96zM72.33 198.12c-.21-.32-.34-.7-.34-1.12v-12h-2v12a4.01 4.01 0 0 0 7.09 2.54c.57-.69.91-1.57.91-2.54v-12h-2v12a1.99 1.99 0 0 1-2 2 2 2 0 0 1-1.66-.88zM75 176c.38 0 .74-.04 1.1-.12a4 4 0 0 0 6.19 2.4A13.94 13.94 0 0 1 84 185v24a6 6 0 0 1-6 6h-3v9a5 5 0 1 1-10 0v-9h-3a6 6 0 0 1-6-6v-24a14 14 0 0 1 14-14 5 5 0 0 0 5 5zm-17 15v12a1.99 1.99 0 0 0 1.22 1.84 2 2 0 0 0 2.44-.72c.21-.32.34-.7.34-1.12v-12h2v12a3.98 3.98 0 0 1-5.35 3.77 3.98 3.98 0 0 1-.65-.3V209a4 4 0 0 0 4 4h16a4 4 0 0 0 4-4v-24c.01-1.53-.23-2.88-.72-4.17-.43.1-.87.16-1.28.17a6 6 0 0 1-5.2-3 7 7 0 0 1-6.47-4.88A12 12 0 0 0 58 185v6zm9 24v9a3 3 0 1 0 6 0v-9h-6z'/%3E%3Cpath d='M-17 191a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm19 9a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2H3a1 1 0 0 1-1-1zm-14 5a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm-25 1a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm5 4a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm9 0a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2h-2a1 1 0 0 1-1-1zm15 1a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2h-2a1 1 0 0 1-1-1zm12-2a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2H4zm-11-14a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2h-2a1 1 0 0 1-1-1zm-19 0a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2h-2zm6 5a1 1 0 0 1 1-1h2a1 1 0 0 1 0 2h-2a1 1 0 0 1-1-1zm-25 15c0-.47.01-.94.03-1.4a5 5 0 0 1-1.7-8 3.99 3.99 0 0 1 1.88-5.18 5 5 0 0 1 3.4-6.22 3 3 0 0 1 1.46-1.05 5 5 0 0 1 7.76-3.27A30.86 30.86 0 0 1-14 184c6.79 0 13.06 2.18 18.17 5.88a5 5 0 0 1 7.76 3.27 3 3 0 0 1 1.47 1.05 5 5 0 0 1 3.4 6.22 4 4 0 0 1 1.87 5.18 4.98 4.98 0 0 1-1.7 8c.02.46.03.93.03 1.4v1h-62v-1zm.83-7.17a30.9 30.9 0 0 0-.62 3.57 3 3 0 0 1-.61-4.2c.37.28.78.49 1.23.63zm1.49-4.61c-.36.87-.68 1.76-.96 2.68a2 2 0 0 1-.21-3.71c.33.4.73.75 1.17 1.03zm2.32-4.54c-.54.86-1.03 1.76-1.49 2.68a3 3 0 0 1-.07-4.67 3 3 0 0 0 1.56 1.99zm1.14-1.7c.35-.5.72-.98 1.1-1.46a1 1 0 1 0-1.1 1.45zm5.34-5.77c-1.03.86-2 1.79-2.9 2.77a3 3 0 0 0-1.11-.77 3 3 0 0 1 4-2zm42.66 2.77c-.9-.98-1.87-1.9-2.9-2.77a3 3 0 0 1 4.01 2 3 3 0 0 0-1.1.77zm1.34 1.54c.38.48.75.96 1.1 1.45a1 1 0 1 0-1.1-1.45zm3.73 5.84c-.46-.92-.95-1.82-1.5-2.68a3 3 0 0 0 1.57-1.99 3 3 0 0 1-.07 4.67zm1.8 4.53c-.29-.9-.6-1.8-.97-2.67.44-.28.84-.63 1.17-1.03a2 2 0 0 1-.2 3.7zm1.14 5.51c-.14-1.21-.35-2.4-.62-3.57.45-.14.86-.35 1.23-.63a2.99 2.99 0 0 1-.6 4.2zM15 214a29 29 0 0 0-57.97 0h57.96z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E"); }

.bg-brick-white {
  background-image: url("data:image/svg+xml,%3Csvg width='42' height='44' viewBox='0 0 42 44' xmlns='http://www.w3.org/2000/svg'%3E%3Cg id='Page-1' fill='none' fill-rule='evenodd'%3E%3Cg id='brick-wall' fill='%23455a64' fill-opacity='0.1'%3E%3Cpath d='M0 0h42v44H0V0zm1 1h40v20H1V1zM0 23h20v20H0V23zm22 0h20v20H22V23z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E"); }

.bg-brick-dark {
  background-image: url("data:image/svg+xml,%3Csvg width='42' height='44' viewBox='0 0 42 44' xmlns='http://www.w3.org/2000/svg'%3E%3Cg id='Page-1' fill='none' fill-rule='evenodd'%3E%3Cg id='brick-wall' fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M0 0h42v44H0V0zm1 1h40v20H1V1zM0 23h20v20H0V23zm22 0h20v20H22V23z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E"); }

.bg-bubbles-white {
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23455a64' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E"); }

.bg-bubbles-dark {
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E"); }

/*border color*/
.wrapper .border-secondary {
  border-color: #4d7bc9 !important; }

.wrapper .border-gray {
  border-color: #87a7db !important; }

.wrapper .border-dark {
  border-color: #172b4c !important; }

.wrapper .border-transparent {
  border-color: transparent !important; }

.wrapper .border-white {
  border-color: #ffffff !important; }

.wrapper .border-light {
  border-color: #f3f6f9 !important; }

.wrapper .border-fade {
  border-color: rgba(97, 106, 120, 0.07) !important; }

/*---Text---*/
.text-secondary {
  color: #4d7bc9 !important; }

a.text-secondary:hover, a.text-secondary:focus {
  color: #4d7bc9 !important; }

.hover-secondary:hover, .hover-secondary:focus {
  color: #4d7bc9 !important; }

.text-dark {
  color: #172b4c !important; }

a.text-dark:hover, a.text-dark:focus {
  color: #172b4c !important; }

.hover-dark:hover, .hover-dark:focus {
  color: #172b4c !important; }

.text-mute {
  color: gray !important; }

a.text-mute:hover, a.text-mute:focus {
  color: gray !important; }

.hover-mute:hover, .hover-mute:focus {
  color: gray !important; }

.text-light {
  color: #b5b5c3 !important; }

a.text-light:hover, a.text-light:focus {
  color: #b5b5c3 !important; }

.hover-light:hover, .hover-light:focus {
  color: #b5b5c3 !important; }

.text-lighter {
  color: #d1d3e0 !important; }

a.text-lighter:hover, a.text-lighter:focus {
  color: #d1d3e0 !important; }

.hover-lighter:hover, .hover-lighter:focus {
  color: #d1d3e0 !important; }

.text-fade {
  color: #7e8299 !important; }

a.text-fade:hover, a.text-fade:focus {
  color: #d1d3e0 !important; }

.hover-fade:hover, .hover-fade:focus {
  color: #d1d3e0 !important; }

.dark-skin .text-fade {
  color: #8daac6 !important; }
.dark-skin a.text-fade:hover, .dark-skin a.text-fade:focus {
  color: #8daac6 !important; }
.dark-skin .hover-fade:hover, .dark-skin .hover-fade:focus {
  color: #8daac6 !important; }

.text-white {
  color: #ffffff !important; }

a.text-white:hover, a.text-white:focus {
  color: #ffffff !important; }

.hover-white:hover, .hover-white:focus {
  color: #ffffff !important; }

.owl-theme .owl-dots .owl-dot.active span, .owl-theme .owl-dots .owl-dot:hover span {
  background: #3246D3; }

/*---color skin---*/
/**************************************
Theme Primary Color
**************************************/
.bg-gradient-primary {
  background: linear-gradient(45deg, #3246D3, #00D0FF); }

.bg-light-body {
  background: transparent; }

.theme-primary a:hover, .theme-primary a:active, .theme-primary a:focus {
  color: #3246D3; }
.theme-primary .svg-icon {
  filter: invert(0.6) sepia(1) saturate(1) hue-rotate(185deg); }
  .theme-primary .svg-icon:hover, .theme-primary .svg-icon:active, .theme-primary .svg-icon:focus {
    filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg); }
.theme-primary a:hover .svg-icon, .theme-primary a:active .svg-icon, .theme-primary a:focus .svg-icon {
  filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg); }

/*---Primary Button---*/
.theme-primary .btn-link {
  color: #3246D3; }
.theme-primary .btn-primary {
  background-color: #3246D3;
  border-color: #3246D3;
  color: #ffffff; }
  .theme-primary .btn-primary:hover, .theme-primary .btn-primary:active, .theme-primary .btn-primary:focus, .theme-primary .btn-primary.active {
    background-color: #2536ad !important;
    border-color: #2536ad !important;
    color: #ffffff !important; }
  .theme-primary .btn-primary:disabled {
    background-color: #8692e5;
    border-color: #3246D3;
    opacity: 0.5; }
  .theme-primary .btn-primary.disabled {
    background-color: #8692e5;
    border-color: #3246D3;
    opacity: 0.5; }
.theme-primary .show > .btn-primary.dropdown-toggle {
  background-color: #2536ad !important;
  border-color: #2536ad !important;
  color: #ffffff; }
.theme-primary .btn-outline.btn-primary {
  color: #3246D3;
  background-color: transparent;
  border-color: #3246D3 !important; }
  .theme-primary .btn-outline.btn-primary:hover, .theme-primary .btn-outline.btn-primary:active, .theme-primary .btn-outline.btn-primary.active {
    background-color: #2536ad !important;
    border-color: #2536ad !important;
    color: #ffffff !important; }
.theme-primary .show > .btn-outline.btn-primary.dropdown-toggle {
  background-color: #2536ad !important;
  border-color: #2536ad !important;
  color: #ffffff; }
.theme-primary .btn-flat.btn-primary {
  color: #3246D3 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-primary .btn-flat.btn-primary:hover, .theme-primary .btn-flat.btn-primary:active, .theme-primary .btn-flat.btn-primary.active {
    background-color: #2536ad !important;
    border-color: #2536ad !important;
    color: #ffffff !important; }

/*---info Button---*/
.theme-primary .btn-info {
  background-color: #00D0FF;
  border-color: #00D0FF;
  color: #ffffff; }
  .theme-primary .btn-info:hover, .theme-primary .btn-info:active, .theme-primary .btn-info:focus, .theme-primary .btn-info.active {
    background-color: #00a6cc !important;
    border-color: #00a6cc !important;
    color: #ffffff !important; }
  .theme-primary .btn-info:disabled {
    background-color: #66e3ff;
    border-color: #00D0FF;
    opacity: 0.5; }
  .theme-primary .btn-info.disabled {
    background-color: #66e3ff;
    border-color: #00D0FF;
    opacity: 0.5; }
.theme-primary .show > .btn-info.dropdown-toggle {
  background-color: #00a6cc !important;
  border-color: #00a6cc !important;
  color: #ffffff; }
.theme-primary .btn-outline.btn-info {
  color: #00D0FF;
  background-color: transparent;
  border-color: #00D0FF !important; }
  .theme-primary .btn-outline.btn-info:hover, .theme-primary .btn-outline.btn-info:active, .theme-primary .btn-outline.btn-info.active {
    background-color: #00a6cc !important;
    border-color: #00a6cc !important;
    color: #ffffff !important; }
.theme-primary .show > .btn-outline.btn-info.dropdown-toggle {
  background-color: #00a6cc !important;
  border-color: #00a6cc !important;
  color: #ffffff; }
.theme-primary .btn-flat.btn-info {
  color: #00D0FF !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-primary .btn-flat.btn-info:hover, .theme-primary .btn-flat.btn-info:active, .theme-primary .btn-flat.btn-info.active {
    background-color: #00a6cc !important;
    border-color: #00a6cc !important;
    color: #ffffff !important; }

/*---Success Button---*/
.theme-primary .btn-success {
  background-color: #1dbfc1;
  border-color: #1dbfc1;
  color: #ffffff; }
  .theme-primary .btn-success:hover, .theme-primary .btn-success:active, .theme-primary .btn-success:focus, .theme-primary .btn-success.active {
    background-color: #169395 !important;
    border-color: #169395 !important;
    color: #ffffff !important; }
  .theme-primary .btn-success:disabled {
    background-color: #5de5e7;
    border-color: #1dbfc1;
    opacity: 0.5; }
  .theme-primary .btn-success.disabled {
    background-color: #5de5e7;
    border-color: #1dbfc1;
    opacity: 0.5; }
.theme-primary .show > .btn-success.dropdown-toggle {
  background-color: #169395 !important;
  border-color: #169395 !important;
  color: #ffffff; }
.theme-primary .btn-outline.btn-success {
  color: #1dbfc1;
  background-color: transparent;
  border-color: #1dbfc1 !important; }
  .theme-primary .btn-outline.btn-success:hover, .theme-primary .btn-outline.btn-success:active, .theme-primary .btn-outline.btn-success.active {
    background-color: #169395 !important;
    border-color: #169395 !important;
    color: #ffffff !important; }
.theme-primary .show > .btn-outline.btn-success.dropdown-toggle {
  background-color: #169395 !important;
  border-color: #169395 !important;
  color: #ffffff; }
.theme-primary .btn-flat.btn-success {
  color: #1dbfc1 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-primary .btn-flat.btn-success:hover, .theme-primary .btn-flat.btn-success:active, .theme-primary .btn-flat.btn-success.active {
    background-color: #169395 !important;
    border-color: #169395 !important;
    color: #ffffff !important; }

/*---Danger Button---*/
.theme-primary .btn-danger {
  background-color: #ee3158;
  border-color: #ee3158;
  color: #ffffff; }
  .theme-primary .btn-danger:hover, .theme-primary .btn-danger:active, .theme-primary .btn-danger:focus, .theme-primary .btn-danger.active {
    background-color: #da123b !important;
    border-color: #da123b !important;
    color: #ffffff !important; }
  .theme-primary .btn-danger:disabled {
    background-color: #f68fa4;
    border-color: #ee3158;
    opacity: 0.5; }
  .theme-primary .btn-danger.disabled {
    background-color: #f68fa4;
    border-color: #ee3158;
    opacity: 0.5; }
.theme-primary .show > .btn-danger.dropdown-toggle {
  background-color: #da123b !important;
  border-color: #da123b !important;
  color: #ffffff; }
.theme-primary .btn-outline.btn-danger {
  color: #ee3158;
  background-color: transparent;
  border-color: #ee3158 !important; }
  .theme-primary .btn-outline.btn-danger:hover, .theme-primary .btn-outline.btn-danger:active, .theme-primary .btn-outline.btn-danger.active {
    background-color: #da123b !important;
    border-color: #da123b !important;
    color: #ffffff !important; }
.theme-primary .show > .btn-outline.btn-danger.dropdown-toggle {
  background-color: #da123b !important;
  border-color: #da123b !important;
  color: #ffffff; }
.theme-primary .btn-flat.btn-danger {
  color: #ee3158 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-primary .btn-flat.btn-danger:hover, .theme-primary .btn-flat.btn-danger:active, .theme-primary .btn-flat.btn-danger.active {
    background-color: #da123b !important;
    border-color: #da123b !important;
    color: #ffffff !important; }

/*---Warning Button---*/
.theme-primary .btn-warning {
  background-color: #ffa800;
  border-color: #ffa800;
  color: #ffffff; }
  .theme-primary .btn-warning:hover, .theme-primary .btn-warning:active, .theme-primary .btn-warning:focus, .theme-primary .btn-warning.active {
    background-color: #cc8600 !important;
    border-color: #cc8600 !important;
    color: #ffffff !important; }
  .theme-primary .btn-warning:disabled {
    background-color: #ffcb66;
    border-color: #ffa800;
    opacity: 0.5; }
  .theme-primary .btn-warning.disabled {
    background-color: #ffcb66;
    border-color: #ffa800;
    opacity: 0.5; }
.theme-primary .show > .btn-warning.dropdown-toggle {
  background-color: #cc8600 !important;
  border-color: #cc8600 !important;
  color: #ffffff; }
.theme-primary .btn-outline.btn-warning {
  color: #ffa800;
  background-color: transparent;
  border-color: #ffa800 !important; }
  .theme-primary .btn-outline.btn-warning:hover, .theme-primary .btn-outline.btn-warning:active, .theme-primary .btn-outline.btn-warning.active {
    background-color: #cc8600 !important;
    border-color: #cc8600 !important;
    color: #ffffff !important; }
.theme-primary .show > .btn-outline.btn-warning.dropdown-toggle {
  background-color: #cc8600 !important;
  border-color: #cc8600 !important;
  color: #ffffff; }
.theme-primary .btn-flat.btn-warning {
  color: #ffa800 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-primary .btn-flat.btn-warning:hover, .theme-primary .btn-flat.btn-warning:active, .theme-primary .btn-flat.btn-warning.active {
    background-color: #cc8600 !important;
    border-color: #cc8600 !important;
    color: #ffffff !important; }

/*---Primary Button light---*/
.theme-primary .btn-primary-light {
  background-color: #dbdfff;
  border-color: #dbdfff;
  color: #3246D3; }
  .theme-primary .btn-primary-light:hover, .theme-primary .btn-primary-light:active, .theme-primary .btn-primary-light:focus, .theme-primary .btn-primary-light.active {
    background-color: #3246D3 !important;
    border-color: #3246D3 !important;
    color: #ffffff !important; }
  .theme-primary .btn-primary-light:disabled {
    background-color: white;
    border-color: #dbdfff;
    opacity: 0.5; }
  .theme-primary .btn-primary-light.disabled {
    background-color: white;
    border-color: #dbdfff;
    opacity: 0.5; }
.theme-primary .show > .btn-primary-light.dropdown-toggle {
  background-color: #3246D3 !important;
  border-color: #3246D3 !important;
  color: #ffffff; }
.theme-primary .btn-outline.btn-primary-light {
  color: #3246D3;
  background-color: transparent;
  border-color: #dbdfff !important; }
  .theme-primary .btn-outline.btn-primary-light:hover, .theme-primary .btn-outline.btn-primary-light:active, .theme-primary .btn-outline.btn-primary-light.active {
    background-color: #3246D3 !important;
    border-color: #3246D3 !important;
    color: #ffffff !important; }
.theme-primary .show > .btn-outline.btn-primary-light.dropdown-toggle {
  background-color: #3246D3 !important;
  border-color: #3246D3 !important;
  color: #ffffff; }
.theme-primary .btn-flat.btn-primary-light {
  color: #3246D3 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-primary .btn-flat.btn-primary-light:hover, .theme-primary .btn-flat.btn-primary-light:active, .theme-primary .btn-flat.btn-primary-light.active {
    background-color: #3246D3 !important;
    border-color: #3246D3 !important;
    color: #ffffff !important; }

/*---info Button light---*/
.theme-primary .btn-info-light {
  background-color: #e1f9ff;
  border-color: #e1f9ff;
  color: #00D0FF; }
  .theme-primary .btn-info-light:hover, .theme-primary .btn-info-light:active, .theme-primary .btn-info-light:focus, .theme-primary .btn-info-light.active {
    background-color: #00D0FF !important;
    border-color: #00D0FF !important;
    color: #ffffff !important; }
  .theme-primary .btn-info-light:disabled {
    background-color: white;
    border-color: #e1f9ff;
    opacity: 0.5; }
  .theme-primary .btn-info-light.disabled {
    background-color: white;
    border-color: #e1f9ff;
    opacity: 0.5; }
.theme-primary .show > .btn-info.dropdown-toggle {
  background-color: #00D0FF !important;
  border-color: #00D0FF !important;
  color: #ffffff; }
.theme-primary .btn-outline.btn-info-light {
  color: #00D0FF;
  background-color: transparent;
  border-color: #e1f9ff !important; }
  .theme-primary .btn-outline.btn-info-light:hover, .theme-primary .btn-outline.btn-info-light:active, .theme-primary .btn-outline.btn-info-light.active {
    background-color: #00D0FF !important;
    border-color: #00D0FF !important;
    color: #ffffff !important; }
.theme-primary .show > .btn-outline.btn-info-light.dropdown-toggle {
  background-color: #00D0FF !important;
  border-color: #00D0FF !important;
  color: #ffffff; }
.theme-primary .btn-flat.btn-info-light {
  color: #00D0FF !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-primary .btn-flat.btn-info-light:hover, .theme-primary .btn-flat.btn-info-light:active, .theme-primary .btn-flat.btn-info-light.active {
    background-color: #00D0FF !important;
    border-color: #00D0FF !important;
    color: #ffffff !important; }

/*---Success Button light---*/
.theme-primary .btn-success-light {
  background-color: #e8f9f9;
  border-color: #e8f9f9;
  color: #1dbfc1; }
  .theme-primary .btn-success-light:hover, .theme-primary .btn-success-light:active, .theme-primary .btn-success-light:focus, .theme-primary .btn-success-light.active {
    background-color: #1dbfc1 !important;
    border-color: #1dbfc1 !important;
    color: #ffffff !important; }
  .theme-primary .btn-success-light:disabled {
    background-color: white;
    border-color: #e8f9f9;
    opacity: 0.5; }
  .theme-primary .btn-success-light.disabled {
    background-color: white;
    border-color: #e8f9f9;
    opacity: 0.5; }
.theme-primary .show > .btn-success-light.dropdown-toggle {
  background-color: #1dbfc1 !important;
  border-color: #1dbfc1 !important;
  color: #ffffff; }
.theme-primary .btn-outline.btn-success-light {
  color: #1dbfc1;
  background-color: transparent;
  border-color: #e8f9f9 !important; }
  .theme-primary .btn-outline.btn-success-light:hover, .theme-primary .btn-outline.btn-success-light:active, .theme-primary .btn-outline.btn-success-light.active {
    background-color: #1dbfc1 !important;
    border-color: #1dbfc1 !important;
    color: #ffffff !important; }
.theme-primary .show > .btn-outline.btn-success-light.dropdown-toggle {
  background-color: #1dbfc1 !important;
  border-color: #1dbfc1 !important;
  color: #ffffff; }
.theme-primary .btn-flat.btn-success-light {
  color: #1dbfc1 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-primary .btn-flat.btn-success-light:hover, .theme-primary .btn-flat.btn-success-light:active, .theme-primary .btn-flat.btn-success-light.active {
    background-color: #1dbfc1 !important;
    border-color: #1dbfc1 !important;
    color: #ffffff !important; }

/*---Danger Button light---*/
.theme-primary .btn-danger-light {
  background-color: #ffd6de;
  border-color: #ffd6de;
  color: #ee3158; }
  .theme-primary .btn-danger-light:hover, .theme-primary .btn-danger-light:active, .theme-primary .btn-danger-light:focus, .theme-primary .btn-danger-light.active {
    background-color: #ee3158 !important;
    border-color: #ee3158 !important;
    color: #ffffff !important; }
  .theme-primary .btn-danger-light:disabled {
    background-color: white;
    border-color: #ffd6de;
    opacity: 0.5; }
  .theme-primary .btn-danger-light.disabled {
    background-color: white;
    border-color: #ffd6de;
    opacity: 0.5; }
.theme-primary .show > .btn-danger-light.dropdown-toggle {
  background-color: #ee3158 !important;
  border-color: #ee3158 !important;
  color: #ffffff; }
.theme-primary .btn-outline.btn-danger-light {
  color: #ee3158;
  background-color: transparent;
  border-color: #ffd6de !important; }
  .theme-primary .btn-outline.btn-danger-light:hover, .theme-primary .btn-outline.btn-danger-light:active, .theme-primary .btn-outline.btn-danger-light.active {
    background-color: #ee3158 !important;
    border-color: #ee3158 !important;
    color: #ffffff !important; }
.theme-primary .show > .btn-outline.btn-danger-light.dropdown-toggle {
  background-color: #ee3158 !important;
  border-color: #ee3158 !important;
  color: #ffffff; }
.theme-primary .btn-flat.btn-danger-light {
  color: #ee3158 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-primary .btn-flat.btn-danger-light:hover, .theme-primary .btn-flat.btn-danger-light:active, .theme-primary .btn-flat.btn-danger-light.active {
    background-color: #ee3158 !important;
    border-color: #ee3158 !important;
    color: #ffffff !important; }

/*---Warning Button light---*/
.theme-primary .btn-warning-light {
  background-color: #fff8ea;
  border-color: #fff8ea;
  color: #ffa800; }
  .theme-primary .btn-warning-light:hover, .theme-primary .btn-warning-light:active, .theme-primary .btn-warning-light:focus, .theme-primary .btn-warning-light.active {
    background-color: #ffa800 !important;
    border-color: #ffa800 !important;
    color: #ffffff !important; }
  .theme-primary .btn-warning-light:disabled {
    background-color: white;
    border-color: #fff8ea;
    opacity: 0.5; }
  .theme-primary .btn-warning-light.disabled {
    background-color: white;
    border-color: #fff8ea;
    opacity: 0.5; }
.theme-primary .show > .btn-warning-light.dropdown-toggle {
  background-color: #ffa800 !important;
  border-color: #ffa800 !important;
  color: #ffffff; }
.theme-primary .btn-outline.btn-warning-light {
  color: #ffa800;
  background-color: transparent;
  border-color: #fff8ea !important; }
  .theme-primary .btn-outline.btn-warning-light:hover, .theme-primary .btn-outline.btn-warning-light:active, .theme-primary .btn-outline.btn-warning-light.active {
    background-color: #ffa800 !important;
    border-color: #ffa800 !important;
    color: #ffffff !important; }
.theme-primary .show > .btn-outline.btn-warning-light.dropdown-toggle {
  background-color: #ffa800 !important;
  border-color: #ffa800 !important;
  color: #ffffff; }
.theme-primary .btn-flat.btn-warning-light {
  color: #ffa800 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-primary .btn-flat.btn-warning-light:hover, .theme-primary .btn-flat.btn-warning-light:active, .theme-primary .btn-flat.btn-warning-light.active {
    background-color: #ffa800 !important;
    border-color: #ffa800 !important;
    color: #ffffff !important; }

/*---callout---*/
.theme-primary .callout.callout-primary {
  border-color: #3246D3;
  background-color: #3246D3 !important; }
.theme-primary .callout.callout-info {
  border-color: #00D0FF;
  background-color: #00D0FF !important; }
.theme-primary .callout.callout-success {
  border-color: #1dbfc1;
  background-color: #1dbfc1 !important; }
.theme-primary .callout.callout-danger {
  border-color: #ee3158;
  background-color: #ee3158 !important; }
.theme-primary .callout.callout-warning {
  border-color: #ffa800;
  background-color: #ffa800 !important; }

/*---alert---*/
.theme-primary .alert-primary {
  border-color: #3246D3;
  background-color: #3246D3 !important;
  color: #ffffff; }
.theme-primary .alert-info {
  border-color: #00D0FF;
  background-color: #00D0FF !important;
  color: #ffffff; }
.theme-primary .alert-success {
  border-color: #1dbfc1;
  background-color: #1dbfc1 !important;
  color: #ffffff; }
.theme-primary .alert-danger {
  border-color: #ee3158;
  background-color: #ee3158 !important;
  color: #ffffff; }
.theme-primary .alert-error {
  border-color: #ee3158;
  background-color: #ee3158 !important;
  color: #ffffff; }
.theme-primary .alert-warning {
  border-color: #ffa800;
  background-color: #ffa800 !important;
  color: #ffffff; }

/*---direct-chat---*/
.theme-primary .direct-chat-primary .right > .direct-chat-text p {
  background-color: #3246D3;
  color: #ffffff; }
.theme-primary .direct-chat-primary .right > .direct-chat-text:before, .theme-primary .direct-chat-primary .right > .direct-chat-text:after {
  border-left-color: #3246D3; }
.theme-primary .direct-chat-info .right > .direct-chat-text p {
  background-color: #00D0FF;
  color: #ffffff; }
.theme-primary .direct-chat-info .right > .direct-chat-text:before, .theme-primary .direct-chat-info .right > .direct-chat-text:after {
  border-left-color: #00D0FF; }
.theme-primary .direct-chat-success .right > .direct-chat-text p {
  background-color: #1dbfc1;
  color: #ffffff; }
.theme-primary .direct-chat-success .right > .direct-chat-text:before, .theme-primary .direct-chat-success .right > .direct-chat-text:after {
  border-left-color: #1dbfc1; }
.theme-primary .direct-chat-danger .right > .direct-chat-text p {
  background-color: #ee3158;
  color: #ffffff; }
.theme-primary .direct-chat-danger .right > .direct-chat-text:before, .theme-primary .direct-chat-danger .right > .direct-chat-text:after {
  border-left-color: #ee3158; }
.theme-primary .direct-chat-warning .right > .direct-chat-text p {
  background-color: #ffa800;
  color: #ffffff; }
.theme-primary .direct-chat-warning .right > .direct-chat-text:before, .theme-primary .direct-chat-warning .right > .direct-chat-text:after {
  border-left-color: #ffa800; }
.theme-primary .right .direct-chat-text p {
  background-color: #3246D3; }

/*---modal---*/
.theme-primary .modal-primary .modal-footer, .theme-primary .modal-primary .modal-header {
  border-color: #3246D3; }
.theme-primary .modal-primary .modal-body {
  background-color: #3246D3 !important; }
.theme-primary .modal-info .modal-footer, .theme-primary .modal-info .modal-header {
  border-color: #00D0FF; }
.theme-primary .modal-info .modal-body {
  background-color: #00D0FF !important; }
.theme-primary .modal-success .modal-footer, .theme-primary .modal-success .modal-header {
  border-color: #1dbfc1; }
.theme-primary .modal-success .modal-body {
  background-color: #1dbfc1 !important; }
.theme-primary .modal-danger .modal-footer, .theme-primary .modal-danger .modal-header {
  border-color: #ee3158; }
.theme-primary .modal-danger .modal-body {
  background-color: #ee3158 !important; }
.theme-primary .modal-warning .modal-footer, .theme-primary .modal-warning .modal-header {
  border-color: #ffa800; }
.theme-primary .modal-warning .modal-body {
  background-color: #ffa800 !important; }

/*---border---*/
.theme-primary .border-primary {
  border-color: #3246D3 !important; }
.theme-primary .border-info {
  border-color: #00D0FF !important; }
.theme-primary .border-success {
  border-color: #1dbfc1 !important; }
.theme-primary .border-danger {
  border-color: #ee3158 !important; }
.theme-primary .border-warning {
  border-color: #ffa800 !important; }

/*---Background---*/
.theme-primary .bg-primary {
  background-color: #3246D3 !important;
  color: #ffffff; }
.theme-primary .bg-primary-light {
  background-color: #dbdfff !important;
  color: #3246D3; }
.theme-primary .bg-info {
  background-color: #00D0FF !important;
  color: #ffffff; }
.theme-primary .bg-info-light {
  background-color: #e1f9ff !important;
  color: #00D0FF; }
.theme-primary .bg-success {
  background-color: #1dbfc1 !important;
  color: #ffffff; }
.theme-primary .bg-success-light {
  background-color: #e8f9f9 !important;
  color: #1dbfc1; }
.theme-primary .bg-danger {
  background-color: #ee3158 !important;
  color: #ffffff; }
.theme-primary .bg-danger-light {
  background-color: #ffd6de !important;
  color: #ee3158; }
.theme-primary .bg-warning {
  background-color: #ffa800 !important;
  color: #ffffff; }
.theme-primary .bg-warning-light {
  background-color: #fff8ea !important;
  color: #ffa800; }

/*---text---*/
.theme-primary .text-primary {
  color: #3246D3 !important; }
.theme-primary a.text-primary:hover, .theme-primary a.text-primary:focus {
  color: #3246D3 !important; }
.theme-primary .hover-primary:hover, .theme-primary .hover-primary:focus {
  color: #3246D3 !important; }
.theme-primary .text-info {
  color: #00D0FF !important; }
.theme-primary a.text-info:hover, .theme-primary a.text-info:focus {
  color: #00D0FF !important; }
.theme-primary .hover-info:hover, .theme-primary .hover-info:focus {
  color: #00D0FF !important; }
.theme-primary .text-success {
  color: #1dbfc1 !important; }
.theme-primary a.text-success:hover, .theme-primary a.text-success:focus {
  color: #1dbfc1 !important; }
.theme-primary .hover-success:hover, .theme-primary .hover-success:focus {
  color: #1dbfc1 !important; }
.theme-primary .text-danger {
  color: #ee3158 !important; }
.theme-primary a.text-danger:hover, .theme-primary a.text-danger:focus {
  color: #ee3158 !important; }
.theme-primary .hover-danger:hover, .theme-primary .hover-danger:focus {
  color: #ee3158 !important; }
.theme-primary .text-warning {
  color: #ffa800 !important; }
.theme-primary a.text-warning:hover, .theme-primary a.text-warning:focus {
  color: #ffa800 !important; }
.theme-primary .hover-warning:hover, .theme-primary .hover-warning:focus {
  color: #ffa800 !important; }

/*---active background---*/
.theme-primary .active.active-primary {
  background-color: #2536ad !important; }
.theme-primary .active.active-info {
  background-color: #00a6cc !important; }
.theme-primary .active.active-success {
  background-color: #169395 !important; }
.theme-primary .active.active-danger {
  background-color: #da123b !important; }
.theme-primary .active.active-warning {
  background-color: #cc8600 !important; }

/*---label background---*/
.theme-primary .label-primary {
  background-color: #3246D3 !important; }
.theme-primary .label-info {
  background-color: #00D0FF !important; }
.theme-primary .label-success {
  background-color: #1dbfc1 !important; }
.theme-primary .label-danger {
  background-color: #ee3158 !important; }
.theme-primary .label-warning {
  background-color: #ffa800 !important; }

/*---ribbon---*/
.theme-primary .ribbon-box .ribbon-primary {
  background-color: #3246D3; }
  .theme-primary .ribbon-box .ribbon-primary:before {
    border-color: #3246D3 transparent transparent; }
.theme-primary .ribbon-box .ribbon-two-primary span {
  background-color: #3246D3; }
  .theme-primary .ribbon-box .ribbon-two-primary span:before {
    border-left: 3px solid #2536ad;
    border-top: 3px solid #2536ad; }
  .theme-primary .ribbon-box .ribbon-two-primary span:after {
    border-right: 3px solid #2536ad;
    border-top: 3px solid #2536ad; }
.theme-primary .ribbon-box .ribbon-info {
  background-color: #00D0FF; }
  .theme-primary .ribbon-box .ribbon-info:before {
    border-color: #00D0FF transparent transparent; }
.theme-primary .ribbon-box .ribbon-two-info span {
  background-color: #00D0FF; }
  .theme-primary .ribbon-box .ribbon-two-info span:before {
    border-left: 3px solid #00a6cc;
    border-top: 3px solid #00a6cc; }
  .theme-primary .ribbon-box .ribbon-two-info span:after {
    border-right: 3px solid #00a6cc;
    border-top: 3px solid #00a6cc; }
.theme-primary .ribbon-box .ribbon-success {
  background-color: #1dbfc1; }
  .theme-primary .ribbon-box .ribbon-success:before {
    border-color: #1dbfc1 transparent transparent; }
.theme-primary .ribbon-box .ribbon-two-success span {
  background-color: #1dbfc1; }
  .theme-primary .ribbon-box .ribbon-two-success span:before {
    border-left: 3px solid #169395;
    border-top: 3px solid #169395; }
  .theme-primary .ribbon-box .ribbon-two-success span:after {
    border-right: 3px solid #169395;
    border-top: 3px solid #169395; }
.theme-primary .ribbon-box .ribbon-danger {
  background-color: #ee3158; }
  .theme-primary .ribbon-box .ribbon-danger:before {
    border-color: #ee3158 transparent transparent; }
.theme-primary .ribbon-box .ribbon-two-danger span {
  background-color: #ee3158; }
  .theme-primary .ribbon-box .ribbon-two-danger span:before {
    border-left: 3px solid #da123b;
    border-top: 3px solid #da123b; }
  .theme-primary .ribbon-box .ribbon-two-danger span:after {
    border-right: 3px solid #da123b;
    border-top: 3px solid #da123b; }
.theme-primary .ribbon-box .ribbon-warning {
  background-color: #ffa800; }
  .theme-primary .ribbon-box .ribbon-warning:before {
    border-color: #ffa800 transparent transparent; }
.theme-primary .ribbon-box .ribbon-two-warning span {
  background-color: #ffa800; }
  .theme-primary .ribbon-box .ribbon-two-warning span:before {
    border-left: 3px solid #cc8600;
    border-top: 3px solid #cc8600; }
  .theme-primary .ribbon-box .ribbon-two-warning span:after {
    border-right: 3px solid #cc8600;
    border-top: 3px solid #cc8600; }

/*---Box---*/
.theme-primary .box-primary {
  background-color: #3246D3 !important; }
  .theme-primary .box-primary.box-bordered {
    border-color: #3246D3; }
.theme-primary .box-outline-primary {
  background-color: #ffffff;
  border: 1px solid #3246D3; }
.theme-primary .box.box-solid.box-primary > .box-header {
  color: #ffffff;
  background-color: #3246D3; }
  .theme-primary .box.box-solid.box-primary > .box-header .btn {
    color: #ffffff; }
  .theme-primary .box.box-solid.box-primary > .box-header > a {
    color: #ffffff; }
.theme-primary .box-info {
  background-color: #00D0FF !important; }
  .theme-primary .box-info.box-bordered {
    border-color: #00D0FF; }
.theme-primary .box-outline-info {
  background-color: #ffffff;
  border: 1px solid #00D0FF; }
.theme-primary .box.box-solid.box-info > .box-header {
  color: #ffffff;
  background-color: #00D0FF; }
  .theme-primary .box.box-solid.box-info > .box-header .btn {
    color: #ffffff; }
  .theme-primary .box.box-solid.box-info > .box-header > a {
    color: #ffffff; }
.theme-primary .box-success {
  background-color: #1dbfc1 !important; }
  .theme-primary .box-success.box-bordered {
    border-color: #1dbfc1; }
.theme-primary .box-outline-success {
  background-color: #ffffff;
  border: 1px solid #1dbfc1; }
.theme-primary .box.box-solid.box-success > .box-header {
  color: #ffffff;
  background-color: #1dbfc1; }
  .theme-primary .box.box-solid.box-success > .box-header .btn {
    color: #ffffff; }
  .theme-primary .box.box-solid.box-success > .box-header > a {
    color: #ffffff; }
.theme-primary .box-danger {
  background-color: #ee3158 !important; }
  .theme-primary .box-danger.box-bordered {
    border-color: #ee3158; }
.theme-primary .box-outline-danger {
  background-color: #ffffff;
  border: 1px solid #ee3158; }
.theme-primary .box.box-solid.box-danger > .box-header {
  color: #ffffff;
  background-color: #ee3158; }
  .theme-primary .box.box-solid.box-danger > .box-header .btn {
    color: #ffffff; }
  .theme-primary .box.box-solid.box-danger > .box-header > a {
    color: #ffffff; }
.theme-primary .box-warning {
  background-color: #ffa800 !important; }
  .theme-primary .box-warning.box-bordered {
    border-color: #ffa800; }
.theme-primary .box-outline-warning {
  background-color: #ffffff;
  border: 1px solid #ffa800; }
.theme-primary .box.box-solid.box-warning > .box-header {
  color: #ffffff;
  background-color: #ffa800; }
  .theme-primary .box.box-solid.box-warning > .box-header .btn {
    color: #ffffff; }
  .theme-primary .box.box-solid.box-warning > .box-header > a {
    color: #ffffff; }
.theme-primary .box-profile .social-states a:hover {
  color: #2536ad; }
.theme-primary .box-controls li > a:hover {
  color: #2536ad; }
.theme-primary .box-controls .dropdown.show > a {
  color: #2536ad; }
.theme-primary .box-fullscreen .box-btn-fullscreen {
  color: #2536ad; }

/*---progress bar---*/
.theme-primary .progress-bar-primary {
  background-color: #3246D3; }
.theme-primary .progress-bar-info {
  background-color: #00D0FF; }
.theme-primary .progress-bar-success {
  background-color: #1dbfc1; }
.theme-primary .progress-bar-danger {
  background-color: #ee3158; }
.theme-primary .progress-bar-warning {
  background-color: #ffa800; }

/*---panel---*/
.theme-primary .panel-primary {
  border-color: #3246D3; }
  .theme-primary .panel-primary > .panel-heading {
    color: #ffffff;
    background-color: #3246D3;
    border-color: #3246D3; }
    .theme-primary .panel-primary > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #3246D3; }
    .theme-primary .panel-primary > .panel-heading .badge-pill {
      color: #3246D3;
      background-color: #ffffff; }
  .theme-primary .panel-primary .panel-title, .theme-primary .panel-primary .panel-action {
    color: #ffffff; }
  .theme-primary .panel-primary .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #3246D3; }
.theme-primary .panel-line.panel-primary .panel-heading {
  color: #3246D3;
  border-top-color: #3246D3;
  background: transparent; }
.theme-primary .panel-line.panel-primary .panel-title, .theme-primary .panel-line.panel-primary .panel-action {
  color: #3246D3; }
.theme-primary .panel-info {
  border-color: #00D0FF; }
  .theme-primary .panel-info > .panel-heading {
    color: #ffffff;
    background-color: #00D0FF;
    border-color: #00D0FF; }
    .theme-primary .panel-info > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #00D0FF; }
    .theme-primary .panel-info > .panel-heading .badge-pill {
      color: #00D0FF;
      background-color: #ffffff; }
  .theme-primary .panel-info .panel-title, .theme-primary .panel-info .panel-action {
    color: #ffffff; }
  .theme-primary .panel-info .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #00D0FF; }
.theme-primary .panel-line.panel-info .panel-heading {
  color: #00D0FF;
  border-top-color: #00D0FF;
  background: transparent; }
.theme-primary .panel-line.panel-info .panel-title, .theme-primary .panel-line.panel-info .panel-action {
  color: #00D0FF; }
.theme-primary .panel-success {
  border-color: #1dbfc1; }
  .theme-primary .panel-success > .panel-heading {
    color: #ffffff;
    background-color: #1dbfc1;
    border-color: #1dbfc1; }
    .theme-primary .panel-success > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #1dbfc1; }
    .theme-primary .panel-success > .panel-heading .badge-pill {
      color: #1dbfc1;
      background-color: #ffffff; }
  .theme-primary .panel-success .panel-title, .theme-primary .panel-success .panel-action {
    color: #ffffff; }
  .theme-primary .panel-success .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #1dbfc1; }
.theme-primary .panel-line.panel-success .panel-heading {
  color: #1dbfc1;
  border-top-color: #1dbfc1;
  background: transparent; }
.theme-primary .panel-line.panel-success .panel-title, .theme-primary .panel-line.panel-success .panel-action {
  color: #1dbfc1; }
.theme-primary .panel-danger {
  border-color: #ee3158; }
  .theme-primary .panel-danger > .panel-heading {
    color: #ffffff;
    background-color: #ee3158;
    border-color: #ee3158; }
    .theme-primary .panel-danger > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #ee3158; }
    .theme-primary .panel-danger > .panel-heading .badge-pill {
      color: #ee3158;
      background-color: #ffffff; }
  .theme-primary .panel-danger .panel-title, .theme-primary .panel-danger .panel-action {
    color: #ffffff; }
  .theme-primary .panel-danger .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #ee3158; }
.theme-primary .panel-line.panel-danger .panel-heading {
  color: #ee3158;
  border-top-color: #ee3158;
  background: transparent; }
.theme-primary .panel-line.panel-danger .panel-title, .theme-primary .panel-line.panel-danger .panel-action {
  color: #ee3158; }
.theme-primary .panel-warning {
  border-color: #ffa800; }
  .theme-primary .panel-warning > .panel-heading {
    color: #ffffff;
    background-color: #ffa800;
    border-color: #ffa800; }
    .theme-primary .panel-warning > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #ffa800; }
    .theme-primary .panel-warning > .panel-heading .badge-pill {
      color: #ffa800;
      background-color: #ffffff; }
  .theme-primary .panel-warning .panel-title, .theme-primary .panel-warning .panel-action {
    color: #ffffff; }
  .theme-primary .panel-warning .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #ffa800; }
.theme-primary .panel-line.panel-warning .panel-heading {
  color: #ffa800;
  border-top-color: #ffa800;
  background: transparent; }
.theme-primary .panel-line.panel-warning .panel-title, .theme-primary .panel-line.panel-warning .panel-action {
  color: #ffa800; }

/*---switch---*/
.theme-primary .switch input:checked ~ .switch-indicator::after {
  background-color: #3246D3; }
.theme-primary .switch.switch-primary input:checked ~ .switch-indicator::after {
  background-color: #3246D3; }
.theme-primary .switch.switch-info input:checked ~ .switch-indicator::after {
  background-color: #00D0FF; }
.theme-primary .switch.switch-success input:checked ~ .switch-indicator::after {
  background-color: #1dbfc1; }
.theme-primary .switch.switch-danger input:checked ~ .switch-indicator::after {
  background-color: #ee3158; }
.theme-primary .switch.switch-warning input:checked ~ .switch-indicator::after {
  background-color: #ffa800; }

/*---badge---*/
.theme-primary .badge-primary {
  background-color: #3246D3;
  color: #ffffff; }
.theme-primary .badge-primary[href]:hover, .theme-primary .badge-primary[href]:focus {
  background-color: #2536ad; }
.theme-primary .badge-secondary {
  background-color: #e4e6ef;
  color: #172b4c; }
.theme-primary .badge-secondary[href]:hover, .theme-primary .badge-secondary[href]:focus {
  background-color: #c4c8dc; }
.theme-primary .badge-info {
  background-color: #00D0FF;
  color: #ffffff; }
.theme-primary .badge-info[href]:hover, .theme-primary .badge-info[href]:focus {
  background-color: #00a6cc; }
.theme-primary .badge-success {
  background-color: #1dbfc1;
  color: #ffffff; }
.theme-primary .badge-success[href]:hover, .theme-primary .badge-success[href]:focus {
  background-color: #169395; }
.theme-primary .badge-danger {
  background-color: #ee3158;
  color: #ffffff; }
.theme-primary .badge-danger[href]:hover, .theme-primary .badge-danger[href]:focus {
  background-color: #da123b; }
.theme-primary .badge-warning {
  background-color: #ffa800;
  color: #ffffff; }
.theme-primary .badge-warning[href]:hover, .theme-primary .badge-warning[href]:focus {
  background-color: #cc8600; }

/*---badge light---*/
.theme-primary .badge-primary-light {
  background-color: #dbdfff;
  color: #3246D3; }
.theme-primary .badge-primary-light[href]:hover, .theme-primary .badge-primary-light[href]:focus {
  background-color: #a8b2ff; }
.theme-primary .badge-secondary-light {
  background-color: #e9edf2;
  color: #172b4c; }
.theme-primary .badge-secondary-light[href]:hover, .theme-primary .badge-secondary-light[href]:focus {
  background-color: #c9d3df; }
.theme-primary .badge-info-light {
  background-color: #e1f9ff;
  color: #00D0FF; }
.theme-primary .badge-info-light[href]:hover, .theme-primary .badge-info-light[href]:focus {
  background-color: #aeefff; }
.theme-primary .badge-success-light {
  background-color: #e8f9f9;
  color: #1dbfc1; }
.theme-primary .badge-success-light[href]:hover, .theme-primary .badge-success-light[href]:focus {
  background-color: #c0eeee; }
.theme-primary .badge-danger-light {
  background-color: #ffd6de;
  color: #ee3158; }
.theme-primary .badge-danger-light[href]:hover, .theme-primary .badge-danger-light[href]:focus {
  background-color: #ffa3b5; }
.theme-primary .badge-warning-light {
  background-color: #fff8ea;
  color: #ffa800; }
.theme-primary .badge-warning-light[href]:hover, .theme-primary .badge-warning-light[href]:focus {
  background-color: #ffe7b7; }

/*---rating---*/
.theme-primary .rating-primary .active {
  color: #3246D3; }
.theme-primary .rating-primary :checked ~ label {
  color: #3246D3; }
.theme-primary .rating-primary label:hover {
  color: #3246D3; }
  .theme-primary .rating-primary label:hover ~ label {
    color: #3246D3; }
.theme-primary .rating-info .active {
  color: #00D0FF; }
.theme-primary .rating-info :checked ~ label {
  color: #00D0FF; }
.theme-primary .rating-info label:hover {
  color: #00D0FF; }
  .theme-primary .rating-info label:hover ~ label {
    color: #00D0FF; }
.theme-primary .rating-success .active {
  color: #1dbfc1; }
.theme-primary .rating-success :checked ~ label {
  color: #1dbfc1; }
.theme-primary .rating-success label:hover {
  color: #1dbfc1; }
  .theme-primary .rating-success label:hover ~ label {
    color: #1dbfc1; }
.theme-primary .rating-danger .active {
  color: #ee3158; }
.theme-primary .rating-danger :checked ~ label {
  color: #ee3158; }
.theme-primary .rating-danger label:hover {
  color: #ee3158; }
  .theme-primary .rating-danger label:hover ~ label {
    color: #ee3158; }
.theme-primary .rating-warning .active {
  color: #ffa800; }
.theme-primary .rating-warning :checked ~ label {
  color: #ffa800; }
.theme-primary .rating-warning label:hover {
  color: #ffa800; }
  .theme-primary .rating-warning label:hover ~ label {
    color: #ffa800; }

/*---toggler---*/
.theme-primary .toggler-primary input:checked + i {
  color: #3246D3; }
.theme-primary .toggler-info input:checked + i {
  color: #00D0FF; }
.theme-primary .toggler-success input:checked + i {
  color: #1dbfc1; }
.theme-primary .toggler-danger input:checked + i {
  color: #ee3158; }
.theme-primary .toggler-warning input:checked + i {
  color: #ffa800; }

/*---nav tabs---*/
.theme-primary .nav-tabs.nav-tabs-primary .nav-link:hover, .theme-primary .nav-tabs.nav-tabs-primary .nav-link:active, .theme-primary .nav-tabs.nav-tabs-primary .nav-link:focus, .theme-primary .nav-tabs.nav-tabs-primary .nav-link.active {
  border-color: #2536ad;
  background-color: transparent;
  color: #2536ad; }
.theme-primary .nav-tabs.nav-tabs-info .nav-link:hover, .theme-primary .nav-tabs.nav-tabs-info .nav-link:active, .theme-primary .nav-tabs.nav-tabs-info .nav-link:focus, .theme-primary .nav-tabs.nav-tabs-info .nav-link.active {
  border-color: #00a6cc;
  background-color: #00D0FF;
  color: #ffffff; }
.theme-primary .nav-tabs.nav-tabs-success .nav-link:hover, .theme-primary .nav-tabs.nav-tabs-success .nav-link:active, .theme-primary .nav-tabs.nav-tabs-success .nav-link:focus, .theme-primary .nav-tabs.nav-tabs-success .nav-link.active {
  border-color: #169395;
  background-color: transparent;
  color: #169395; }
.theme-primary .nav-tabs.nav-tabs-danger .nav-link:hover, .theme-primary .nav-tabs.nav-tabs-danger .nav-link:active, .theme-primary .nav-tabs.nav-tabs-danger .nav-link:focus, .theme-primary .nav-tabs.nav-tabs-danger .nav-link.active {
  border-color: #da123b;
  background-color: transparent;
  color: #da123b; }
.theme-primary .nav-tabs.nav-tabs-warning .nav-link:hover, .theme-primary .nav-tabs.nav-tabs-warning .nav-link:active, .theme-primary .nav-tabs.nav-tabs-warning .nav-link:focus, .theme-primary .nav-tabs.nav-tabs-warning .nav-link.active {
  border-color: #cc8600;
  background-color: transparent;
  color: #cc8600; }
.theme-primary .nav-tabs-custom.tab-primary > .nav-tabs > li a.active {
  border-top-color: #2536ad; }
.theme-primary .nav-tabs-custom.tab-info > .nav-tabs > li a.active {
  border-top-color: #00a6cc; }
.theme-primary .nav-tabs-custom.tab-success > .nav-tabs > li a.active {
  border-top-color: #169395; }
.theme-primary .nav-tabs-custom.tab-danger > .nav-tabs > li a.active {
  border-top-color: #da123b; }
.theme-primary .nav-tabs-custom.tab-warning > .nav-tabs > li a.active {
  border-top-color: #cc8600; }
.theme-primary .nav-tabs .nav-link.active {
  border-bottom-color: #3246D3;
  background-color: #3246D3;
  color: #ffffff; }
  .theme-primary .nav-tabs .nav-link.active:hover, .theme-primary .nav-tabs .nav-link.active:focus {
    border-bottom-color: #3246D3;
    background-color: #3246D3;
    color: #ffffff; }
.theme-primary .nav-tabs .nav-item.open .nav-link {
  border-bottom-color: #3246D3;
  background-color: #3246D3; }
  .theme-primary .nav-tabs .nav-item.open .nav-link:hover, .theme-primary .nav-tabs .nav-item.open .nav-link:focus {
    border-bottom-color: #3246D3;
    background-color: #3246D3; }

/*---todo---*/
.theme-primary .todo-list .primary {
  border-left-color: #3246D3; }
.theme-primary .todo-list .info {
  border-left-color: #3246D3; }
.theme-primary .todo-list .success {
  border-left-color: #1dbfc1; }
.theme-primary .todo-list .danger {
  border-left-color: #ee3158; }
.theme-primary .todo-list .warning {
  border-left-color: #ffa800; }

/*---timeline---*/
.theme-primary .timeline .timeline-item > .timeline-event.timeline-event-primary {
  background-color: #3246D3;
  border: 1px solid #3246D3;
  color: #ffffff; }
  .theme-primary .timeline .timeline-item > .timeline-event.timeline-event-primary:before, .theme-primary .timeline .timeline-item > .timeline-event.timeline-event-primary:after {
    border-left-color: #3246D3;
    border-right-color: #3246D3; }
  .theme-primary .timeline .timeline-item > .timeline-event.timeline-event-primary * {
    color: inherit; }
.theme-primary .timeline .timeline-item > .timeline-event.timeline-event-info {
  background-color: #00D0FF;
  border: 1px solid #00D0FF;
  color: #ffffff; }
  .theme-primary .timeline .timeline-item > .timeline-event.timeline-event-info:before, .theme-primary .timeline .timeline-item > .timeline-event.timeline-event-info:after {
    border-left-color: #00D0FF;
    border-right-color: #00D0FF; }
  .theme-primary .timeline .timeline-item > .timeline-event.timeline-event-info * {
    color: inherit; }
.theme-primary .timeline .timeline-item > .timeline-event.timeline-event-success {
  background-color: #1dbfc1;
  border: 1px solid #1dbfc1;
  color: #ffffff; }
  .theme-primary .timeline .timeline-item > .timeline-event.timeline-event-success:before, .theme-primary .timeline .timeline-item > .timeline-event.timeline-event-success:after {
    border-left-color: #1dbfc1;
    border-right-color: #1dbfc1; }
  .theme-primary .timeline .timeline-item > .timeline-event.timeline-event-success * {
    color: inherit; }
.theme-primary .timeline .timeline-item > .timeline-event.timeline-event-danger {
  background-color: #ee3158;
  border: 1px solid #ee3158;
  color: #ffffff; }
  .theme-primary .timeline .timeline-item > .timeline-event.timeline-event-danger:before, .theme-primary .timeline .timeline-item > .timeline-event.timeline-event-danger:after {
    border-left-color: #ee3158;
    border-right-color: #ee3158; }
  .theme-primary .timeline .timeline-item > .timeline-event.timeline-event-danger * {
    color: inherit; }
.theme-primary .timeline .timeline-item > .timeline-event.timeline-event-warning {
  background-color: #ffa800;
  border: 1px solid #ffa800;
  color: #ffffff; }
  .theme-primary .timeline .timeline-item > .timeline-event.timeline-event-warning:before, .theme-primary .timeline .timeline-item > .timeline-event.timeline-event-warning:after {
    border-left-color: #ffa800;
    border-right-color: #ffa800; }
  .theme-primary .timeline .timeline-item > .timeline-event.timeline-event-warning * {
    color: inherit; }
.theme-primary .timeline .timeline-item > .timeline-point.timeline-point-primary {
  color: #3246D3;
  background-color: #ffffff; }
.theme-primary .timeline .timeline-item > .timeline-point.timeline-point-info {
  color: #00D0FF;
  background-color: #ffffff; }
.theme-primary .timeline .timeline-item > .timeline-point.timeline-point-success {
  color: #1dbfc1;
  background-color: #ffffff; }
.theme-primary .timeline .timeline-item > .timeline-point.timeline-point-danger {
  color: #ee3158;
  background-color: #ffffff; }
.theme-primary .timeline .timeline-item > .timeline-point.timeline-point-warning {
  color: #ffa800;
  background-color: #ffffff; }
.theme-primary .timeline .timeline-label .label-primary {
  background-color: #3246D3; }
.theme-primary .timeline .timeline-label .label-info {
  background-color: #00D0FF; }
.theme-primary .timeline .timeline-label .label-success {
  background-color: #1dbfc1; }
.theme-primary .timeline .timeline-label .label-danger {
  background-color: #ee3158; }
.theme-primary .timeline .timeline-label .label-warning {
  background-color: #ffa800; }
.theme-primary .timeline__year, .theme-primary .timeline5:before, .theme-primary .timeline__box:before, .theme-primary .timeline__date {
  background-color: #3246D3; }
.theme-primary .timeline__post {
  border-left: 3px solid #3246D3; }

/*---daterangepicker---*/
.theme-primary .daterangepicker td.active {
  background-color: #3246D3; }
  .theme-primary .daterangepicker td.active:hover {
    background-color: #3246D3; }
.theme-primary .daterangepicker .input-mini.active {
  border: 1px solid #3246D3; }
.theme-primary .ranges li:hover, .theme-primary .ranges li:active, .theme-primary .ranges li.active {
  border: 1px solid #3246D3;
  background-color: #3246D3; }

/*---control-sidebar---*/
.theme-primary .control-sidebar .nav-tabs.control-sidebar-tabs > li > a:hover, .theme-primary .control-sidebar .nav-tabs.control-sidebar-tabs > li > a:active, .theme-primary .control-sidebar .nav-tabs.control-sidebar-tabs > li > a:focus {
  border-color: #3246D3;
  color: #3246D3; }
.theme-primary .control-sidebar .nav-tabs.control-sidebar-tabs > li > a.active {
  border-color: #3246D3;
  color: #3246D3; }
  .theme-primary .control-sidebar .nav-tabs.control-sidebar-tabs > li > a.active:hover, .theme-primary .control-sidebar .nav-tabs.control-sidebar-tabs > li > a.active:active, .theme-primary .control-sidebar .nav-tabs.control-sidebar-tabs > li > a.active:focus {
    border-color: #3246D3;
    color: #3246D3; }
.theme-primary .control-sidebar .rpanel-title .btn:hover {
  color: #3246D3; }

/*---nav---*/
.theme-primary .nav > li > a:hover, .theme-primary .nav > li > a:active, .theme-primary .nav > li > a:focus {
  color: #3246D3; }
.theme-primary .nav-pills > li > a.active {
  border-top-color: #3246D3;
  background-color: #3246D3 !important;
  color: #ffffff; }
  .theme-primary .nav-pills > li > a.active:hover, .theme-primary .nav-pills > li > a.active:focus {
    border-top-color: #3246D3;
    background-color: #3246D3 !important;
    color: #ffffff; }
.theme-primary .mailbox-nav .nav-pills > li > a:hover, .theme-primary .mailbox-nav .nav-pills > li > a:focus {
  border-color: #3246D3; }
.theme-primary .mailbox-nav .nav-pills > li > a.active {
  border-color: #3246D3; }
  .theme-primary .mailbox-nav .nav-pills > li > a.active:hover, .theme-primary .mailbox-nav .nav-pills > li > a.active:focus {
    border-color: #3246D3; }
.theme-primary .nav-tabs-custom > .nav-tabs > li a.active {
  border-top-color: #3246D3; }
.theme-primary .profile-tab li a.nav-link.active {
  border-bottom: 2px solid #3246D3; }
.theme-primary .customtab li a.nav-link.active {
  border-bottom: 2px solid #3246D3; }

/*---form-element---*/
.theme-primary .form-element .input-group .input-group-addon {
  background-image: linear-gradient(45deg, #3246D3, #00D0FF), linear-gradient(#3b6dc1, #3b6dc1); }
.theme-primary .form-element .form-control {
  background-image: linear-gradient(45deg, #3246D3, #00D0FF), linear-gradient(#3b6dc1, #3b6dc1); }
  .theme-primary .form-element .form-control:focus {
    background-image: linear-gradient(45deg, #3246D3, #00D0FF), linear-gradient(#3b6dc1, #3b6dc1); }
.theme-primary .form-control:focus {
  border-color: #3246D3; }
.theme-primary [type=checkbox]:checked.chk-col-primary + label:before {
  border-right: 1px solid #3246D3;
  border-bottom: 1px solid #3246D3; }
.theme-primary [type=checkbox]:checked.chk-col-info + label:before {
  border-right: 1px solid #00D0FF;
  border-bottom: 1px solid #00D0FF; }
.theme-primary [type=checkbox]:checked.chk-col-success + label:before {
  border-right: 1px solid #1dbfc1;
  border-bottom: 1px solid #1dbfc1; }
.theme-primary [type=checkbox]:checked.chk-col-danger + label:before {
  border-right: 1px solid #ee3158;
  border-bottom: 1px solid #ee3158; }
.theme-primary [type=checkbox]:checked.chk-col-warning + label:before {
  border-right: 1px solid #ffa800;
  border-bottom: 1px solid #ffa800; }
.theme-primary [type=checkbox].filled-in:checked.chk-col-primary + label:after {
  border: 1px solid #3246D3;
  background-color: #3246D3; }
.theme-primary [type=checkbox].filled-in:checked.chk-col-info + label:after {
  border: 1px solid #00D0FF;
  background-color: #00D0FF; }
.theme-primary [type=checkbox].filled-in:checked.chk-col-success + label:after {
  border: 1px solid #1dbfc1;
  background-color: #1dbfc1; }
.theme-primary [type=checkbox].filled-in:checked.chk-col-danger + label:after {
  border: 1px solid #ee3158;
  background-color: #ee3158; }
.theme-primary [type=checkbox].filled-in:checked.chk-col-warning + label:after {
  border: 1px solid #ffa800;
  background-color: #ffa800; }
.theme-primary [type=radio].radio-col-primary:checked + label:after {
  background-color: #3246D3;
  border-color: #3246D3;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-primary [type=radio].with-gap.radio-col-primary:checked + label:before {
  border: 1px solid #3246D3;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-primary [type=radio].with-gap.radio-col-primary:checked + label:after {
  background-color: #3246D3;
  border: 1px solid #3246D3;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-primary [type=radio].radio-col-info:checked + label:after {
  background-color: #00D0FF;
  border-color: #00D0FF;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-primary [type=radio].with-gap.radio-col-info:checked + label:before {
  border: 1px solid #00D0FF;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-primary [type=radio].with-gap.radio-col-info:checked + label:after {
  background-color: #00D0FF;
  border: 1px solid #00D0FF;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-primary [type=radio].radio-col-success:checked + label:after {
  background-color: #1dbfc1;
  border-color: #1dbfc1;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-primary [type=radio].with-gap.radio-col-success:checked + label:before {
  border: 1px solid #1dbfc1;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-primary [type=radio].with-gap.radio-col-success:checked + label:after {
  background-color: #1dbfc1;
  border: 1px solid #1dbfc1;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-primary [type=radio].radio-col-danger:checked + label:after {
  background-color: #ee3158;
  border-color: #ee3158;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-primary [type=radio].with-gap.radio-col-danger:checked + label:before {
  border: 1px solid #ee3158;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-primary [type=radio].with-gap.radio-col-danger:checked + label:after {
  background-color: #ee3158;
  border: 1px solid #ee3158;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-primary [type=radio].radio-col-warning:checked + label:after {
  background-color: #ffa800;
  border-color: #ffa800;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-primary [type=radio].with-gap.radio-col-warning:checked + label:before {
  border: 1px solid #ffa800;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-primary [type=radio].with-gap.radio-col-warning:checked + label:after {
  background-color: #ffa800;
  border: 1px solid #ffa800;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-primary [type=checkbox]:checked + label:before {
  border-right: 1px solid #3246D3;
  border-bottom: 1px solid #3246D3; }
.theme-primary [type=checkbox].filled-in:checked + label:after {
  border: 1px solid #3246D3;
  background-color: #3246D3; }
.theme-primary [type=radio].with-gap:checked + label:before, .theme-primary [type=radio].with-gap:checked + label:after {
  border: 1px solid #3246D3; }
.theme-primary [type=radio].with-gap:checked + label:after {
  background-color: #3246D3;
  z-index: 0; }
.theme-primary [type=radio]:checked + label:after {
  border: 1px solid #3246D3;
  background-color: #3246D3;
  z-index: 0; }
.theme-primary [type=checkbox].filled-in.tabbed:checked:focus + label:after {
  border-color: #3246D3;
  background-color: #3246D3; }

/*---Calender---*/
.theme-primary .fx-element-overlay .fx-card-item .fx-card-content a:hover {
  color: #3246D3; }
.theme-primary .fx-element-overlay .fx-card-item .fx-overlay-1 .fx-info > li a:hover {
  background: #3246D3;
  border-color: #3246D3; }
.theme-primary .fc-event, .theme-primary .calendar-event {
  background: #3246D3; }

/*---Tabs---*/
.theme-primary .tabs-vertical li .nav-link:hover, .theme-primary .tabs-vertical li .nav-link:active, .theme-primary .tabs-vertical li .nav-link:focus, .theme-primary .tabs-vertical li .nav-link.active {
  background-color: #3246D3;
  color: #ffffff; }
.theme-primary .customvtab .tabs-vertical li .nav-link:hover, .theme-primary .customvtab .tabs-vertical li .nav-link:active, .theme-primary .customvtab .tabs-vertical li .nav-link:focus, .theme-primary .customvtab .tabs-vertical li .nav-link.active {
  border-right: 2px solid #3246D3;
  color: #3246D3; }
.theme-primary .customtab2 li a.nav-link:hover, .theme-primary .customtab2 li a.nav-link:active, .theme-primary .customtab2 li a.nav-link.active {
  background-color: #3246D3; }

/*---Notification---*/
.theme-primary .jq-icon-primary {
  background-color: #3246D3;
  color: #ffffff;
  border-color: #3246D3; }
.theme-primary .jq-icon-info {
  background-color: #00D0FF;
  color: #ffffff;
  border-color: #00D0FF; }
.theme-primary .jq-icon-success {
  background-color: #1dbfc1;
  color: #ffffff;
  border-color: #3246D3; }
.theme-primary .jq-icon-error {
  background-color: #ee3158;
  color: #ffffff;
  border-color: #ee3158; }
.theme-primary .jq-icon-danger {
  background-color: #ee3158;
  color: #ffffff;
  border-color: #ee3158; }
.theme-primary .jq-icon-warning {
  background-color: #ffa800;
  color: #ffffff;
  border-color: #ffa800; }

/*---avatar---*/
.theme-primary .avatar.status-primary::after {
  background-color: #3246D3; }
.theme-primary .avatar.status-info::after {
  background-color: #00D0FF; }
.theme-primary .avatar.status-success::after {
  background-color: #1dbfc1; }
.theme-primary .avatar.status-danger::after {
  background-color: #ee3158; }
.theme-primary .avatar.status-warning::after {
  background-color: #ffa800; }
.theme-primary .avatar[class*='status-']::after {
  background-color: #3246D3; }
.theme-primary .avatar-add:hover {
  background-color: #2536ad;
  border-color: #2536ad; }

/*---media---*/
.theme-primary .media-chat.media-chat-reverse .media-body p {
  background-color: #3246D3; }
.theme-primary .media-right-out a:hover {
  color: #2536ad; }

/*---control---*/
.theme-primary .control input:checked:focus ~ .control_indicator {
  background-color: #3246D3; }
.theme-primary .control input:checked ~ .control_indicator {
  background-color: #3246D3; }
.theme-primary .control:hover input:not([disabled]):checked ~ .control_indicator {
  background-color: #3246D3; }

/*---flex---*/
.theme-primary .flex-column > li > a.nav-link.active {
  border-left-color: #3246D3; }
  .theme-primary .flex-column > li > a.nav-link.active:hover {
    border-left-color: #3246D3; }

/*---pagination---*/
.theme-primary .pagination li a.current {
  border: 1px solid #3246D3;
  background-color: #3246D3; }
  .theme-primary .pagination li a.current:hover {
    border: 1px solid #3246D3;
    background-color: #3246D3; }
.theme-primary .pagination li a:hover {
  border: 1px solid #2536ad;
  background-color: #2536ad !important; }
.theme-primary .dataTables_wrapper .dataTables_paginate .paginate_button.current {
  border: 1px solid #3246D3;
  background-color: #3246D3; }
  .theme-primary .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    border: 1px solid #3246D3;
    background-color: #3246D3; }
.theme-primary .paging_simple_numbers .pagination .paginate_button.active a {
  background-color: #3246D3; }
.theme-primary .paging_simple_numbers .pagination .paginate_button:hover a {
  background-color: #3246D3; }
.theme-primary .footable .pagination li a:hover, .theme-primary .footable .pagination li a:active, .theme-primary .footable .pagination li a.active {
  background-color: #3246D3; }

/*---dataTables---*/
.theme-primary .dt-buttons .dt-button {
  background-color: #3246D3; }

/*---select2---*/
.theme-primary .select2-container--default.select2-container--open {
  border-color: #3246D3; }
.theme-primary .select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: #3246D3; }
.theme-primary .select2-container--default .select2-search--dropdown .select2-search__field {
  border-color: #3246D3 !important; }
.theme-primary .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #3246D3 !important; }
.theme-primary .select2-container--default .select2-selection--multiple:focus {
  border-color: #3246D3 !important; }
.theme-primary .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #3246D3;
  border-color: #3246D3; }

/*---Other---*/
.theme-primary .myadmin-dd .dd-list .dd-list .dd-handle:hover {
  color: #2536ad; }
.theme-primary .myadmin-dd-empty .dd-list .dd3-handle:hover {
  color: #2536ad; }
.theme-primary .myadmin-dd-empty .dd-list .dd3-content:hover {
  color: #2536ad; }
.theme-primary [data-overlay-primary]::before {
  background: #2536ad; }

/*---wizard---*/
.theme-primary .wizard-content .wizard > .steps > ul > li.current {
  border: 2px solid #3246D3;
  background-color: #3246D3; }
.theme-primary .wizard-content .wizard > .steps > ul > li.done {
  border-color: #2536ad;
  background-color: #2536ad; }
.theme-primary .wizard-content .wizard > .actions > ul > li > a {
  background-color: #3246D3; }
.theme-primary .wizard-content .wizard.wizard-circle > .steps > ul > li:after {
  background-color: #3246D3; }
.theme-primary .wizard-content .wizard.wizard-circle > .steps > ul > li:before {
  background-color: #3246D3; }
.theme-primary .wizard-content .wizard.wizard-notification > .steps > ul > li:after {
  background-color: #3246D3; }
.theme-primary .wizard-content .wizard.wizard-notification > .steps > ul > li:before {
  background-color: #3246D3; }
.theme-primary .wizard-content .wizard.wizard-notification > .steps > ul > li.current .step {
  border: 2px solid #3246D3;
  color: #3246D3; }
  .theme-primary .wizard-content .wizard.wizard-notification > .steps > ul > li.current .step:after {
    border-top-color: #3246D3; }
.theme-primary .wizard-content .wizard.wizard-notification > .steps > ul > li.done .step:after {
  border-top-color: #3246D3; }

@media (max-width: 767px) {
  .theme-primary .wizard-content .wizard > .steps > ul > li:last-child:after {
    background-color: #3246D3; } }
@media (max-width: 575px) {
  .theme-primary .wizard-content .wizard > .steps > ul > li.current:after {
    background-color: #3246D3; } }
/*---slider---*/
.theme-primary #primary .slider-selection {
  background-color: #3246D3; }
.theme-primary #info .slider-selection {
  background-color: #00D0FF; }
.theme-primary #success .slider-selection {
  background-color: #1dbfc1; }
.theme-primary #danger .slider-selection {
  background-color: #ee3158; }
.theme-primary #warning .slider-selection {
  background-color: #ffa800; }

/*---horizontal-timeline---*/
.theme-primary .cd-horizontal-timeline .events a.selected::after {
  background: #3246D3;
  border-color: #3246D3; }
.theme-primary .cd-horizontal-timeline .events a.older-event::after {
  border-color: #3246D3; }
.theme-primary .cd-horizontal-timeline .filling-line {
  background: #3246D3; }
.theme-primary .cd-horizontal-timeline a {
  color: #3246D3; }
  .theme-primary .cd-horizontal-timeline a:hover, .theme-primary .cd-horizontal-timeline a:focus {
    color: #3246D3; }
.theme-primary .cd-timeline-navigation a:hover, .theme-primary .cd-timeline-navigation a:focus {
  border-color: #3246D3; }

/**************************************
Theme Secondary Color
**************************************/
.bg-gradient-secondary, .theme-secondary .bg-gradient-secondary, .theme-secondary .art-bg {
  background: linear-gradient(45deg, #e4e6ef, #00D0FF); }

.bg-light-body {
  background: transparent; }

.theme-secondary.fixed .main-header {
  background: transparent; }
.theme-secondary .main-header {
  background: transparent; }

.theme-secondary.onlyheader .art-bg {
  background-image: none; }

.bg-gradient-secondary-dark, .dark-skin.theme-secondary .bg-gradient-secondary, .dark-skin.theme-secondary .art-bg {
  background-image: linear-gradient(45deg, #a4abc9, #007d99); }

.bg-dark-body {
  background: #0c1a32; }

.dark-skin.theme-secondary.fixed .main-header {
  background: transparent; }
.dark-skin.theme-secondary .main-header {
  background: transparent; }

@media (max-width: 767px) {
  .theme-secondary.fixed .main-header {
    background-image: #e4e6ef; }
    .theme-secondary.fixed .main-header.navbar {
      background: none; }

  .dark-skin.theme-secondary.fixed .main-header {
    background-image: #0c1a32; } }
.theme-secondary a:hover, .theme-secondary a:active, .theme-secondary a:focus {
  color: #e4e6ef; }
.theme-secondary .main-sidebar .svg-icon {
  filter: invert(0.6) sepia(1) saturate(1) hue-rotate(185deg); }
  .theme-secondary .main-sidebar .svg-icon:hover, .theme-secondary .main-sidebar .svg-icon:active, .theme-secondary .main-sidebar .svg-icon:focus {
    filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg); }
.theme-secondary .main-sidebar a:hover .svg-icon, .theme-secondary .main-sidebar a:active .svg-icon, .theme-secondary .main-sidebar a:focus .svg-icon {
  filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg); }
.theme-secondary .svg-icon {
  filter: invert(0.6) sepia(1) saturate(1) hue-rotate(185deg); }
  .theme-secondary .svg-icon:hover, .theme-secondary .svg-icon:active, .theme-secondary .svg-icon:focus {
    filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg); }
.theme-secondary a:hover .svg-icon, .theme-secondary a:active .svg-icon, .theme-secondary a:focus .svg-icon {
  filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg); }

.theme-secondary.light-skin .sidebar-menu > li.active.treeview > a {
  background: transparent;
  color: #b5b5c3 !important; }
  .theme-secondary.light-skin .sidebar-menu > li.active.treeview > a > i {
    color: #ffffff; }
  .theme-secondary.light-skin .sidebar-menu > li.active.treeview > a > svg {
    color: #ffffff;
    fill: rgba(1, 104, 250, 0.2); }
  .theme-secondary.light-skin .sidebar-menu > li.active.treeview > a:after {
    border-color: transparent #fafafa transparent transparent !important; }
.theme-secondary.light-skin .sidebar-menu > li.treeview .treeview-menu li a {
  color: #b5b5c3; }
.theme-secondary.light-skin.sidebar-mini.sidebar-collapse .sidebar-menu > li.active > a > span {
  background: #e4e6ef !important; }
.theme-secondary.dark-skin .sidebar-menu > li.active > a:after {
  border-color: transparent #333333 transparent transparent !important; }
.theme-secondary.dark-skin .sidebar-menu > li.active.treeview > a {
  background: transparent;
  color: #b5b5c3 !important; }
  .theme-secondary.dark-skin .sidebar-menu > li.active.treeview > a > i {
    color: #ffffff; }
  .theme-secondary.dark-skin .sidebar-menu > li.active.treeview > a:after {
    border-color: transparent #fafafa transparent transparent !important; }
.theme-secondary.dark-skin .sidebar-menu > li.active.treeview .treeview-menu li a {
  color: #b5b5c3; }
.theme-secondary.dark-skin.sidebar-mini.sidebar-collapse .sidebar-menu > li.active > a > span {
  background: #e4e6ef !important; }
.theme-secondary.light-skin .sidebar-menu > li:hover, .theme-secondary.light-skin .sidebar-menu > li:active, .theme-secondary.light-skin .sidebar-menu > li.active {
  background-color: rgba(228, 230, 239, 0);
  color: white;
  border-left: 5px solid rgba(228, 230, 239, 0); }
  .theme-secondary.light-skin .sidebar-menu > li:hover a, .theme-secondary.light-skin .sidebar-menu > li:active a, .theme-secondary.light-skin .sidebar-menu > li.active a {
    color: white; }
.theme-secondary.light-skin .sidebar-menu > li.active {
  background-color: rgba(228, 230, 239, 0);
  color: white;
  border-left: 5px solid #e4e6ef; }
  .theme-secondary.light-skin .sidebar-menu > li.active a {
    color: white;
    background-color: transparent; }
    .theme-secondary.light-skin .sidebar-menu > li.active a > i {
      color: #ffffff;
      background-color: rgba(228, 230, 239, 0); }
    .theme-secondary.light-skin .sidebar-menu > li.active a > svg {
      color: #ffffff;
      fill: rgba(1, 104, 250, 0.2); }
    .theme-secondary.light-skin .sidebar-menu > li.active a img.svg-icon {
      filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg); }
  .theme-secondary.light-skin .sidebar-menu > li.active .treeview-menu li.active {
    background-color: rgba(228, 230, 239, 0);
    color: white; }
    .theme-secondary.light-skin .sidebar-menu > li.active .treeview-menu li.active a {
      color: white; }
      .theme-secondary.light-skin .sidebar-menu > li.active .treeview-menu li.active a > i {
        color: white;
        background-color: rgba(228, 230, 239, 0); }
  .theme-secondary.light-skin .sidebar-menu > li.active .treeview-menu li a > i {
    color: #b5b5c3;
    background-color: rgba(228, 230, 239, 0); }
  .theme-secondary.light-skin .sidebar-menu > li.active .treeview-menu li.treeview.active {
    background-color: rgba(228, 230, 239, 0);
    color: white; }
    .theme-secondary.light-skin .sidebar-menu > li.active .treeview-menu li.treeview.active a {
      color: white; }
      .theme-secondary.light-skin .sidebar-menu > li.active .treeview-menu li.treeview.active a > i {
        color: white;
        background-color: rgba(228, 230, 239, 0); }
  .theme-secondary.light-skin .sidebar-menu > li.active .treeview-menu li.treeview .treeview-menu li.active {
    background-color: rgba(228, 230, 239, 0);
    color: white; }
    .theme-secondary.light-skin .sidebar-menu > li.active .treeview-menu li.treeview .treeview-menu li.active a {
      color: white; }
      .theme-secondary.light-skin .sidebar-menu > li.active .treeview-menu li.treeview .treeview-menu li.active a > i {
        color: white;
        background-color: rgba(228, 230, 239, 0); }
  .theme-secondary.light-skin .sidebar-menu > li.active .treeview-menu li.treeview .treeview-menu li a {
    color: #b5b5c3; }
    .theme-secondary.light-skin .sidebar-menu > li.active .treeview-menu li.treeview .treeview-menu li a > i {
      color: #b5b5c3;
      background-color: rgba(228, 230, 239, 0); }
.theme-secondary.rtl.light-skin .sidebar-menu > li:hover, .theme-secondary.rtl.light-skin .sidebar-menu > li:active, .theme-secondary.rtl.light-skin .sidebar-menu > li.active {
  border-left: 0px solid rgba(228, 230, 239, 0);
  border-right: 5px solid rgba(228, 230, 239, 0); }
.theme-secondary.rtl.light-skin .sidebar-menu > li.active {
  border-left: 0px solid #e4e6ef;
  border-right: 5px solid #e4e6ef; }
.theme-secondary.dark-skin .sidebar-menu > li.active {
  background-color: rgba(228, 230, 239, 0);
  color: white;
  border-left: 5px solid #e4e6ef; }
  .theme-secondary.dark-skin .sidebar-menu > li.active a {
    color: white;
    background-color: transparent; }
    .theme-secondary.dark-skin .sidebar-menu > li.active a > i {
      color: white; }
    .theme-secondary.dark-skin .sidebar-menu > li.active a > svg {
      color: #ffffff;
      fill: rgba(1, 104, 250, 0.2); }
    .theme-secondary.dark-skin .sidebar-menu > li.active a img.svg-icon {
      filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg); }
  .theme-secondary.dark-skin .sidebar-menu > li.active .treeview-menu li.active {
    background-color: rgba(228, 230, 239, 0);
    color: white; }
    .theme-secondary.dark-skin .sidebar-menu > li.active .treeview-menu li.active a {
      color: white !important; }
.theme-secondary.rtl.dark-skin .sidebar-menu > li.active {
  border-left: 0px solid #e4e6ef;
  border-right: 5px solid #e4e6ef; }

@media (min-width: 768px) {
  .sidebar-mini.sidebar-collapse .sidebar-menu > li.active.menu-open {
    background-color: rgba(228, 230, 239, 0.2);
    color: #e4e6ef; } }
/*---Main Nav---*/
.theme-secondary .sm-blue li.current > a, .theme-secondary .sm-blue li.highlighted > a {
  background: #e4e6ef;
  color: #ffffff !important; }
  .theme-secondary .sm-blue li.current > a:hover, .theme-secondary .sm-blue li.current > a:active, .theme-secondary .sm-blue li.current > a:focus, .theme-secondary .sm-blue li.highlighted > a:hover, .theme-secondary .sm-blue li.highlighted > a:active, .theme-secondary .sm-blue li.highlighted > a:focus {
    background: #e4e6ef;
    color: #ffffff !important; }
.theme-secondary .sm-blue a.current, .theme-secondary .sm-blue a.highlighted {
  background: #e4e6ef;
  color: #ffffff !important; }
.theme-secondary .sm-blue a:hover, .theme-secondary .sm-blue a:active, .theme-secondary .sm-blue a:focus {
  background: #e4e6ef;
  color: #ffffff !important; }
.theme-secondary .sm-blue ul a:hover, .theme-secondary .sm-blue ul a:active, .theme-secondary .sm-blue ul a:focus {
  background: #ebedf3;
  color: #e4e6ef !important; }
.theme-secondary .sm-blue ul a.highlighted {
  background: #ebedf3;
  color: #e4e6ef !important; }

.dark-skin.theme-secondary .sm-blue a.current, .dark-skin.theme-secondary .sm-blue a.highlighted {
  background: #e4e6ef;
  color: #ffffff !important; }
.dark-skin.theme-secondary .sm-blue a:hover, .dark-skin.theme-secondary .sm-blue a:active, .dark-skin.theme-secondary .sm-blue a:focus {
  background: #e4e6ef;
  color: #ffffff !important; }
.dark-skin.theme-secondary .sm-blue ul a:hover, .dark-skin.theme-secondary .sm-blue ul a:active, .dark-skin.theme-secondary .sm-blue ul a:focus {
  background: #29354b;
  color: #e4e6ef !important; }
.dark-skin.theme-secondary .sm-blue ul a.highlighted {
  background: #29354b;
  color: #e4e6ef !important; }

/*---Primary Button---*/
.theme-secondary .btn-link {
  color: #e4e6ef; }
.theme-secondary .btn-primary {
  background-color: #e4e6ef;
  border-color: #e4e6ef;
  color: #ffffff; }
  .theme-secondary .btn-primary:hover, .theme-secondary .btn-primary:active, .theme-secondary .btn-primary:focus, .theme-secondary .btn-primary.active {
    background-color: #c4c8dc !important;
    border-color: #c4c8dc !important;
    color: #ffffff !important; }
  .theme-secondary .btn-primary:disabled {
    background-color: white;
    border-color: #e4e6ef;
    opacity: 0.5; }
  .theme-secondary .btn-primary.disabled {
    background-color: white;
    border-color: #e4e6ef;
    opacity: 0.5; }
.theme-secondary .show > .btn-primary.dropdown-toggle {
  background-color: #c4c8dc !important;
  border-color: #c4c8dc !important;
  color: #ffffff; }
.theme-secondary .btn-outline.btn-primary {
  color: #e4e6ef;
  background-color: transparent;
  border-color: #e4e6ef !important; }
  .theme-secondary .btn-outline.btn-primary:hover, .theme-secondary .btn-outline.btn-primary:active, .theme-secondary .btn-outline.btn-primary.active {
    background-color: #c4c8dc !important;
    border-color: #c4c8dc !important;
    color: #ffffff !important; }
.theme-secondary .show > .btn-outline.btn-primary.dropdown-toggle {
  background-color: #c4c8dc !important;
  border-color: #c4c8dc !important;
  color: #ffffff; }
.theme-secondary .btn-flat.btn-primary {
  color: #e4e6ef !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-secondary .btn-flat.btn-primary:hover, .theme-secondary .btn-flat.btn-primary:active, .theme-secondary .btn-flat.btn-primary.active {
    background-color: #c4c8dc !important;
    border-color: #c4c8dc !important;
    color: #ffffff !important; }

/*---info Button---*/
.theme-secondary .btn-info {
  background-color: #00D0FF;
  border-color: #00D0FF;
  color: #ffffff; }
  .theme-secondary .btn-info:hover, .theme-secondary .btn-info:active, .theme-secondary .btn-info:focus, .theme-secondary .btn-info.active {
    background-color: #00a6cc !important;
    border-color: #00a6cc !important;
    color: #ffffff !important; }
  .theme-secondary .btn-info:disabled {
    background-color: #66e3ff;
    border-color: #00D0FF;
    opacity: 0.5; }
  .theme-secondary .btn-info.disabled {
    background-color: #66e3ff;
    border-color: #00D0FF;
    opacity: 0.5; }
.theme-secondary .show > .btn-info.dropdown-toggle {
  background-color: #00a6cc !important;
  border-color: #00a6cc !important;
  color: #ffffff; }
.theme-secondary .btn-outline.btn-info {
  color: #00D0FF;
  background-color: transparent;
  border-color: #00D0FF !important; }
  .theme-secondary .btn-outline.btn-info:hover, .theme-secondary .btn-outline.btn-info:active, .theme-secondary .btn-outline.btn-info.active {
    background-color: #00a6cc !important;
    border-color: #00a6cc !important;
    color: #ffffff !important; }
.theme-secondary .show > .btn-outline.btn-info.dropdown-toggle {
  background-color: #00a6cc !important;
  border-color: #00a6cc !important;
  color: #ffffff; }
.theme-secondary .btn-flat.btn-info {
  color: #00D0FF !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-secondary .btn-flat.btn-info:hover, .theme-secondary .btn-flat.btn-info:active, .theme-secondary .btn-flat.btn-info.active {
    background-color: #00a6cc !important;
    border-color: #00a6cc !important;
    color: #ffffff !important; }

/*---Success Button---*/
.theme-secondary .btn-success {
  background-color: #1dbfc1;
  border-color: #1dbfc1;
  color: #ffffff; }
  .theme-secondary .btn-success:hover, .theme-secondary .btn-success:active, .theme-secondary .btn-success:focus, .theme-secondary .btn-success.active {
    background-color: #169395 !important;
    border-color: #169395 !important;
    color: #ffffff !important; }
  .theme-secondary .btn-success:disabled {
    background-color: #5de5e7;
    border-color: #1dbfc1;
    opacity: 0.5; }
  .theme-secondary .btn-success.disabled {
    background-color: #5de5e7;
    border-color: #1dbfc1;
    opacity: 0.5; }
.theme-secondary .show > .btn-success.dropdown-toggle {
  background-color: #169395 !important;
  border-color: #169395 !important;
  color: #ffffff; }
.theme-secondary .btn-outline.btn-success {
  color: #1dbfc1;
  background-color: transparent;
  border-color: #1dbfc1 !important; }
  .theme-secondary .btn-outline.btn-success:hover, .theme-secondary .btn-outline.btn-success:active, .theme-secondary .btn-outline.btn-success.active {
    background-color: #169395 !important;
    border-color: #169395 !important;
    color: #ffffff !important; }
.theme-secondary .show > .btn-outline.btn-success.dropdown-toggle {
  background-color: #169395 !important;
  border-color: #169395 !important;
  color: #ffffff; }
.theme-secondary .btn-flat.btn-success {
  color: #1dbfc1 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-secondary .btn-flat.btn-success:hover, .theme-secondary .btn-flat.btn-success:active, .theme-secondary .btn-flat.btn-success.active {
    background-color: #169395 !important;
    border-color: #169395 !important;
    color: #ffffff !important; }

/*---Danger Button---*/
.theme-secondary .btn-danger {
  background-color: #ee3158;
  border-color: #ee3158;
  color: #ffffff; }
  .theme-secondary .btn-danger:hover, .theme-secondary .btn-danger:active, .theme-secondary .btn-danger:focus, .theme-secondary .btn-danger.active {
    background-color: #da123b !important;
    border-color: #da123b !important;
    color: #ffffff !important; }
  .theme-secondary .btn-danger:disabled {
    background-color: #f68fa4;
    border-color: #ee3158;
    opacity: 0.5; }
  .theme-secondary .btn-danger.disabled {
    background-color: #f68fa4;
    border-color: #ee3158;
    opacity: 0.5; }
.theme-secondary .show > .btn-danger.dropdown-toggle {
  background-color: #da123b !important;
  border-color: #da123b !important;
  color: #ffffff; }
.theme-secondary .btn-outline.btn-danger {
  color: #ee3158;
  background-color: transparent;
  border-color: #ee3158 !important; }
  .theme-secondary .btn-outline.btn-danger:hover, .theme-secondary .btn-outline.btn-danger:active, .theme-secondary .btn-outline.btn-danger.active {
    background-color: #da123b !important;
    border-color: #da123b !important;
    color: #ffffff !important; }
.theme-secondary .show > .btn-outline.btn-danger.dropdown-toggle {
  background-color: #da123b !important;
  border-color: #da123b !important;
  color: #ffffff; }
.theme-secondary .btn-flat.btn-danger {
  color: #ee3158 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-secondary .btn-flat.btn-danger:hover, .theme-secondary .btn-flat.btn-danger:active, .theme-secondary .btn-flat.btn-danger.active {
    background-color: #da123b !important;
    border-color: #da123b !important;
    color: #ffffff !important; }

/*---Warning Button---*/
.theme-secondary .btn-warning {
  background-color: #ffa800;
  border-color: #ffa800;
  color: #ffffff; }
  .theme-secondary .btn-warning:hover, .theme-secondary .btn-warning:active, .theme-secondary .btn-warning:focus, .theme-secondary .btn-warning.active {
    background-color: #cc8600 !important;
    border-color: #cc8600 !important;
    color: #ffffff !important; }
  .theme-secondary .btn-warning:disabled {
    background-color: #ffcb66;
    border-color: #ffa800;
    opacity: 0.5; }
  .theme-secondary .btn-warning.disabled {
    background-color: #ffcb66;
    border-color: #ffa800;
    opacity: 0.5; }
.theme-secondary .show > .btn-warning.dropdown-toggle {
  background-color: #cc8600 !important;
  border-color: #cc8600 !important;
  color: #ffffff; }
.theme-secondary .btn-outline.btn-warning {
  color: #ffa800;
  background-color: transparent;
  border-color: #ffa800 !important; }
  .theme-secondary .btn-outline.btn-warning:hover, .theme-secondary .btn-outline.btn-warning:active, .theme-secondary .btn-outline.btn-warning.active {
    background-color: #cc8600 !important;
    border-color: #cc8600 !important;
    color: #ffffff !important; }
.theme-secondary .show > .btn-outline.btn-warning.dropdown-toggle {
  background-color: #cc8600 !important;
  border-color: #cc8600 !important;
  color: #ffffff; }
.theme-secondary .btn-flat.btn-warning {
  color: #ffa800 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-secondary .btn-flat.btn-warning:hover, .theme-secondary .btn-flat.btn-warning:active, .theme-secondary .btn-flat.btn-warning.active {
    background-color: #cc8600 !important;
    border-color: #cc8600 !important;
    color: #ffffff !important; }

/*---Primary Button light---*/
.theme-secondary .btn-primary-light {
  background-color: #e9edf2;
  border-color: #e9edf2;
  color: #e4e6ef; }
  .theme-secondary .btn-primary-light:hover, .theme-secondary .btn-primary-light:active, .theme-secondary .btn-primary-light:focus, .theme-secondary .btn-primary-light.active {
    background-color: #e4e6ef !important;
    border-color: #e4e6ef !important;
    color: #ffffff !important; }
  .theme-secondary .btn-primary-light:disabled {
    background-color: white;
    border-color: #e9edf2;
    opacity: 0.5; }
  .theme-secondary .btn-primary-light.disabled {
    background-color: white;
    border-color: #e9edf2;
    opacity: 0.5; }
.theme-secondary .show > .btn-primary-light.dropdown-toggle {
  background-color: #e4e6ef !important;
  border-color: #e4e6ef !important;
  color: #ffffff; }
.theme-secondary .btn-outline.btn-primary-light {
  color: #e4e6ef;
  background-color: transparent;
  border-color: #e9edf2 !important; }
  .theme-secondary .btn-outline.btn-primary-light:hover, .theme-secondary .btn-outline.btn-primary-light:active, .theme-secondary .btn-outline.btn-primary-light.active {
    background-color: #e4e6ef !important;
    border-color: #e4e6ef !important;
    color: #ffffff !important; }
.theme-secondary .show > .btn-outline.btn-primary-light.dropdown-toggle {
  background-color: #e4e6ef !important;
  border-color: #e4e6ef !important;
  color: #ffffff; }
.theme-secondary .btn-flat.btn-primary-light {
  color: #e4e6ef !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-secondary .btn-flat.btn-primary-light:hover, .theme-secondary .btn-flat.btn-primary-light:active, .theme-secondary .btn-flat.btn-primary-light.active {
    background-color: #e4e6ef !important;
    border-color: #e4e6ef !important;
    color: #ffffff !important; }

/*---info Button light---*/
.theme-secondary .btn-info-light {
  background-color: #e1f9ff;
  border-color: #e1f9ff;
  color: #00D0FF; }
  .theme-secondary .btn-info-light:hover, .theme-secondary .btn-info-light:active, .theme-secondary .btn-info-light:focus, .theme-secondary .btn-info-light.active {
    background-color: #00D0FF !important;
    border-color: #00D0FF !important;
    color: #ffffff !important; }
  .theme-secondary .btn-info-light:disabled {
    background-color: white;
    border-color: #e1f9ff;
    opacity: 0.5; }
  .theme-secondary .btn-info-light.disabled {
    background-color: white;
    border-color: #e1f9ff;
    opacity: 0.5; }
.theme-secondary .show > .btn-info.dropdown-toggle {
  background-color: #00D0FF !important;
  border-color: #00D0FF !important;
  color: #ffffff; }
.theme-secondary .btn-outline.btn-info-light {
  color: #00D0FF;
  background-color: transparent;
  border-color: #e1f9ff !important; }
  .theme-secondary .btn-outline.btn-info-light:hover, .theme-secondary .btn-outline.btn-info-light:active, .theme-secondary .btn-outline.btn-info-light.active {
    background-color: #00D0FF !important;
    border-color: #00D0FF !important;
    color: #ffffff !important; }
.theme-secondary .show > .btn-outline.btn-info-light.dropdown-toggle {
  background-color: #00D0FF !important;
  border-color: #00D0FF !important;
  color: #ffffff; }
.theme-secondary .btn-flat.btn-info-light {
  color: #00D0FF !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-secondary .btn-flat.btn-info-light:hover, .theme-secondary .btn-flat.btn-info-light:active, .theme-secondary .btn-flat.btn-info-light.active {
    background-color: #00D0FF !important;
    border-color: #00D0FF !important;
    color: #ffffff !important; }

/*---Success Button light---*/
.theme-secondary .btn-success-light {
  background-color: #e8f9f9;
  border-color: #e8f9f9;
  color: #1dbfc1; }
  .theme-secondary .btn-success-light:hover, .theme-secondary .btn-success-light:active, .theme-secondary .btn-success-light:focus, .theme-secondary .btn-success-light.active {
    background-color: #1dbfc1 !important;
    border-color: #1dbfc1 !important;
    color: #ffffff !important; }
  .theme-secondary .btn-success-light:disabled {
    background-color: white;
    border-color: #e8f9f9;
    opacity: 0.5; }
  .theme-secondary .btn-success-light.disabled {
    background-color: white;
    border-color: #e8f9f9;
    opacity: 0.5; }
.theme-secondary .show > .btn-success-light.dropdown-toggle {
  background-color: #1dbfc1 !important;
  border-color: #1dbfc1 !important;
  color: #ffffff; }
.theme-secondary .btn-outline.btn-success-light {
  color: #1dbfc1;
  background-color: transparent;
  border-color: #e8f9f9 !important; }
  .theme-secondary .btn-outline.btn-success-light:hover, .theme-secondary .btn-outline.btn-success-light:active, .theme-secondary .btn-outline.btn-success-light.active {
    background-color: #1dbfc1 !important;
    border-color: #1dbfc1 !important;
    color: #ffffff !important; }
.theme-secondary .show > .btn-outline.btn-success-light.dropdown-toggle {
  background-color: #1dbfc1 !important;
  border-color: #1dbfc1 !important;
  color: #ffffff; }
.theme-secondary .btn-flat.btn-success-light {
  color: #1dbfc1 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-secondary .btn-flat.btn-success-light:hover, .theme-secondary .btn-flat.btn-success-light:active, .theme-secondary .btn-flat.btn-success-light.active {
    background-color: #1dbfc1 !important;
    border-color: #1dbfc1 !important;
    color: #ffffff !important; }

/*---Danger Button light---*/
.theme-secondary .btn-danger-light {
  background-color: #ffd6de;
  border-color: #ffd6de;
  color: #ee3158; }
  .theme-secondary .btn-danger-light:hover, .theme-secondary .btn-danger-light:active, .theme-secondary .btn-danger-light:focus, .theme-secondary .btn-danger-light.active {
    background-color: #ee3158 !important;
    border-color: #ee3158 !important;
    color: #ffffff !important; }
  .theme-secondary .btn-danger-light:disabled {
    background-color: white;
    border-color: #ffd6de;
    opacity: 0.5; }
  .theme-secondary .btn-danger-light.disabled {
    background-color: white;
    border-color: #ffd6de;
    opacity: 0.5; }
.theme-secondary .show > .btn-danger-light.dropdown-toggle {
  background-color: #ee3158 !important;
  border-color: #ee3158 !important;
  color: #ffffff; }
.theme-secondary .btn-outline.btn-danger-light {
  color: #ee3158;
  background-color: transparent;
  border-color: #ffd6de !important; }
  .theme-secondary .btn-outline.btn-danger-light:hover, .theme-secondary .btn-outline.btn-danger-light:active, .theme-secondary .btn-outline.btn-danger-light.active {
    background-color: #ee3158 !important;
    border-color: #ee3158 !important;
    color: #ffffff !important; }
.theme-secondary .show > .btn-outline.btn-danger-light.dropdown-toggle {
  background-color: #ee3158 !important;
  border-color: #ee3158 !important;
  color: #ffffff; }
.theme-secondary .btn-flat.btn-danger-light {
  color: #ee3158 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-secondary .btn-flat.btn-danger-light:hover, .theme-secondary .btn-flat.btn-danger-light:active, .theme-secondary .btn-flat.btn-danger-light.active {
    background-color: #ee3158 !important;
    border-color: #ee3158 !important;
    color: #ffffff !important; }

/*---Warning Button light---*/
.theme-secondary .btn-warning-light {
  background-color: #fff8ea;
  border-color: #fff8ea;
  color: #ffa800; }
  .theme-secondary .btn-warning-light:hover, .theme-secondary .btn-warning-light:active, .theme-secondary .btn-warning-light:focus, .theme-secondary .btn-warning-light.active {
    background-color: #ffa800 !important;
    border-color: #ffa800 !important;
    color: #ffffff !important; }
  .theme-secondary .btn-warning-light:disabled {
    background-color: white;
    border-color: #fff8ea;
    opacity: 0.5; }
  .theme-secondary .btn-warning-light.disabled {
    background-color: white;
    border-color: #fff8ea;
    opacity: 0.5; }
.theme-secondary .show > .btn-warning-light.dropdown-toggle {
  background-color: #ffa800 !important;
  border-color: #ffa800 !important;
  color: #ffffff; }
.theme-secondary .btn-outline.btn-warning-light {
  color: #ffa800;
  background-color: transparent;
  border-color: #fff8ea !important; }
  .theme-secondary .btn-outline.btn-warning-light:hover, .theme-secondary .btn-outline.btn-warning-light:active, .theme-secondary .btn-outline.btn-warning-light.active {
    background-color: #ffa800 !important;
    border-color: #ffa800 !important;
    color: #ffffff !important; }
.theme-secondary .show > .btn-outline.btn-warning-light.dropdown-toggle {
  background-color: #ffa800 !important;
  border-color: #ffa800 !important;
  color: #ffffff; }
.theme-secondary .btn-flat.btn-warning-light {
  color: #ffa800 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-secondary .btn-flat.btn-warning-light:hover, .theme-secondary .btn-flat.btn-warning-light:active, .theme-secondary .btn-flat.btn-warning-light.active {
    background-color: #ffa800 !important;
    border-color: #ffa800 !important;
    color: #ffffff !important; }

/*---callout---*/
.theme-secondary .callout.callout-primary {
  border-color: #e4e6ef;
  background-color: #e4e6ef !important; }
.theme-secondary .callout.callout-info {
  border-color: #00D0FF;
  background-color: #00D0FF !important; }
.theme-secondary .callout.callout-success {
  border-color: #1dbfc1;
  background-color: #1dbfc1 !important; }
.theme-secondary .callout.callout-danger {
  border-color: #ee3158;
  background-color: #ee3158 !important; }
.theme-secondary .callout.callout-warning {
  border-color: #ffa800;
  background-color: #ffa800 !important; }

/*---alert---*/
.theme-secondary .alert-primary {
  border-color: #e4e6ef;
  background-color: #e4e6ef !important;
  color: #ffffff; }
.theme-secondary .alert-info {
  border-color: #00D0FF;
  background-color: #00D0FF !important;
  color: #ffffff; }
.theme-secondary .alert-success {
  border-color: #1dbfc1;
  background-color: #1dbfc1 !important;
  color: #ffffff; }
.theme-secondary .alert-danger {
  border-color: #ee3158;
  background-color: #ee3158 !important;
  color: #ffffff; }
.theme-secondary .alert-error {
  border-color: #ee3158;
  background-color: #ee3158 !important;
  color: #ffffff; }
.theme-secondary .alert-warning {
  border-color: #ffa800;
  background-color: #ffa800 !important;
  color: #ffffff; }

/*---direct-chat---*/
.theme-secondary .direct-chat-primary .right > .direct-chat-text p {
  background-color: #e4e6ef;
  color: #ffffff; }
.theme-secondary .direct-chat-primary .right > .direct-chat-text:before, .theme-secondary .direct-chat-primary .right > .direct-chat-text:after {
  border-left-color: #e4e6ef; }
.theme-secondary .direct-chat-info .right > .direct-chat-text p {
  background-color: #00D0FF;
  color: #ffffff; }
.theme-secondary .direct-chat-info .right > .direct-chat-text:before, .theme-secondary .direct-chat-info .right > .direct-chat-text:after {
  border-left-color: #00D0FF; }
.theme-secondary .direct-chat-success .right > .direct-chat-text p {
  background-color: #1dbfc1;
  color: #ffffff; }
.theme-secondary .direct-chat-success .right > .direct-chat-text:before, .theme-secondary .direct-chat-success .right > .direct-chat-text:after {
  border-left-color: #1dbfc1; }
.theme-secondary .direct-chat-danger .right > .direct-chat-text p {
  background-color: #ee3158;
  color: #ffffff; }
.theme-secondary .direct-chat-danger .right > .direct-chat-text:before, .theme-secondary .direct-chat-danger .right > .direct-chat-text:after {
  border-left-color: #ee3158; }
.theme-secondary .direct-chat-warning .right > .direct-chat-text p {
  background-color: #ffa800;
  color: #ffffff; }
.theme-secondary .direct-chat-warning .right > .direct-chat-text:before, .theme-secondary .direct-chat-warning .right > .direct-chat-text:after {
  border-left-color: #ffa800; }
.theme-secondary .right .direct-chat-text p {
  background-color: #e4e6ef; }

/*---modal---*/
.theme-secondary .modal-primary .modal-footer, .theme-secondary .modal-primary .modal-header {
  border-color: #e4e6ef; }
.theme-secondary .modal-primary .modal-body {
  background-color: #e4e6ef !important; }
.theme-secondary .modal-info .modal-footer, .theme-secondary .modal-info .modal-header {
  border-color: #00D0FF; }
.theme-secondary .modal-info .modal-body {
  background-color: #00D0FF !important; }
.theme-secondary .modal-success .modal-footer, .theme-secondary .modal-success .modal-header {
  border-color: #1dbfc1; }
.theme-secondary .modal-success .modal-body {
  background-color: #1dbfc1 !important; }
.theme-secondary .modal-danger .modal-footer, .theme-secondary .modal-danger .modal-header {
  border-color: #ee3158; }
.theme-secondary .modal-danger .modal-body {
  background-color: #ee3158 !important; }
.theme-secondary .modal-warning .modal-footer, .theme-secondary .modal-warning .modal-header {
  border-color: #ffa800; }
.theme-secondary .modal-warning .modal-body {
  background-color: #ffa800 !important; }

/*---border---*/
.theme-secondary .border-primary {
  border-color: #e4e6ef !important; }
.theme-secondary .border-info {
  border-color: #00D0FF !important; }
.theme-secondary .border-success {
  border-color: #1dbfc1 !important; }
.theme-secondary .border-danger {
  border-color: #ee3158 !important; }
.theme-secondary .border-warning {
  border-color: #ffa800 !important; }

/*---Background---*/
.theme-secondary .bg-primary {
  background-color: #e4e6ef !important;
  color: #ffffff; }
.theme-secondary .bg-primary-light {
  background-color: #e9edf2 !important;
  color: #e4e6ef; }
.theme-secondary .bg-info {
  background-color: #00D0FF !important;
  color: #ffffff; }
.theme-secondary .bg-info-light {
  background-color: #e1f9ff !important;
  color: #00D0FF; }
.theme-secondary .bg-success {
  background-color: #1dbfc1 !important;
  color: #ffffff; }
.theme-secondary .bg-success-light {
  background-color: #e8f9f9 !important;
  color: #1dbfc1; }
.theme-secondary .bg-danger {
  background-color: #ee3158 !important;
  color: #ffffff; }
.theme-secondary .bg-danger-light {
  background-color: #ffd6de !important;
  color: #ee3158; }
.theme-secondary .bg-warning {
  background-color: #ffa800 !important;
  color: #ffffff; }
.theme-secondary .bg-warning-light {
  background-color: #fff8ea !important;
  color: #ffa800; }

/*---text---*/
.theme-secondary .text-primary {
  color: #e4e6ef !important; }
.theme-secondary a.text-primary:hover, .theme-secondary a.text-primary:focus {
  color: #e4e6ef !important; }
.theme-secondary .hover-primary:hover, .theme-secondary .hover-primary:focus {
  color: #e4e6ef !important; }
.theme-secondary .text-info {
  color: #00D0FF !important; }
.theme-secondary a.text-info:hover, .theme-secondary a.text-info:focus {
  color: #00D0FF !important; }
.theme-secondary .hover-info:hover, .theme-secondary .hover-info:focus {
  color: #00D0FF !important; }
.theme-secondary .text-success {
  color: #1dbfc1 !important; }
.theme-secondary a.text-success:hover, .theme-secondary a.text-success:focus {
  color: #1dbfc1 !important; }
.theme-secondary .hover-success:hover, .theme-secondary .hover-success:focus {
  color: #1dbfc1 !important; }
.theme-secondary .text-danger {
  color: #ee3158 !important; }
.theme-secondary a.text-danger:hover, .theme-secondary a.text-danger:focus {
  color: #ee3158 !important; }
.theme-secondary .hover-danger:hover, .theme-secondary .hover-danger:focus {
  color: #ee3158 !important; }
.theme-secondary .text-warning {
  color: #ffa800 !important; }
.theme-secondary a.text-warning:hover, .theme-secondary a.text-warning:focus {
  color: #ffa800 !important; }
.theme-secondary .hover-warning:hover, .theme-secondary .hover-warning:focus {
  color: #ffa800 !important; }

/*---active background---*/
.theme-secondary .active.active-primary {
  background-color: #c4c8dc !important; }
.theme-secondary .active.active-info {
  background-color: #00a6cc !important; }
.theme-secondary .active.active-success {
  background-color: #169395 !important; }
.theme-secondary .active.active-danger {
  background-color: #da123b !important; }
.theme-secondary .active.active-warning {
  background-color: #cc8600 !important; }

/*---label background---*/
.theme-secondary .label-primary {
  background-color: #e4e6ef !important; }
.theme-secondary .label-info {
  background-color: #00D0FF !important; }
.theme-secondary .label-success {
  background-color: #1dbfc1 !important; }
.theme-secondary .label-danger {
  background-color: #ee3158 !important; }
.theme-secondary .label-warning {
  background-color: #ffa800 !important; }

/*---ribbon---*/
.theme-secondary .ribbon-box .ribbon-primary {
  background-color: #e4e6ef; }
  .theme-secondary .ribbon-box .ribbon-primary:before {
    border-color: #e4e6ef transparent transparent; }
.theme-secondary .ribbon-box .ribbon-two-primary span {
  background-color: #e4e6ef; }
  .theme-secondary .ribbon-box .ribbon-two-primary span:before {
    border-left: 3px solid #c4c8dc;
    border-top: 3px solid #c4c8dc; }
  .theme-secondary .ribbon-box .ribbon-two-primary span:after {
    border-right: 3px solid #c4c8dc;
    border-top: 3px solid #c4c8dc; }
.theme-secondary .ribbon-box .ribbon-info {
  background-color: #00D0FF; }
  .theme-secondary .ribbon-box .ribbon-info:before {
    border-color: #00D0FF transparent transparent; }
.theme-secondary .ribbon-box .ribbon-two-info span {
  background-color: #00D0FF; }
  .theme-secondary .ribbon-box .ribbon-two-info span:before {
    border-left: 3px solid #00a6cc;
    border-top: 3px solid #00a6cc; }
  .theme-secondary .ribbon-box .ribbon-two-info span:after {
    border-right: 3px solid #00a6cc;
    border-top: 3px solid #00a6cc; }
.theme-secondary .ribbon-box .ribbon-success {
  background-color: #1dbfc1; }
  .theme-secondary .ribbon-box .ribbon-success:before {
    border-color: #1dbfc1 transparent transparent; }
.theme-secondary .ribbon-box .ribbon-two-success span {
  background-color: #1dbfc1; }
  .theme-secondary .ribbon-box .ribbon-two-success span:before {
    border-left: 3px solid #169395;
    border-top: 3px solid #169395; }
  .theme-secondary .ribbon-box .ribbon-two-success span:after {
    border-right: 3px solid #169395;
    border-top: 3px solid #169395; }
.theme-secondary .ribbon-box .ribbon-danger {
  background-color: #ee3158; }
  .theme-secondary .ribbon-box .ribbon-danger:before {
    border-color: #ee3158 transparent transparent; }
.theme-secondary .ribbon-box .ribbon-two-danger span {
  background-color: #ee3158; }
  .theme-secondary .ribbon-box .ribbon-two-danger span:before {
    border-left: 3px solid #da123b;
    border-top: 3px solid #da123b; }
  .theme-secondary .ribbon-box .ribbon-two-danger span:after {
    border-right: 3px solid #da123b;
    border-top: 3px solid #da123b; }
.theme-secondary .ribbon-box .ribbon-warning {
  background-color: #ffa800; }
  .theme-secondary .ribbon-box .ribbon-warning:before {
    border-color: #ffa800 transparent transparent; }
.theme-secondary .ribbon-box .ribbon-two-warning span {
  background-color: #ffa800; }
  .theme-secondary .ribbon-box .ribbon-two-warning span:before {
    border-left: 3px solid #cc8600;
    border-top: 3px solid #cc8600; }
  .theme-secondary .ribbon-box .ribbon-two-warning span:after {
    border-right: 3px solid #cc8600;
    border-top: 3px solid #cc8600; }

/*---Box---*/
.theme-secondary .box-primary {
  background-color: #e4e6ef !important; }
  .theme-secondary .box-primary.box-bordered {
    border-color: #e4e6ef; }
.theme-secondary .box-outline-primary {
  background-color: #ffffff;
  border: 1px solid #e4e6ef; }
.theme-secondary .box.box-solid.box-primary > .box-header {
  color: #ffffff;
  background-color: #e4e6ef; }
  .theme-secondary .box.box-solid.box-primary > .box-header .btn {
    color: #ffffff; }
  .theme-secondary .box.box-solid.box-primary > .box-header > a {
    color: #ffffff; }
.theme-secondary .box-info {
  background-color: #00D0FF !important; }
  .theme-secondary .box-info.box-bordered {
    border-color: #00D0FF; }
.theme-secondary .box-outline-info {
  background-color: #ffffff;
  border: 1px solid #00D0FF; }
.theme-secondary .box.box-solid.box-info > .box-header {
  color: #ffffff;
  background-color: #00D0FF; }
  .theme-secondary .box.box-solid.box-info > .box-header .btn {
    color: #ffffff; }
  .theme-secondary .box.box-solid.box-info > .box-header > a {
    color: #ffffff; }
.theme-secondary .box-success {
  background-color: #1dbfc1 !important; }
  .theme-secondary .box-success.box-bordered {
    border-color: #1dbfc1; }
.theme-secondary .box-outline-success {
  background-color: #ffffff;
  border: 1px solid #1dbfc1; }
.theme-secondary .box.box-solid.box-success > .box-header {
  color: #ffffff;
  background-color: #1dbfc1; }
  .theme-secondary .box.box-solid.box-success > .box-header .btn {
    color: #ffffff; }
  .theme-secondary .box.box-solid.box-success > .box-header > a {
    color: #ffffff; }
.theme-secondary .box-danger {
  background-color: #ee3158 !important; }
  .theme-secondary .box-danger.box-bordered {
    border-color: #ee3158; }
.theme-secondary .box-outline-danger {
  background-color: #ffffff;
  border: 1px solid #ee3158; }
.theme-secondary .box.box-solid.box-danger > .box-header {
  color: #ffffff;
  background-color: #ee3158; }
  .theme-secondary .box.box-solid.box-danger > .box-header .btn {
    color: #ffffff; }
  .theme-secondary .box.box-solid.box-danger > .box-header > a {
    color: #ffffff; }
.theme-secondary .box-warning {
  background-color: #ffa800 !important; }
  .theme-secondary .box-warning.box-bordered {
    border-color: #ffa800; }
.theme-secondary .box-outline-warning {
  background-color: #ffffff;
  border: 1px solid #ffa800; }
.theme-secondary .box.box-solid.box-warning > .box-header {
  color: #ffffff;
  background-color: #ffa800; }
  .theme-secondary .box.box-solid.box-warning > .box-header .btn {
    color: #ffffff; }
  .theme-secondary .box.box-solid.box-warning > .box-header > a {
    color: #ffffff; }
.theme-secondary .box-profile .social-states a:hover {
  color: #c4c8dc; }
.theme-secondary .box-controls li > a:hover {
  color: #c4c8dc; }
.theme-secondary .box-controls .dropdown.show > a {
  color: #c4c8dc; }
.theme-secondary .box-fullscreen .box-btn-fullscreen {
  color: #c4c8dc; }

/*---progress bar---*/
.theme-secondary .progress-bar-primary {
  background-color: #e4e6ef; }
.theme-secondary .progress-bar-info {
  background-color: #00D0FF; }
.theme-secondary .progress-bar-success {
  background-color: #1dbfc1; }
.theme-secondary .progress-bar-danger {
  background-color: #ee3158; }
.theme-secondary .progress-bar-warning {
  background-color: #ffa800; }

/*---panel---*/
.theme-secondary .panel-primary {
  border-color: #e4e6ef; }
  .theme-secondary .panel-primary > .panel-heading {
    color: #ffffff;
    background-color: #e4e6ef;
    border-color: #e4e6ef; }
    .theme-secondary .panel-primary > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #e4e6ef; }
    .theme-secondary .panel-primary > .panel-heading .badge-pill {
      color: #e4e6ef;
      background-color: #ffffff; }
  .theme-secondary .panel-primary .panel-title, .theme-secondary .panel-primary .panel-action {
    color: #ffffff; }
  .theme-secondary .panel-primary .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #e4e6ef; }
.theme-secondary .panel-line.panel-primary .panel-heading {
  color: #e4e6ef;
  border-top-color: #e4e6ef;
  background: transparent; }
.theme-secondary .panel-line.panel-primary .panel-title, .theme-secondary .panel-line.panel-primary .panel-action {
  color: #e4e6ef; }
.theme-secondary .panel-info {
  border-color: #00D0FF; }
  .theme-secondary .panel-info > .panel-heading {
    color: #ffffff;
    background-color: #00D0FF;
    border-color: #00D0FF; }
    .theme-secondary .panel-info > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #00D0FF; }
    .theme-secondary .panel-info > .panel-heading .badge-pill {
      color: #00D0FF;
      background-color: #ffffff; }
  .theme-secondary .panel-info .panel-title, .theme-secondary .panel-info .panel-action {
    color: #ffffff; }
  .theme-secondary .panel-info .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #00D0FF; }
.theme-secondary .panel-line.panel-info .panel-heading {
  color: #00D0FF;
  border-top-color: #00D0FF;
  background: transparent; }
.theme-secondary .panel-line.panel-info .panel-title, .theme-secondary .panel-line.panel-info .panel-action {
  color: #00D0FF; }
.theme-secondary .panel-success {
  border-color: #1dbfc1; }
  .theme-secondary .panel-success > .panel-heading {
    color: #ffffff;
    background-color: #1dbfc1;
    border-color: #1dbfc1; }
    .theme-secondary .panel-success > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #1dbfc1; }
    .theme-secondary .panel-success > .panel-heading .badge-pill {
      color: #1dbfc1;
      background-color: #ffffff; }
  .theme-secondary .panel-success .panel-title, .theme-secondary .panel-success .panel-action {
    color: #ffffff; }
  .theme-secondary .panel-success .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #1dbfc1; }
.theme-secondary .panel-line.panel-success .panel-heading {
  color: #1dbfc1;
  border-top-color: #1dbfc1;
  background: transparent; }
.theme-secondary .panel-line.panel-success .panel-title, .theme-secondary .panel-line.panel-success .panel-action {
  color: #1dbfc1; }
.theme-secondary .panel-danger {
  border-color: #ee3158; }
  .theme-secondary .panel-danger > .panel-heading {
    color: #ffffff;
    background-color: #ee3158;
    border-color: #ee3158; }
    .theme-secondary .panel-danger > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #ee3158; }
    .theme-secondary .panel-danger > .panel-heading .badge-pill {
      color: #ee3158;
      background-color: #ffffff; }
  .theme-secondary .panel-danger .panel-title, .theme-secondary .panel-danger .panel-action {
    color: #ffffff; }
  .theme-secondary .panel-danger .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #ee3158; }
.theme-secondary .panel-line.panel-danger .panel-heading {
  color: #ee3158;
  border-top-color: #ee3158;
  background: transparent; }
.theme-secondary .panel-line.panel-danger .panel-title, .theme-secondary .panel-line.panel-danger .panel-action {
  color: #ee3158; }
.theme-secondary .panel-warning {
  border-color: #ffa800; }
  .theme-secondary .panel-warning > .panel-heading {
    color: #ffffff;
    background-color: #ffa800;
    border-color: #ffa800; }
    .theme-secondary .panel-warning > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #ffa800; }
    .theme-secondary .panel-warning > .panel-heading .badge-pill {
      color: #ffa800;
      background-color: #ffffff; }
  .theme-secondary .panel-warning .panel-title, .theme-secondary .panel-warning .panel-action {
    color: #ffffff; }
  .theme-secondary .panel-warning .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #ffa800; }
.theme-secondary .panel-line.panel-warning .panel-heading {
  color: #ffa800;
  border-top-color: #ffa800;
  background: transparent; }
.theme-secondary .panel-line.panel-warning .panel-title, .theme-secondary .panel-line.panel-warning .panel-action {
  color: #ffa800; }

/*---switch---*/
.theme-secondary .switch input:checked ~ .switch-indicator::after {
  background-color: #e4e6ef; }
.theme-secondary .switch.switch-primary input:checked ~ .switch-indicator::after {
  background-color: #e4e6ef; }
.theme-secondary .switch.switch-info input:checked ~ .switch-indicator::after {
  background-color: #00D0FF; }
.theme-secondary .switch.switch-success input:checked ~ .switch-indicator::after {
  background-color: #1dbfc1; }
.theme-secondary .switch.switch-danger input:checked ~ .switch-indicator::after {
  background-color: #ee3158; }
.theme-secondary .switch.switch-warning input:checked ~ .switch-indicator::after {
  background-color: #ffa800; }

/*---badge---*/
.theme-secondary .badge-primary {
  background-color: #e4e6ef;
  color: #ffffff; }
.theme-secondary .badge-primary[href]:hover, .theme-secondary .badge-primary[href]:focus {
  background-color: #c4c8dc; }
.theme-secondary .badge-secondary {
  background-color: #3246D3;
  color: #172b4c; }
.theme-secondary .badge-secondary[href]:hover, .theme-secondary .badge-secondary[href]:focus {
  background-color: #2536ad; }
.theme-secondary .badge-info {
  background-color: #00D0FF;
  color: #ffffff; }
.theme-secondary .badge-info[href]:hover, .theme-secondary .badge-info[href]:focus {
  background-color: #00a6cc; }
.theme-secondary .badge-success {
  background-color: #1dbfc1;
  color: #ffffff; }
.theme-secondary .badge-success[href]:hover, .theme-secondary .badge-success[href]:focus {
  background-color: #169395; }
.theme-secondary .badge-danger {
  background-color: #ee3158;
  color: #ffffff; }
.theme-secondary .badge-danger[href]:hover, .theme-secondary .badge-danger[href]:focus {
  background-color: #da123b; }
.theme-secondary .badge-warning {
  background-color: #ffa800;
  color: #ffffff; }
.theme-secondary .badge-warning[href]:hover, .theme-secondary .badge-warning[href]:focus {
  background-color: #cc8600; }

/*---badge light---*/
.theme-secondary .badge-primary-light {
  background-color: #e9edf2;
  color: #e4e6ef; }
.theme-secondary .badge-primary-light[href]:hover, .theme-secondary .badge-primary-light[href]:focus {
  background-color: #c9d3df; }
.theme-secondary .badge-secondary-light {
  background-color: #dbdfff;
  color: #172b4c; }
.theme-secondary .badge-secondary-light[href]:hover, .theme-secondary .badge-secondary-light[href]:focus {
  background-color: #a8b2ff; }
.theme-secondary .badge-info-light {
  background-color: #e1f9ff;
  color: #00D0FF; }
.theme-secondary .badge-info-light[href]:hover, .theme-secondary .badge-info-light[href]:focus {
  background-color: #aeefff; }
.theme-secondary .badge-success-light {
  background-color: #e8f9f9;
  color: #1dbfc1; }
.theme-secondary .badge-success-light[href]:hover, .theme-secondary .badge-success-light[href]:focus {
  background-color: #c0eeee; }
.theme-secondary .badge-danger-light {
  background-color: #ffd6de;
  color: #ee3158; }
.theme-secondary .badge-danger-light[href]:hover, .theme-secondary .badge-danger-light[href]:focus {
  background-color: #ffa3b5; }
.theme-secondary .badge-warning-light {
  background-color: #fff8ea;
  color: #ffa800; }
.theme-secondary .badge-warning-light[href]:hover, .theme-secondary .badge-warning-light[href]:focus {
  background-color: #ffe7b7; }

/*---rating---*/
.theme-secondary .rating-primary .active {
  color: #e4e6ef; }
.theme-secondary .rating-primary :checked ~ label {
  color: #e4e6ef; }
.theme-secondary .rating-primary label:hover {
  color: #e4e6ef; }
  .theme-secondary .rating-primary label:hover ~ label {
    color: #e4e6ef; }
.theme-secondary .rating-info .active {
  color: #00D0FF; }
.theme-secondary .rating-info :checked ~ label {
  color: #00D0FF; }
.theme-secondary .rating-info label:hover {
  color: #00D0FF; }
  .theme-secondary .rating-info label:hover ~ label {
    color: #00D0FF; }
.theme-secondary .rating-success .active {
  color: #1dbfc1; }
.theme-secondary .rating-success :checked ~ label {
  color: #1dbfc1; }
.theme-secondary .rating-success label:hover {
  color: #1dbfc1; }
  .theme-secondary .rating-success label:hover ~ label {
    color: #1dbfc1; }
.theme-secondary .rating-danger .active {
  color: #ee3158; }
.theme-secondary .rating-danger :checked ~ label {
  color: #ee3158; }
.theme-secondary .rating-danger label:hover {
  color: #ee3158; }
  .theme-secondary .rating-danger label:hover ~ label {
    color: #ee3158; }
.theme-secondary .rating-warning .active {
  color: #ffa800; }
.theme-secondary .rating-warning :checked ~ label {
  color: #ffa800; }
.theme-secondary .rating-warning label:hover {
  color: #ffa800; }
  .theme-secondary .rating-warning label:hover ~ label {
    color: #ffa800; }

/*---toggler---*/
.theme-secondary .toggler-primary input:checked + i {
  color: #e4e6ef; }
.theme-secondary .toggler-info input:checked + i {
  color: #00D0FF; }
.theme-secondary .toggler-success input:checked + i {
  color: #1dbfc1; }
.theme-secondary .toggler-danger input:checked + i {
  color: #ee3158; }
.theme-secondary .toggler-warning input:checked + i {
  color: #ffa800; }

/*---nav tabs---*/
.theme-secondary .nav-tabs.nav-tabs-primary .nav-link:hover, .theme-secondary .nav-tabs.nav-tabs-primary .nav-link:active, .theme-secondary .nav-tabs.nav-tabs-primary .nav-link:focus, .theme-secondary .nav-tabs.nav-tabs-primary .nav-link.active {
  border-color: #c4c8dc;
  background-color: transparent;
  color: #c4c8dc; }
.theme-secondary .nav-tabs.nav-tabs-info .nav-link:hover, .theme-secondary .nav-tabs.nav-tabs-info .nav-link:active, .theme-secondary .nav-tabs.nav-tabs-info .nav-link:focus, .theme-secondary .nav-tabs.nav-tabs-info .nav-link.active {
  border-color: #00a6cc;
  background-color: #00D0FF;
  color: #ffffff; }
.theme-secondary .nav-tabs.nav-tabs-success .nav-link:hover, .theme-secondary .nav-tabs.nav-tabs-success .nav-link:active, .theme-secondary .nav-tabs.nav-tabs-success .nav-link:focus, .theme-secondary .nav-tabs.nav-tabs-success .nav-link.active {
  border-color: #169395;
  background-color: transparent;
  color: #169395; }
.theme-secondary .nav-tabs.nav-tabs-danger .nav-link:hover, .theme-secondary .nav-tabs.nav-tabs-danger .nav-link:active, .theme-secondary .nav-tabs.nav-tabs-danger .nav-link:focus, .theme-secondary .nav-tabs.nav-tabs-danger .nav-link.active {
  border-color: #da123b;
  background-color: transparent;
  color: #da123b; }
.theme-secondary .nav-tabs.nav-tabs-warning .nav-link:hover, .theme-secondary .nav-tabs.nav-tabs-warning .nav-link:active, .theme-secondary .nav-tabs.nav-tabs-warning .nav-link:focus, .theme-secondary .nav-tabs.nav-tabs-warning .nav-link.active {
  border-color: #cc8600;
  background-color: transparent;
  color: #cc8600; }
.theme-secondary .nav-tabs-custom.tab-primary > .nav-tabs > li a.active {
  border-top-color: #c4c8dc; }
.theme-secondary .nav-tabs-custom.tab-info > .nav-tabs > li a.active {
  border-top-color: #00a6cc; }
.theme-secondary .nav-tabs-custom.tab-success > .nav-tabs > li a.active {
  border-top-color: #169395; }
.theme-secondary .nav-tabs-custom.tab-danger > .nav-tabs > li a.active {
  border-top-color: #da123b; }
.theme-secondary .nav-tabs-custom.tab-warning > .nav-tabs > li a.active {
  border-top-color: #cc8600; }
.theme-secondary .nav-tabs .nav-link.active {
  border-bottom-color: #e4e6ef;
  background-color: #e4e6ef;
  color: #ffffff; }
  .theme-secondary .nav-tabs .nav-link.active:hover, .theme-secondary .nav-tabs .nav-link.active:focus {
    border-bottom-color: #e4e6ef;
    background-color: #e4e6ef;
    color: #ffffff; }
.theme-secondary .nav-tabs .nav-item.open .nav-link {
  border-bottom-color: #e4e6ef;
  background-color: #e4e6ef; }
  .theme-secondary .nav-tabs .nav-item.open .nav-link:hover, .theme-secondary .nav-tabs .nav-item.open .nav-link:focus {
    border-bottom-color: #e4e6ef;
    background-color: #e4e6ef; }

/*---todo---*/
.theme-secondary .todo-list .primary {
  border-left-color: #e4e6ef; }
.theme-secondary .todo-list .info {
  border-left-color: #e4e6ef; }
.theme-secondary .todo-list .success {
  border-left-color: #1dbfc1; }
.theme-secondary .todo-list .danger {
  border-left-color: #ee3158; }
.theme-secondary .todo-list .warning {
  border-left-color: #ffa800; }

/*---timeline---*/
.theme-secondary .timeline .timeline-item > .timeline-event.timeline-event-primary {
  background-color: #e4e6ef;
  border: 1px solid #e4e6ef;
  color: #ffffff; }
  .theme-secondary .timeline .timeline-item > .timeline-event.timeline-event-primary:before, .theme-secondary .timeline .timeline-item > .timeline-event.timeline-event-primary:after {
    border-left-color: #e4e6ef;
    border-right-color: #e4e6ef; }
  .theme-secondary .timeline .timeline-item > .timeline-event.timeline-event-primary * {
    color: inherit; }
.theme-secondary .timeline .timeline-item > .timeline-event.timeline-event-info {
  background-color: #00D0FF;
  border: 1px solid #00D0FF;
  color: #ffffff; }
  .theme-secondary .timeline .timeline-item > .timeline-event.timeline-event-info:before, .theme-secondary .timeline .timeline-item > .timeline-event.timeline-event-info:after {
    border-left-color: #00D0FF;
    border-right-color: #00D0FF; }
  .theme-secondary .timeline .timeline-item > .timeline-event.timeline-event-info * {
    color: inherit; }
.theme-secondary .timeline .timeline-item > .timeline-event.timeline-event-success {
  background-color: #1dbfc1;
  border: 1px solid #1dbfc1;
  color: #ffffff; }
  .theme-secondary .timeline .timeline-item > .timeline-event.timeline-event-success:before, .theme-secondary .timeline .timeline-item > .timeline-event.timeline-event-success:after {
    border-left-color: #1dbfc1;
    border-right-color: #1dbfc1; }
  .theme-secondary .timeline .timeline-item > .timeline-event.timeline-event-success * {
    color: inherit; }
.theme-secondary .timeline .timeline-item > .timeline-event.timeline-event-danger {
  background-color: #ee3158;
  border: 1px solid #ee3158;
  color: #ffffff; }
  .theme-secondary .timeline .timeline-item > .timeline-event.timeline-event-danger:before, .theme-secondary .timeline .timeline-item > .timeline-event.timeline-event-danger:after {
    border-left-color: #ee3158;
    border-right-color: #ee3158; }
  .theme-secondary .timeline .timeline-item > .timeline-event.timeline-event-danger * {
    color: inherit; }
.theme-secondary .timeline .timeline-item > .timeline-event.timeline-event-warning {
  background-color: #ffa800;
  border: 1px solid #ffa800;
  color: #ffffff; }
  .theme-secondary .timeline .timeline-item > .timeline-event.timeline-event-warning:before, .theme-secondary .timeline .timeline-item > .timeline-event.timeline-event-warning:after {
    border-left-color: #ffa800;
    border-right-color: #ffa800; }
  .theme-secondary .timeline .timeline-item > .timeline-event.timeline-event-warning * {
    color: inherit; }
.theme-secondary .timeline .timeline-item > .timeline-point.timeline-point-primary {
  color: #e4e6ef;
  background-color: #ffffff; }
.theme-secondary .timeline .timeline-item > .timeline-point.timeline-point-info {
  color: #00D0FF;
  background-color: #ffffff; }
.theme-secondary .timeline .timeline-item > .timeline-point.timeline-point-success {
  color: #1dbfc1;
  background-color: #ffffff; }
.theme-secondary .timeline .timeline-item > .timeline-point.timeline-point-danger {
  color: #ee3158;
  background-color: #ffffff; }
.theme-secondary .timeline .timeline-item > .timeline-point.timeline-point-warning {
  color: #ffa800;
  background-color: #ffffff; }
.theme-secondary .timeline .timeline-label .label-primary {
  background-color: #e4e6ef; }
.theme-secondary .timeline .timeline-label .label-info {
  background-color: #00D0FF; }
.theme-secondary .timeline .timeline-label .label-success {
  background-color: #1dbfc1; }
.theme-secondary .timeline .timeline-label .label-danger {
  background-color: #ee3158; }
.theme-secondary .timeline .timeline-label .label-warning {
  background-color: #ffa800; }
.theme-secondary .timeline__year, .theme-secondary .timeline5:before, .theme-secondary .timeline__box:before, .theme-secondary .timeline__date {
  background-color: #e4e6ef; }
.theme-secondary .timeline__post {
  border-left: 3px solid #e4e6ef; }

/*---daterangepicker---*/
.theme-secondary .daterangepicker td.active {
  background-color: #e4e6ef; }
  .theme-secondary .daterangepicker td.active:hover {
    background-color: #e4e6ef; }
.theme-secondary .daterangepicker .input-mini.active {
  border: 1px solid #e4e6ef; }
.theme-secondary .ranges li:hover, .theme-secondary .ranges li:active, .theme-secondary .ranges li.active {
  border: 1px solid #e4e6ef;
  background-color: #e4e6ef; }

/*---control-sidebar---*/
.theme-secondary .control-sidebar .nav-tabs.control-sidebar-tabs > li > a:hover, .theme-secondary .control-sidebar .nav-tabs.control-sidebar-tabs > li > a:active, .theme-secondary .control-sidebar .nav-tabs.control-sidebar-tabs > li > a:focus {
  border-color: #e4e6ef;
  color: #e4e6ef; }
.theme-secondary .control-sidebar .nav-tabs.control-sidebar-tabs > li > a.active {
  border-color: #e4e6ef;
  color: #e4e6ef; }
  .theme-secondary .control-sidebar .nav-tabs.control-sidebar-tabs > li > a.active:hover, .theme-secondary .control-sidebar .nav-tabs.control-sidebar-tabs > li > a.active:active, .theme-secondary .control-sidebar .nav-tabs.control-sidebar-tabs > li > a.active:focus {
    border-color: #e4e6ef;
    color: #e4e6ef; }
.theme-secondary .control-sidebar .rpanel-title .btn:hover {
  color: #e4e6ef; }

/*---nav---*/
.theme-secondary .nav > li > a:hover, .theme-secondary .nav > li > a:active, .theme-secondary .nav > li > a:focus {
  color: #e4e6ef; }
.theme-secondary .nav-pills > li > a.active {
  border-top-color: #e4e6ef;
  background-color: #e4e6ef !important;
  color: #ffffff; }
  .theme-secondary .nav-pills > li > a.active:hover, .theme-secondary .nav-pills > li > a.active:focus {
    border-top-color: #e4e6ef;
    background-color: #e4e6ef !important;
    color: #ffffff; }
.theme-secondary .mailbox-nav .nav-pills > li > a:hover, .theme-secondary .mailbox-nav .nav-pills > li > a:focus {
  border-color: #e4e6ef; }
.theme-secondary .mailbox-nav .nav-pills > li > a.active {
  border-color: #e4e6ef; }
  .theme-secondary .mailbox-nav .nav-pills > li > a.active:hover, .theme-secondary .mailbox-nav .nav-pills > li > a.active:focus {
    border-color: #e4e6ef; }
.theme-secondary .nav-tabs-custom > .nav-tabs > li a.active {
  border-top-color: #e4e6ef; }
.theme-secondary .profile-tab li a.nav-link.active {
  border-bottom: 2px solid #e4e6ef; }
.theme-secondary .customtab li a.nav-link.active {
  border-bottom: 2px solid #e4e6ef; }

/*---form-element---*/
.theme-secondary .form-element .input-group .input-group-addon {
  background-image: linear-gradient(45deg, #e4e6ef, #00D0FF), linear-gradient(#3b6dc1, #3b6dc1); }
.theme-secondary .form-element .form-control {
  background-image: linear-gradient(45deg, #e4e6ef, #00D0FF), linear-gradient(#3b6dc1, #3b6dc1); }
  .theme-secondary .form-element .form-control:focus {
    background-image: linear-gradient(45deg, #e4e6ef, #00D0FF), linear-gradient(#3b6dc1, #3b6dc1); }
.theme-secondary .form-control:focus {
  border-color: #e4e6ef; }
.theme-secondary [type=checkbox]:checked.chk-col-primary + label:before {
  border-right: 2px solid #e4e6ef;
  border-bottom: 2px solid #e4e6ef; }
.theme-secondary [type=checkbox]:checked.chk-col-info + label:before {
  border-right: 2px solid #00D0FF;
  border-bottom: 2px solid #00D0FF; }
.theme-secondary [type=checkbox]:checked.chk-col-success + label:before {
  border-right: 2px solid #1dbfc1;
  border-bottom: 2px solid #1dbfc1; }
.theme-secondary [type=checkbox]:checked.chk-col-danger + label:before {
  border-right: 2px solid #ee3158;
  border-bottom: 2px solid #ee3158; }
.theme-secondary [type=checkbox]:checked.chk-col-warning + label:before {
  border-right: 2px solid #ffa800;
  border-bottom: 2px solid #ffa800; }
.theme-secondary [type=checkbox].filled-in:checked.chk-col-primary + label:after {
  border: 2px solid #e4e6ef;
  background-color: #e4e6ef; }
.theme-secondary [type=checkbox].filled-in:checked.chk-col-info + label:after {
  border: 2px solid #00D0FF;
  background-color: #00D0FF; }
.theme-secondary [type=checkbox].filled-in:checked.chk-col-success + label:after {
  border: 2px solid #1dbfc1;
  background-color: #1dbfc1; }
.theme-secondary [type=checkbox].filled-in:checked.chk-col-danger + label:after {
  border: 2px solid #ee3158;
  background-color: #ee3158; }
.theme-secondary [type=checkbox].filled-in:checked.chk-col-warning + label:after {
  border: 2px solid #ffa800;
  background-color: #ffa800; }
.theme-secondary [type=radio].radio-col-primary:checked + label:after {
  background-color: #e4e6ef;
  border-color: #e4e6ef;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-secondary [type=radio].with-gap.radio-col-primary:checked + label:before {
  border: 2px solid #e4e6ef;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-secondary [type=radio].with-gap.radio-col-primary:checked + label:after {
  background-color: #e4e6ef;
  border: 2px solid #e4e6ef;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-secondary [type=radio].radio-col-info:checked + label:after {
  background-color: #00D0FF;
  border-color: #00D0FF;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-secondary [type=radio].with-gap.radio-col-info:checked + label:before {
  border: 2px solid #00D0FF;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-secondary [type=radio].with-gap.radio-col-info:checked + label:after {
  background-color: #00D0FF;
  border: 2px solid #00D0FF;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-secondary [type=radio].radio-col-success:checked + label:after {
  background-color: #1dbfc1;
  border-color: #1dbfc1;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-secondary [type=radio].with-gap.radio-col-success:checked + label:before {
  border: 2px solid #1dbfc1;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-secondary [type=radio].with-gap.radio-col-success:checked + label:after {
  background-color: #1dbfc1;
  border: 2px solid #1dbfc1;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-secondary [type=radio].radio-col-danger:checked + label:after {
  background-color: #ee3158;
  border-color: #ee3158;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-secondary [type=radio].with-gap.radio-col-danger:checked + label:before {
  border: 2px solid #ee3158;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-secondary [type=radio].with-gap.radio-col-danger:checked + label:after {
  background-color: #ee3158;
  border: 2px solid #ee3158;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-secondary [type=radio].radio-col-warning:checked + label:after {
  background-color: #ffa800;
  border-color: #ffa800;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-secondary [type=radio].with-gap.radio-col-warning:checked + label:before {
  border: 2px solid #ffa800;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-secondary [type=radio].with-gap.radio-col-warning:checked + label:after {
  background-color: #ffa800;
  border: 2px solid #ffa800;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-secondary [type=checkbox]:checked + label:before {
  border-right: 2px solid #e4e6ef;
  border-bottom: 2px solid #e4e6ef; }
.theme-secondary [type=checkbox].filled-in:checked + label:after {
  border: 2px solid #e4e6ef;
  background-color: #e4e6ef; }
.theme-secondary [type=radio].with-gap:checked + label:before, .theme-secondary [type=radio].with-gap:checked + label:after {
  border: 2px solid #e4e6ef; }
.theme-secondary [type=radio].with-gap:checked + label:after {
  background-color: #e4e6ef;
  z-index: 0; }
.theme-secondary [type=radio]:checked + label:after {
  border: 2px solid #e4e6ef;
  background-color: #e4e6ef;
  z-index: 0; }
.theme-secondary [type=checkbox].filled-in.tabbed:checked:focus + label:after {
  border-color: #e4e6ef;
  background-color: #e4e6ef; }

/*---Calender---*/
.theme-secondary .fx-element-overlay .fx-card-item .fx-card-content a:hover {
  color: #e4e6ef; }
.theme-secondary .fx-element-overlay .fx-card-item .fx-overlay-1 .fx-info > li a:hover {
  background: #e4e6ef;
  border-color: #e4e6ef; }
.theme-secondary .fc-event, .theme-secondary .calendar-event {
  background: #e4e6ef; }

/*---Tabs---*/
.theme-secondary .tabs-vertical li .nav-link:hover, .theme-secondary .tabs-vertical li .nav-link:active, .theme-secondary .tabs-vertical li .nav-link:focus, .theme-secondary .tabs-vertical li .nav-link.active {
  background-color: #e4e6ef;
  color: #ffffff; }
.theme-secondary .customvtab .tabs-vertical li .nav-link:hover, .theme-secondary .customvtab .tabs-vertical li .nav-link:active, .theme-secondary .customvtab .tabs-vertical li .nav-link:focus, .theme-secondary .customvtab .tabs-vertical li .nav-link.active {
  border-right: 2px solid #e4e6ef;
  color: #e4e6ef; }
.theme-secondary .customtab2 li a.nav-link:hover, .theme-secondary .customtab2 li a.nav-link:active, .theme-secondary .customtab2 li a.nav-link.active {
  background-color: #e4e6ef; }

/*---Notification---*/
.theme-secondary .jq-icon-primary {
  background-color: #e4e6ef;
  color: #ffffff;
  border-color: #e4e6ef; }
.theme-secondary .jq-icon-info {
  background-color: #00D0FF;
  color: #ffffff;
  border-color: #00D0FF; }
.theme-secondary .jq-icon-success {
  background-color: #1dbfc1;
  color: #ffffff;
  border-color: #e4e6ef; }
.theme-secondary .jq-icon-error {
  background-color: #ee3158;
  color: #ffffff;
  border-color: #ee3158; }
.theme-secondary .jq-icon-danger {
  background-color: #ee3158;
  color: #ffffff;
  border-color: #ee3158; }
.theme-secondary .jq-icon-warning {
  background-color: #ffa800;
  color: #ffffff;
  border-color: #ffa800; }

/*---avatar---*/
.theme-secondary .avatar.status-primary::after {
  background-color: #e4e6ef; }
.theme-secondary .avatar.status-info::after {
  background-color: #00D0FF; }
.theme-secondary .avatar.status-success::after {
  background-color: #1dbfc1; }
.theme-secondary .avatar.status-danger::after {
  background-color: #ee3158; }
.theme-secondary .avatar.status-warning::after {
  background-color: #ffa800; }
.theme-secondary .avatar[class*='status-']::after {
  background-color: #e4e6ef; }
.theme-secondary .avatar-add:hover {
  background-color: #c4c8dc;
  border-color: #c4c8dc; }

/*---media---*/
.theme-secondary .media-chat.media-chat-reverse .media-body p {
  background-color: #e4e6ef; }
.theme-secondary .media-right-out a:hover {
  color: #c4c8dc; }

/*---control---*/
.theme-secondary .control input:checked:focus ~ .control_indicator {
  background-color: #e4e6ef; }
.theme-secondary .control input:checked ~ .control_indicator {
  background-color: #e4e6ef; }
.theme-secondary .control:hover input:not([disabled]):checked ~ .control_indicator {
  background-color: #e4e6ef; }

/*---flex---*/
.theme-secondary .flex-column > li > a.nav-link.active {
  border-left-color: #e4e6ef; }
  .theme-secondary .flex-column > li > a.nav-link.active:hover {
    border-left-color: #e4e6ef; }

/*---pagination---*/
.theme-secondary .pagination li a.current {
  border: 1px solid #e4e6ef;
  background-color: #e4e6ef; }
  .theme-secondary .pagination li a.current:hover {
    border: 1px solid #e4e6ef;
    background-color: #e4e6ef; }
.theme-secondary .pagination li a:hover {
  border: 1px solid #c4c8dc;
  background-color: #c4c8dc !important; }
.theme-secondary .dataTables_wrapper .dataTables_paginate .paginate_button.current {
  border: 1px solid #e4e6ef;
  background-color: #e4e6ef; }
  .theme-secondary .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    border: 1px solid #e4e6ef;
    background-color: #e4e6ef; }
.theme-secondary .paging_simple_numbers .pagination .paginate_button.active a {
  background-color: #e4e6ef; }
.theme-secondary .paging_simple_numbers .pagination .paginate_button:hover a {
  background-color: #e4e6ef; }
.theme-secondary .footable .pagination li a:hover, .theme-secondary .footable .pagination li a:active, .theme-secondary .footable .pagination li a.active {
  background-color: #e4e6ef; }

/*---dataTables---*/
.theme-secondary .dt-buttons .dt-button {
  background-color: #e4e6ef; }

/*---select2---*/
.theme-secondary .select2-container--default.select2-container--open {
  border-color: #e4e6ef; }
.theme-secondary .select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: #e4e6ef; }
.theme-secondary .select2-container--default .select2-search--dropdown .select2-search__field {
  border-color: #e4e6ef !important; }
.theme-secondary .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #e4e6ef !important; }
.theme-secondary .select2-container--default .select2-selection--multiple:focus {
  border-color: #e4e6ef !important; }
.theme-secondary .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #e4e6ef;
  border-color: #e4e6ef; }

/*---Other---*/
.theme-secondary .myadmin-dd .dd-list .dd-list .dd-handle:hover {
  color: #c4c8dc; }
.theme-secondary .myadmin-dd-empty .dd-list .dd3-handle:hover {
  color: #c4c8dc; }
.theme-secondary .myadmin-dd-empty .dd-list .dd3-content:hover {
  color: #c4c8dc; }
.theme-secondary [data-overlay-primary]::before {
  background: #c4c8dc; }

/*---wizard---*/
.theme-secondary .wizard-content .wizard > .steps > ul > li.current {
  border: 2px solid #e4e6ef;
  background-color: #e4e6ef; }
.theme-secondary .wizard-content .wizard > .steps > ul > li.done {
  border-color: #c4c8dc;
  background-color: #c4c8dc; }
.theme-secondary .wizard-content .wizard > .actions > ul > li > a {
  background-color: #e4e6ef; }
.theme-secondary .wizard-content .wizard.wizard-circle > .steps > ul > li:after {
  background-color: #e4e6ef; }
.theme-secondary .wizard-content .wizard.wizard-circle > .steps > ul > li:before {
  background-color: #e4e6ef; }
.theme-secondary .wizard-content .wizard.wizard-notification > .steps > ul > li:after {
  background-color: #e4e6ef; }
.theme-secondary .wizard-content .wizard.wizard-notification > .steps > ul > li:before {
  background-color: #e4e6ef; }
.theme-secondary .wizard-content .wizard.wizard-notification > .steps > ul > li.current .step {
  border: 2px solid #e4e6ef;
  color: #e4e6ef; }
  .theme-secondary .wizard-content .wizard.wizard-notification > .steps > ul > li.current .step:after {
    border-top-color: #e4e6ef; }
.theme-secondary .wizard-content .wizard.wizard-notification > .steps > ul > li.done .step:after {
  border-top-color: #e4e6ef; }

@media (max-width: 767px) {
  .theme-secondary .wizard-content .wizard > .steps > ul > li:last-child:after {
    background-color: #e4e6ef; } }
@media (max-width: 575px) {
  .theme-secondary .wizard-content .wizard > .steps > ul > li.current:after {
    background-color: #e4e6ef; } }
/*---slider---*/
.theme-secondary #primary .slider-selection {
  background-color: #e4e6ef; }
.theme-secondary #info .slider-selection {
  background-color: #00D0FF; }
.theme-secondary #success .slider-selection {
  background-color: #1dbfc1; }
.theme-secondary #danger .slider-selection {
  background-color: #ee3158; }
.theme-secondary #warning .slider-selection {
  background-color: #ffa800; }

/*---horizontal-timeline---*/
.theme-secondary .cd-horizontal-timeline .events a.selected::after {
  background: #e4e6ef;
  border-color: #e4e6ef; }
.theme-secondary .cd-horizontal-timeline .events a.older-event::after {
  border-color: #e4e6ef; }
.theme-secondary .cd-horizontal-timeline .filling-line {
  background: #e4e6ef; }
.theme-secondary .cd-horizontal-timeline a {
  color: #e4e6ef; }
  .theme-secondary .cd-horizontal-timeline a:hover, .theme-secondary .cd-horizontal-timeline a:focus {
    color: #e4e6ef; }
.theme-secondary .cd-timeline-navigation a:hover, .theme-secondary .cd-timeline-navigation a:focus {
  border-color: #e4e6ef; }

/**************************************
Theme Info Color
**************************************/
.bg-gradient-info, .theme-info .bg-gradient-info, .theme-info .art-bg {
  background: linear-gradient(45deg, #00D0FF, #3246D3); }

.bg-light-body {
  background: transparent; }

.theme-info.fixed .main-header {
  background: transparent; }
.theme-info .main-header {
  background: transparent; }

.theme-info.onlyheader .art-bg {
  background-image: none; }

.bg-gradient-info-dark, .dark-skin.theme-info .bg-gradient-info, .dark-skin.theme-info .art-bg {
  background-image: linear-gradient(45deg, #007d99, #1c2983); }

.bg-dark-body {
  background: #0c1a32; }

.dark-skin.theme-info.fixed .main-header {
  background: transparent; }
.dark-skin.theme-info .main-header {
  background: transparent; }

@media (max-width: 767px) {
  .theme-info.fixed .main-header {
    background-image: #e4e6ef; }
    .theme-info.fixed .main-header.navbar {
      background: none; }

  .dark-skin.theme-info.fixed .main-header {
    background-image: #0c1a32; } }
.theme-info a:hover, .theme-info a:active, .theme-info a:focus {
  color: #00D0FF; }
.theme-info .main-sidebar .svg-icon {
  filter: invert(0.6) sepia(1) saturate(1) hue-rotate(185deg); }
  .theme-info .main-sidebar .svg-icon:hover, .theme-info .main-sidebar .svg-icon:active, .theme-info .main-sidebar .svg-icon:focus {
    filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg); }
.theme-info .main-sidebar a:hover .svg-icon, .theme-info .main-sidebar a:active .svg-icon, .theme-info .main-sidebar a:focus .svg-icon {
  filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg); }
.theme-info .svg-icon {
  filter: invert(0.6) sepia(1) saturate(1) hue-rotate(185deg); }
  .theme-info .svg-icon:hover, .theme-info .svg-icon:active, .theme-info .svg-icon:focus {
    filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg); }
.theme-info a:hover .svg-icon, .theme-info a:active .svg-icon, .theme-info a:focus .svg-icon {
  filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg); }

.theme-info.light-skin .sidebar-menu > li.active.treeview > a {
  background: transparent;
  color: #b5b5c3 !important; }
  .theme-info.light-skin .sidebar-menu > li.active.treeview > a > i {
    color: #ffffff; }
  .theme-info.light-skin .sidebar-menu > li.active.treeview > a > svg {
    color: #ffffff;
    fill: rgba(1, 104, 250, 0.2); }
  .theme-info.light-skin .sidebar-menu > li.active.treeview > a:after {
    border-color: transparent #fafafa transparent transparent !important; }
.theme-info.light-skin .sidebar-menu > li.treeview .treeview-menu li a {
  color: #b5b5c3; }
.theme-info.light-skin.sidebar-mini.sidebar-collapse .sidebar-menu > li.active > a > span {
  background: #00D0FF !important; }
.theme-info.dark-skin .sidebar-menu > li.active > a:after {
  border-color: transparent #333333 transparent transparent !important; }
.theme-info.dark-skin .sidebar-menu > li.active.treeview > a {
  background: transparent;
  color: #b5b5c3 !important; }
  .theme-info.dark-skin .sidebar-menu > li.active.treeview > a > i {
    color: #ffffff; }
  .theme-info.dark-skin .sidebar-menu > li.active.treeview > a:after {
    border-color: transparent #fafafa transparent transparent !important; }
.theme-info.dark-skin .sidebar-menu > li.active.treeview .treeview-menu li a {
  color: #b5b5c3; }
.theme-info.dark-skin.sidebar-mini.sidebar-collapse .sidebar-menu > li.active > a > span {
  background: #00D0FF !important; }
.theme-info.light-skin .sidebar-menu > li:hover, .theme-info.light-skin .sidebar-menu > li:active, .theme-info.light-skin .sidebar-menu > li.active {
  background-color: rgba(0, 208, 255, 0);
  color: white;
  border-left: 5px solid rgba(0, 208, 255, 0); }
  .theme-info.light-skin .sidebar-menu > li:hover a, .theme-info.light-skin .sidebar-menu > li:active a, .theme-info.light-skin .sidebar-menu > li.active a {
    color: white; }
.theme-info.light-skin .sidebar-menu > li.active {
  background-color: rgba(0, 208, 255, 0);
  color: white;
  border-left: 5px solid #00d0ff; }
  .theme-info.light-skin .sidebar-menu > li.active a {
    color: white;
    background-color: transparent; }
    .theme-info.light-skin .sidebar-menu > li.active a > i {
      color: #ffffff;
      background-color: rgba(0, 208, 255, 0); }
    .theme-info.light-skin .sidebar-menu > li.active a > svg {
      color: #ffffff;
      fill: rgba(1, 104, 250, 0.2); }
    .theme-info.light-skin .sidebar-menu > li.active a img.svg-icon {
      filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg); }
  .theme-info.light-skin .sidebar-menu > li.active .treeview-menu li.active {
    background-color: rgba(0, 208, 255, 0);
    color: white; }
    .theme-info.light-skin .sidebar-menu > li.active .treeview-menu li.active a {
      color: white; }
      .theme-info.light-skin .sidebar-menu > li.active .treeview-menu li.active a > i {
        color: white;
        background-color: rgba(0, 208, 255, 0); }
  .theme-info.light-skin .sidebar-menu > li.active .treeview-menu li a > i {
    color: #b5b5c3;
    background-color: rgba(0, 208, 255, 0); }
  .theme-info.light-skin .sidebar-menu > li.active .treeview-menu li.treeview.active {
    background-color: rgba(0, 208, 255, 0);
    color: white; }
    .theme-info.light-skin .sidebar-menu > li.active .treeview-menu li.treeview.active a {
      color: white; }
      .theme-info.light-skin .sidebar-menu > li.active .treeview-menu li.treeview.active a > i {
        color: white;
        background-color: rgba(0, 208, 255, 0); }
  .theme-info.light-skin .sidebar-menu > li.active .treeview-menu li.treeview .treeview-menu li.active {
    background-color: rgba(0, 208, 255, 0);
    color: white; }
    .theme-info.light-skin .sidebar-menu > li.active .treeview-menu li.treeview .treeview-menu li.active a {
      color: white; }
      .theme-info.light-skin .sidebar-menu > li.active .treeview-menu li.treeview .treeview-menu li.active a > i {
        color: white;
        background-color: rgba(0, 208, 255, 0); }
  .theme-info.light-skin .sidebar-menu > li.active .treeview-menu li.treeview .treeview-menu li a {
    color: #b5b5c3; }
    .theme-info.light-skin .sidebar-menu > li.active .treeview-menu li.treeview .treeview-menu li a > i {
      color: #b5b5c3;
      background-color: rgba(0, 208, 255, 0); }
.theme-info.rtl.light-skin .sidebar-menu > li:hover, .theme-info.rtl.light-skin .sidebar-menu > li:active, .theme-info.rtl.light-skin .sidebar-menu > li.active {
  border-left: 0px solid rgba(0, 208, 255, 0);
  border-right: 5px solid rgba(0, 208, 255, 0); }
.theme-info.rtl.light-skin .sidebar-menu > li.active {
  border-left: 0px solid #00d0ff;
  border-right: 5px solid #00d0ff; }
.theme-info.dark-skin .sidebar-menu > li.active {
  background-color: rgba(0, 208, 255, 0);
  color: white;
  border-left: 5px solid #00d0ff; }
  .theme-info.dark-skin .sidebar-menu > li.active a {
    color: white;
    background-color: transparent; }
    .theme-info.dark-skin .sidebar-menu > li.active a > i {
      color: white; }
    .theme-info.dark-skin .sidebar-menu > li.active a > svg {
      color: #ffffff;
      fill: rgba(1, 104, 250, 0.2); }
    .theme-info.dark-skin .sidebar-menu > li.active a img.svg-icon {
      filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg); }
  .theme-info.dark-skin .sidebar-menu > li.active .treeview-menu li.active {
    background-color: rgba(0, 208, 255, 0);
    color: white; }
    .theme-info.dark-skin .sidebar-menu > li.active .treeview-menu li.active a {
      color: white !important; }
.theme-info.rtl.dark-skin .sidebar-menu > li.active {
  border-left: 0px solid #00d0ff;
  border-right: 5px solid #00d0ff; }

@media (min-width: 768px) {
  .sidebar-mini.sidebar-collapse .sidebar-menu > li.active.menu-open {
    background-color: rgba(0, 208, 255, 0.2);
    color: #00d0ff; } }
/*---Main Nav---*/
.theme-info .sm-blue li.current > a, .theme-info .sm-blue li.highlighted > a {
  background: #00D0FF;
  color: #ffffff !important; }
  .theme-info .sm-blue li.current > a:hover, .theme-info .sm-blue li.current > a:active, .theme-info .sm-blue li.current > a:focus, .theme-info .sm-blue li.highlighted > a:hover, .theme-info .sm-blue li.highlighted > a:active, .theme-info .sm-blue li.highlighted > a:focus {
    background: #00D0FF;
    color: #ffffff !important; }
.theme-info .sm-blue a.current, .theme-info .sm-blue a.highlighted {
  background: #00D0FF;
  color: #ffffff !important; }
.theme-info .sm-blue a:hover, .theme-info .sm-blue a:active, .theme-info .sm-blue a:focus {
  background: #00D0FF;
  color: #ffffff !important; }
.theme-info .sm-blue ul a:hover, .theme-info .sm-blue ul a:active, .theme-info .sm-blue ul a:focus {
  background: #ebedf3;
  color: #00D0FF !important; }
.theme-info .sm-blue ul a.highlighted {
  background: #ebedf3;
  color: #00D0FF !important; }

.dark-skin.theme-info .sm-blue a.current, .dark-skin.theme-info .sm-blue a.highlighted {
  background: #00D0FF;
  color: #ffffff !important; }
.dark-skin.theme-info .sm-blue a:hover, .dark-skin.theme-info .sm-blue a:active, .dark-skin.theme-info .sm-blue a:focus {
  background: #00D0FF;
  color: #ffffff !important; }
.dark-skin.theme-info .sm-blue ul a:hover, .dark-skin.theme-info .sm-blue ul a:active, .dark-skin.theme-info .sm-blue ul a:focus {
  background: #29354b;
  color: #00D0FF !important; }
.dark-skin.theme-info .sm-blue ul a.highlighted {
  background: #29354b;
  color: #00D0FF !important; }

/*---Primary Button---*/
.theme-info .btn-link {
  color: #00D0FF; }
.theme-info .btn-primary {
  background-color: #00D0FF;
  border-color: #00D0FF;
  color: #ffffff; }
  .theme-info .btn-primary:hover, .theme-info .btn-primary:active, .theme-info .btn-primary:focus, .theme-info .btn-primary.active {
    background-color: #00a6cc !important;
    border-color: #00a6cc !important;
    color: #ffffff !important; }
  .theme-info .btn-primary:disabled {
    background-color: #66e3ff;
    border-color: #00D0FF;
    opacity: 0.5; }
  .theme-info .btn-primary.disabled {
    background-color: #66e3ff;
    border-color: #00D0FF;
    opacity: 0.5; }
.theme-info .show > .btn-primary.dropdown-toggle {
  background-color: #00a6cc !important;
  border-color: #00a6cc !important;
  color: #ffffff; }
.theme-info .btn-outline.btn-primary {
  color: #00D0FF;
  background-color: transparent;
  border-color: #00D0FF !important; }
  .theme-info .btn-outline.btn-primary:hover, .theme-info .btn-outline.btn-primary:active, .theme-info .btn-outline.btn-primary.active {
    background-color: #00a6cc !important;
    border-color: #00a6cc !important;
    color: #ffffff !important; }
.theme-info .show > .btn-outline.btn-primary.dropdown-toggle {
  background-color: #00a6cc !important;
  border-color: #00a6cc !important;
  color: #ffffff; }
.theme-info .btn-flat.btn-primary {
  color: #00D0FF !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-info .btn-flat.btn-primary:hover, .theme-info .btn-flat.btn-primary:active, .theme-info .btn-flat.btn-primary.active {
    background-color: #00a6cc !important;
    border-color: #00a6cc !important;
    color: #ffffff !important; }

/*---info Button---*/
.theme-info .btn-info {
  background-color: #3246D3;
  border-color: #3246D3;
  color: #ffffff; }
  .theme-info .btn-info:hover, .theme-info .btn-info:active, .theme-info .btn-info:focus, .theme-info .btn-info.active {
    background-color: #2536ad !important;
    border-color: #2536ad !important;
    color: #ffffff !important; }
  .theme-info .btn-info:disabled {
    background-color: #8692e5;
    border-color: #3246D3;
    opacity: 0.5; }
  .theme-info .btn-info.disabled {
    background-color: #8692e5;
    border-color: #3246D3;
    opacity: 0.5; }
.theme-info .show > .btn-info.dropdown-toggle {
  background-color: #2536ad !important;
  border-color: #2536ad !important;
  color: #ffffff; }
.theme-info .btn-outline.btn-info {
  color: #3246D3;
  background-color: transparent;
  border-color: #3246D3 !important; }
  .theme-info .btn-outline.btn-info:hover, .theme-info .btn-outline.btn-info:active, .theme-info .btn-outline.btn-info.active {
    background-color: #2536ad !important;
    border-color: #2536ad !important;
    color: #ffffff !important; }
.theme-info .show > .btn-outline.btn-info.dropdown-toggle {
  background-color: #2536ad !important;
  border-color: #2536ad !important;
  color: #ffffff; }
.theme-info .btn-flat.btn-info {
  color: #3246D3 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-info .btn-flat.btn-info:hover, .theme-info .btn-flat.btn-info:active, .theme-info .btn-flat.btn-info.active {
    background-color: #2536ad !important;
    border-color: #2536ad !important;
    color: #ffffff !important; }

/*---Success Button---*/
.theme-info .btn-success {
  background-color: #1dbfc1;
  border-color: #1dbfc1;
  color: #ffffff; }
  .theme-info .btn-success:hover, .theme-info .btn-success:active, .theme-info .btn-success:focus, .theme-info .btn-success.active {
    background-color: #169395 !important;
    border-color: #169395 !important;
    color: #ffffff !important; }
  .theme-info .btn-success:disabled {
    background-color: #5de5e7;
    border-color: #1dbfc1;
    opacity: 0.5; }
  .theme-info .btn-success.disabled {
    background-color: #5de5e7;
    border-color: #1dbfc1;
    opacity: 0.5; }
.theme-info .show > .btn-success.dropdown-toggle {
  background-color: #169395 !important;
  border-color: #169395 !important;
  color: #ffffff; }
.theme-info .btn-outline.btn-success {
  color: #1dbfc1;
  background-color: transparent;
  border-color: #1dbfc1 !important; }
  .theme-info .btn-outline.btn-success:hover, .theme-info .btn-outline.btn-success:active, .theme-info .btn-outline.btn-success.active {
    background-color: #169395 !important;
    border-color: #169395 !important;
    color: #ffffff !important; }
.theme-info .show > .btn-outline.btn-success.dropdown-toggle {
  background-color: #169395 !important;
  border-color: #169395 !important;
  color: #ffffff; }
.theme-info .btn-flat.btn-success {
  color: #1dbfc1 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-info .btn-flat.btn-success:hover, .theme-info .btn-flat.btn-success:active, .theme-info .btn-flat.btn-success.active {
    background-color: #169395 !important;
    border-color: #169395 !important;
    color: #ffffff !important; }

/*---Danger Button---*/
.theme-info .btn-danger {
  background-color: #ee3158;
  border-color: #ee3158;
  color: #ffffff; }
  .theme-info .btn-danger:hover, .theme-info .btn-danger:active, .theme-info .btn-danger:focus, .theme-info .btn-danger.active {
    background-color: #da123b !important;
    border-color: #da123b !important;
    color: #ffffff !important; }
  .theme-info .btn-danger:disabled {
    background-color: #f68fa4;
    border-color: #ee3158;
    opacity: 0.5; }
  .theme-info .btn-danger.disabled {
    background-color: #f68fa4;
    border-color: #ee3158;
    opacity: 0.5; }
.theme-info .show > .btn-danger.dropdown-toggle {
  background-color: #da123b !important;
  border-color: #da123b !important;
  color: #ffffff; }
.theme-info .btn-outline.btn-danger {
  color: #ee3158;
  background-color: transparent;
  border-color: #ee3158 !important; }
  .theme-info .btn-outline.btn-danger:hover, .theme-info .btn-outline.btn-danger:active, .theme-info .btn-outline.btn-danger.active {
    background-color: #da123b !important;
    border-color: #da123b !important;
    color: #ffffff !important; }
.theme-info .show > .btn-outline.btn-danger.dropdown-toggle {
  background-color: #da123b !important;
  border-color: #da123b !important;
  color: #ffffff; }
.theme-info .btn-flat.btn-danger {
  color: #ee3158 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-info .btn-flat.btn-danger:hover, .theme-info .btn-flat.btn-danger:active, .theme-info .btn-flat.btn-danger.active {
    background-color: #da123b !important;
    border-color: #da123b !important;
    color: #ffffff !important; }

/*---Warning Button---*/
.theme-info .btn-warning {
  background-color: #ffa800;
  border-color: #ffa800;
  color: #ffffff; }
  .theme-info .btn-warning:hover, .theme-info .btn-warning:active, .theme-info .btn-warning:focus, .theme-info .btn-warning.active {
    background-color: #cc8600 !important;
    border-color: #cc8600 !important;
    color: #ffffff !important; }
  .theme-info .btn-warning:disabled {
    background-color: #ffcb66;
    border-color: #ffa800;
    opacity: 0.5; }
  .theme-info .btn-warning.disabled {
    background-color: #ffcb66;
    border-color: #ffa800;
    opacity: 0.5; }
.theme-info .show > .btn-warning.dropdown-toggle {
  background-color: #cc8600 !important;
  border-color: #cc8600 !important;
  color: #ffffff; }
.theme-info .btn-outline.btn-warning {
  color: #ffa800;
  background-color: transparent;
  border-color: #ffa800 !important; }
  .theme-info .btn-outline.btn-warning:hover, .theme-info .btn-outline.btn-warning:active, .theme-info .btn-outline.btn-warning.active {
    background-color: #cc8600 !important;
    border-color: #cc8600 !important;
    color: #ffffff !important; }
.theme-info .show > .btn-outline.btn-warning.dropdown-toggle {
  background-color: #cc8600 !important;
  border-color: #cc8600 !important;
  color: #ffffff; }
.theme-info .btn-flat.btn-warning {
  color: #ffa800 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-info .btn-flat.btn-warning:hover, .theme-info .btn-flat.btn-warning:active, .theme-info .btn-flat.btn-warning.active {
    background-color: #cc8600 !important;
    border-color: #cc8600 !important;
    color: #ffffff !important; }

/*---Primary Button light---*/
.theme-info .btn-primary-light {
  background-color: #e1f9ff;
  border-color: #e1f9ff;
  color: #00D0FF; }
  .theme-info .btn-primary-light:hover, .theme-info .btn-primary-light:active, .theme-info .btn-primary-light:focus, .theme-info .btn-primary-light.active {
    background-color: #00D0FF !important;
    border-color: #00D0FF !important;
    color: #ffffff !important; }
  .theme-info .btn-primary-light:disabled {
    background-color: white;
    border-color: #e1f9ff;
    opacity: 0.5; }
  .theme-info .btn-primary-light.disabled {
    background-color: white;
    border-color: #e1f9ff;
    opacity: 0.5; }
.theme-info .show > .btn-primary-light.dropdown-toggle {
  background-color: #00D0FF !important;
  border-color: #00D0FF !important;
  color: #ffffff; }
.theme-info .btn-outline.btn-primary-light {
  color: #00D0FF;
  background-color: transparent;
  border-color: #e1f9ff !important; }
  .theme-info .btn-outline.btn-primary-light:hover, .theme-info .btn-outline.btn-primary-light:active, .theme-info .btn-outline.btn-primary-light.active {
    background-color: #00D0FF !important;
    border-color: #00D0FF !important;
    color: #ffffff !important; }
.theme-info .show > .btn-outline.btn-primary-light.dropdown-toggle {
  background-color: #00D0FF !important;
  border-color: #00D0FF !important;
  color: #ffffff; }
.theme-info .btn-flat.btn-primary-light {
  color: #00D0FF !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-info .btn-flat.btn-primary-light:hover, .theme-info .btn-flat.btn-primary-light:active, .theme-info .btn-flat.btn-primary-light.active {
    background-color: #00D0FF !important;
    border-color: #00D0FF !important;
    color: #ffffff !important; }

/*---info Button light---*/
.theme-info .btn-info-light {
  background-color: #dbdfff;
  border-color: #dbdfff;
  color: #3246D3; }
  .theme-info .btn-info-light:hover, .theme-info .btn-info-light:active, .theme-info .btn-info-light:focus, .theme-info .btn-info-light.active {
    background-color: #3246D3 !important;
    border-color: #3246D3 !important;
    color: #ffffff !important; }
  .theme-info .btn-info-light:disabled {
    background-color: white;
    border-color: #dbdfff;
    opacity: 0.5; }
  .theme-info .btn-info-light.disabled {
    background-color: white;
    border-color: #dbdfff;
    opacity: 0.5; }
.theme-info .show > .btn-info.dropdown-toggle {
  background-color: #3246D3 !important;
  border-color: #3246D3 !important;
  color: #ffffff; }
.theme-info .btn-outline.btn-info-light {
  color: #3246D3;
  background-color: transparent;
  border-color: #dbdfff !important; }
  .theme-info .btn-outline.btn-info-light:hover, .theme-info .btn-outline.btn-info-light:active, .theme-info .btn-outline.btn-info-light.active {
    background-color: #3246D3 !important;
    border-color: #3246D3 !important;
    color: #ffffff !important; }
.theme-info .show > .btn-outline.btn-info-light.dropdown-toggle {
  background-color: #3246D3 !important;
  border-color: #3246D3 !important;
  color: #ffffff; }
.theme-info .btn-flat.btn-info-light {
  color: #3246D3 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-info .btn-flat.btn-info-light:hover, .theme-info .btn-flat.btn-info-light:active, .theme-info .btn-flat.btn-info-light.active {
    background-color: #3246D3 !important;
    border-color: #3246D3 !important;
    color: #ffffff !important; }

/*---Success Button light---*/
.theme-info .btn-success-light {
  background-color: #e8f9f9;
  border-color: #e8f9f9;
  color: #1dbfc1; }
  .theme-info .btn-success-light:hover, .theme-info .btn-success-light:active, .theme-info .btn-success-light:focus, .theme-info .btn-success-light.active {
    background-color: #1dbfc1 !important;
    border-color: #1dbfc1 !important;
    color: #ffffff !important; }
  .theme-info .btn-success-light:disabled {
    background-color: white;
    border-color: #e8f9f9;
    opacity: 0.5; }
  .theme-info .btn-success-light.disabled {
    background-color: white;
    border-color: #e8f9f9;
    opacity: 0.5; }
.theme-info .show > .btn-success-light.dropdown-toggle {
  background-color: #1dbfc1 !important;
  border-color: #1dbfc1 !important;
  color: #ffffff; }
.theme-info .btn-outline.btn-success-light {
  color: #1dbfc1;
  background-color: transparent;
  border-color: #e8f9f9 !important; }
  .theme-info .btn-outline.btn-success-light:hover, .theme-info .btn-outline.btn-success-light:active, .theme-info .btn-outline.btn-success-light.active {
    background-color: #1dbfc1 !important;
    border-color: #1dbfc1 !important;
    color: #ffffff !important; }
.theme-info .show > .btn-outline.btn-success-light.dropdown-toggle {
  background-color: #1dbfc1 !important;
  border-color: #1dbfc1 !important;
  color: #ffffff; }
.theme-info .btn-flat.btn-success-light {
  color: #1dbfc1 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-info .btn-flat.btn-success-light:hover, .theme-info .btn-flat.btn-success-light:active, .theme-info .btn-flat.btn-success-light.active {
    background-color: #1dbfc1 !important;
    border-color: #1dbfc1 !important;
    color: #ffffff !important; }

/*---Danger Button light---*/
.theme-info .btn-danger-light {
  background-color: #ffd6de;
  border-color: #ffd6de;
  color: #ee3158; }
  .theme-info .btn-danger-light:hover, .theme-info .btn-danger-light:active, .theme-info .btn-danger-light:focus, .theme-info .btn-danger-light.active {
    background-color: #ee3158 !important;
    border-color: #ee3158 !important;
    color: #ffffff !important; }
  .theme-info .btn-danger-light:disabled {
    background-color: white;
    border-color: #ffd6de;
    opacity: 0.5; }
  .theme-info .btn-danger-light.disabled {
    background-color: white;
    border-color: #ffd6de;
    opacity: 0.5; }
.theme-info .show > .btn-danger-light.dropdown-toggle {
  background-color: #ee3158 !important;
  border-color: #ee3158 !important;
  color: #ffffff; }
.theme-info .btn-outline.btn-danger-light {
  color: #ee3158;
  background-color: transparent;
  border-color: #ffd6de !important; }
  .theme-info .btn-outline.btn-danger-light:hover, .theme-info .btn-outline.btn-danger-light:active, .theme-info .btn-outline.btn-danger-light.active {
    background-color: #ee3158 !important;
    border-color: #ee3158 !important;
    color: #ffffff !important; }
.theme-info .show > .btn-outline.btn-danger-light.dropdown-toggle {
  background-color: #ee3158 !important;
  border-color: #ee3158 !important;
  color: #ffffff; }
.theme-info .btn-flat.btn-danger-light {
  color: #ee3158 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-info .btn-flat.btn-danger-light:hover, .theme-info .btn-flat.btn-danger-light:active, .theme-info .btn-flat.btn-danger-light.active {
    background-color: #ee3158 !important;
    border-color: #ee3158 !important;
    color: #ffffff !important; }

/*---Warning Button light---*/
.theme-info .btn-warning-light {
  background-color: #fff8ea;
  border-color: #fff8ea;
  color: #ffa800; }
  .theme-info .btn-warning-light:hover, .theme-info .btn-warning-light:active, .theme-info .btn-warning-light:focus, .theme-info .btn-warning-light.active {
    background-color: #ffa800 !important;
    border-color: #ffa800 !important;
    color: #ffffff !important; }
  .theme-info .btn-warning-light:disabled {
    background-color: white;
    border-color: #fff8ea;
    opacity: 0.5; }
  .theme-info .btn-warning-light.disabled {
    background-color: white;
    border-color: #fff8ea;
    opacity: 0.5; }
.theme-info .show > .btn-warning-light.dropdown-toggle {
  background-color: #ffa800 !important;
  border-color: #ffa800 !important;
  color: #ffffff; }
.theme-info .btn-outline.btn-warning-light {
  color: #ffa800;
  background-color: transparent;
  border-color: #fff8ea !important; }
  .theme-info .btn-outline.btn-warning-light:hover, .theme-info .btn-outline.btn-warning-light:active, .theme-info .btn-outline.btn-warning-light.active {
    background-color: #ffa800 !important;
    border-color: #ffa800 !important;
    color: #ffffff !important; }
.theme-info .show > .btn-outline.btn-warning-light.dropdown-toggle {
  background-color: #ffa800 !important;
  border-color: #ffa800 !important;
  color: #ffffff; }
.theme-info .btn-flat.btn-warning-light {
  color: #ffa800 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-info .btn-flat.btn-warning-light:hover, .theme-info .btn-flat.btn-warning-light:active, .theme-info .btn-flat.btn-warning-light.active {
    background-color: #ffa800 !important;
    border-color: #ffa800 !important;
    color: #ffffff !important; }

/*---callout---*/
.theme-info .callout.callout-primary {
  border-color: #00D0FF;
  background-color: #00D0FF !important; }
.theme-info .callout.callout-info {
  border-color: #3246D3;
  background-color: #3246D3 !important; }
.theme-info .callout.callout-success {
  border-color: #1dbfc1;
  background-color: #1dbfc1 !important; }
.theme-info .callout.callout-danger {
  border-color: #ee3158;
  background-color: #ee3158 !important; }
.theme-info .callout.callout-warning {
  border-color: #ffa800;
  background-color: #ffa800 !important; }

/*---alert---*/
.theme-info .alert-primary {
  border-color: #00D0FF;
  background-color: #00D0FF !important;
  color: #ffffff; }
.theme-info .alert-info {
  border-color: #3246D3;
  background-color: #3246D3 !important;
  color: #ffffff; }
.theme-info .alert-success {
  border-color: #1dbfc1;
  background-color: #1dbfc1 !important;
  color: #ffffff; }
.theme-info .alert-danger {
  border-color: #ee3158;
  background-color: #ee3158 !important;
  color: #ffffff; }
.theme-info .alert-error {
  border-color: #ee3158;
  background-color: #ee3158 !important;
  color: #ffffff; }
.theme-info .alert-warning {
  border-color: #ffa800;
  background-color: #ffa800 !important;
  color: #ffffff; }

/*---direct-chat---*/
.theme-info .direct-chat-primary .right > .direct-chat-text p {
  background-color: #00D0FF;
  color: #ffffff; }
.theme-info .direct-chat-primary .right > .direct-chat-text:before, .theme-info .direct-chat-primary .right > .direct-chat-text:after {
  border-left-color: #00D0FF; }
.theme-info .direct-chat-info .right > .direct-chat-text p {
  background-color: #3246D3;
  color: #ffffff; }
.theme-info .direct-chat-info .right > .direct-chat-text:before, .theme-info .direct-chat-info .right > .direct-chat-text:after {
  border-left-color: #3246D3; }
.theme-info .direct-chat-success .right > .direct-chat-text p {
  background-color: #1dbfc1;
  color: #ffffff; }
.theme-info .direct-chat-success .right > .direct-chat-text:before, .theme-info .direct-chat-success .right > .direct-chat-text:after {
  border-left-color: #1dbfc1; }
.theme-info .direct-chat-danger .right > .direct-chat-text p {
  background-color: #ee3158;
  color: #ffffff; }
.theme-info .direct-chat-danger .right > .direct-chat-text:before, .theme-info .direct-chat-danger .right > .direct-chat-text:after {
  border-left-color: #ee3158; }
.theme-info .direct-chat-warning .right > .direct-chat-text p {
  background-color: #ffa800;
  color: #ffffff; }
.theme-info .direct-chat-warning .right > .direct-chat-text:before, .theme-info .direct-chat-warning .right > .direct-chat-text:after {
  border-left-color: #ffa800; }
.theme-info .right .direct-chat-text p {
  background-color: #00D0FF; }

/*---modal---*/
.theme-info .modal-primary .modal-footer, .theme-info .modal-primary .modal-header {
  border-color: #00D0FF; }
.theme-info .modal-primary .modal-body {
  background-color: #00D0FF !important; }
.theme-info .modal-info .modal-footer, .theme-info .modal-info .modal-header {
  border-color: #3246D3; }
.theme-info .modal-info .modal-body {
  background-color: #3246D3 !important; }
.theme-info .modal-success .modal-footer, .theme-info .modal-success .modal-header {
  border-color: #1dbfc1; }
.theme-info .modal-success .modal-body {
  background-color: #1dbfc1 !important; }
.theme-info .modal-danger .modal-footer, .theme-info .modal-danger .modal-header {
  border-color: #ee3158; }
.theme-info .modal-danger .modal-body {
  background-color: #ee3158 !important; }
.theme-info .modal-warning .modal-footer, .theme-info .modal-warning .modal-header {
  border-color: #ffa800; }
.theme-info .modal-warning .modal-body {
  background-color: #ffa800 !important; }

/*---border---*/
.theme-info .border-primary {
  border-color: #00D0FF !important; }
.theme-info .border-info {
  border-color: #3246D3 !important; }
.theme-info .border-success {
  border-color: #1dbfc1 !important; }
.theme-info .border-danger {
  border-color: #ee3158 !important; }
.theme-info .border-warning {
  border-color: #ffa800 !important; }

/*---Background---*/
.theme-info .bg-primary {
  background-color: #00D0FF !important;
  color: #ffffff; }
.theme-info .bg-primary-light {
  background-color: #e1f9ff !important;
  color: #00D0FF; }
.theme-info .bg-info {
  background-color: #3246D3 !important;
  color: #ffffff; }
.theme-info .bg-info-light {
  background-color: #dbdfff !important;
  color: #3246D3; }
.theme-info .bg-success {
  background-color: #1dbfc1 !important;
  color: #ffffff; }
.theme-info .bg-success-light {
  background-color: #e8f9f9 !important;
  color: #1dbfc1; }
.theme-info .bg-danger {
  background-color: #ee3158 !important;
  color: #ffffff; }
.theme-info .bg-danger-light {
  background-color: #ffd6de !important;
  color: #ee3158; }
.theme-info .bg-warning {
  background-color: #ffa800 !important;
  color: #ffffff; }
.theme-info .bg-warning-light {
  background-color: #fff8ea !important;
  color: #ffa800; }

/*---text---*/
.theme-info .text-primary {
  color: #00D0FF !important; }
.theme-info a.text-primary:hover, .theme-info a.text-primary:focus {
  color: #00D0FF !important; }
.theme-info .hover-primary:hover, .theme-info .hover-primary:focus {
  color: #00D0FF !important; }
.theme-info .text-info {
  color: #3246D3 !important; }
.theme-info a.text-info:hover, .theme-info a.text-info:focus {
  color: #3246D3 !important; }
.theme-info .hover-info:hover, .theme-info .hover-info:focus {
  color: #3246D3 !important; }
.theme-info .text-success {
  color: #1dbfc1 !important; }
.theme-info a.text-success:hover, .theme-info a.text-success:focus {
  color: #1dbfc1 !important; }
.theme-info .hover-success:hover, .theme-info .hover-success:focus {
  color: #1dbfc1 !important; }
.theme-info .text-danger {
  color: #ee3158 !important; }
.theme-info a.text-danger:hover, .theme-info a.text-danger:focus {
  color: #ee3158 !important; }
.theme-info .hover-danger:hover, .theme-info .hover-danger:focus {
  color: #ee3158 !important; }
.theme-info .text-warning {
  color: #ffa800 !important; }
.theme-info a.text-warning:hover, .theme-info a.text-warning:focus {
  color: #ffa800 !important; }
.theme-info .hover-warning:hover, .theme-info .hover-warning:focus {
  color: #ffa800 !important; }

/*---active background---*/
.theme-info .active.active-primary {
  background-color: #00a6cc !important; }
.theme-info .active.active-info {
  background-color: #2536ad !important; }
.theme-info .active.active-success {
  background-color: #169395 !important; }
.theme-info .active.active-danger {
  background-color: #da123b !important; }
.theme-info .active.active-warning {
  background-color: #cc8600 !important; }

/*---label background---*/
.theme-info .label-primary {
  background-color: #00D0FF !important; }
.theme-info .label-info {
  background-color: #3246D3 !important; }
.theme-info .label-success {
  background-color: #1dbfc1 !important; }
.theme-info .label-danger {
  background-color: #ee3158 !important; }
.theme-info .label-warning {
  background-color: #ffa800 !important; }

/*---ribbon---*/
.theme-info .ribbon-box .ribbon-primary {
  background-color: #00D0FF; }
  .theme-info .ribbon-box .ribbon-primary:before {
    border-color: #00D0FF transparent transparent; }
.theme-info .ribbon-box .ribbon-two-primary span {
  background-color: #00D0FF; }
  .theme-info .ribbon-box .ribbon-two-primary span:before {
    border-left: 3px solid #00a6cc;
    border-top: 3px solid #00a6cc; }
  .theme-info .ribbon-box .ribbon-two-primary span:after {
    border-right: 3px solid #00a6cc;
    border-top: 3px solid #00a6cc; }
.theme-info .ribbon-box .ribbon-info {
  background-color: #3246D3; }
  .theme-info .ribbon-box .ribbon-info:before {
    border-color: #3246D3 transparent transparent; }
.theme-info .ribbon-box .ribbon-two-info span {
  background-color: #3246D3; }
  .theme-info .ribbon-box .ribbon-two-info span:before {
    border-left: 3px solid #2536ad;
    border-top: 3px solid #2536ad; }
  .theme-info .ribbon-box .ribbon-two-info span:after {
    border-right: 3px solid #2536ad;
    border-top: 3px solid #2536ad; }
.theme-info .ribbon-box .ribbon-success {
  background-color: #1dbfc1; }
  .theme-info .ribbon-box .ribbon-success:before {
    border-color: #1dbfc1 transparent transparent; }
.theme-info .ribbon-box .ribbon-two-success span {
  background-color: #1dbfc1; }
  .theme-info .ribbon-box .ribbon-two-success span:before {
    border-left: 3px solid #169395;
    border-top: 3px solid #169395; }
  .theme-info .ribbon-box .ribbon-two-success span:after {
    border-right: 3px solid #169395;
    border-top: 3px solid #169395; }
.theme-info .ribbon-box .ribbon-danger {
  background-color: #ee3158; }
  .theme-info .ribbon-box .ribbon-danger:before {
    border-color: #ee3158 transparent transparent; }
.theme-info .ribbon-box .ribbon-two-danger span {
  background-color: #ee3158; }
  .theme-info .ribbon-box .ribbon-two-danger span:before {
    border-left: 3px solid #da123b;
    border-top: 3px solid #da123b; }
  .theme-info .ribbon-box .ribbon-two-danger span:after {
    border-right: 3px solid #da123b;
    border-top: 3px solid #da123b; }
.theme-info .ribbon-box .ribbon-warning {
  background-color: #ffa800; }
  .theme-info .ribbon-box .ribbon-warning:before {
    border-color: #ffa800 transparent transparent; }
.theme-info .ribbon-box .ribbon-two-warning span {
  background-color: #ffa800; }
  .theme-info .ribbon-box .ribbon-two-warning span:before {
    border-left: 3px solid #cc8600;
    border-top: 3px solid #cc8600; }
  .theme-info .ribbon-box .ribbon-two-warning span:after {
    border-right: 3px solid #cc8600;
    border-top: 3px solid #cc8600; }

/*---Box---*/
.theme-info .box-primary {
  background-color: #00D0FF !important; }
  .theme-info .box-primary.box-bordered {
    border-color: #00D0FF; }
.theme-info .box-outline-primary {
  background-color: #ffffff;
  border: 1px solid #00D0FF; }
.theme-info .box.box-solid.box-primary > .box-header {
  color: #ffffff;
  background-color: #00D0FF; }
  .theme-info .box.box-solid.box-primary > .box-header .btn {
    color: #ffffff; }
  .theme-info .box.box-solid.box-primary > .box-header > a {
    color: #ffffff; }
.theme-info .box-info {
  background-color: #3246D3 !important; }
  .theme-info .box-info.box-bordered {
    border-color: #3246D3; }
.theme-info .box-outline-info {
  background-color: #ffffff;
  border: 1px solid #3246D3; }
.theme-info .box.box-solid.box-info > .box-header {
  color: #ffffff;
  background-color: #3246D3; }
  .theme-info .box.box-solid.box-info > .box-header .btn {
    color: #ffffff; }
  .theme-info .box.box-solid.box-info > .box-header > a {
    color: #ffffff; }
.theme-info .box-success {
  background-color: #1dbfc1 !important; }
  .theme-info .box-success.box-bordered {
    border-color: #1dbfc1; }
.theme-info .box-outline-success {
  background-color: #ffffff;
  border: 1px solid #1dbfc1; }
.theme-info .box.box-solid.box-success > .box-header {
  color: #ffffff;
  background-color: #1dbfc1; }
  .theme-info .box.box-solid.box-success > .box-header .btn {
    color: #ffffff; }
  .theme-info .box.box-solid.box-success > .box-header > a {
    color: #ffffff; }
.theme-info .box-danger {
  background-color: #ee3158 !important; }
  .theme-info .box-danger.box-bordered {
    border-color: #ee3158; }
.theme-info .box-outline-danger {
  background-color: #ffffff;
  border: 1px solid #ee3158; }
.theme-info .box.box-solid.box-danger > .box-header {
  color: #ffffff;
  background-color: #ee3158; }
  .theme-info .box.box-solid.box-danger > .box-header .btn {
    color: #ffffff; }
  .theme-info .box.box-solid.box-danger > .box-header > a {
    color: #ffffff; }
.theme-info .box-warning {
  background-color: #ffa800 !important; }
  .theme-info .box-warning.box-bordered {
    border-color: #ffa800; }
.theme-info .box-outline-warning {
  background-color: #ffffff;
  border: 1px solid #ffa800; }
.theme-info .box.box-solid.box-warning > .box-header {
  color: #ffffff;
  background-color: #ffa800; }
  .theme-info .box.box-solid.box-warning > .box-header .btn {
    color: #ffffff; }
  .theme-info .box.box-solid.box-warning > .box-header > a {
    color: #ffffff; }
.theme-info .box-profile .social-states a:hover {
  color: #00a6cc; }
.theme-info .box-controls li > a:hover {
  color: #00a6cc; }
.theme-info .box-controls .dropdown.show > a {
  color: #00a6cc; }
.theme-info .box-fullscreen .box-btn-fullscreen {
  color: #00a6cc; }

/*---progress bar---*/
.theme-info .progress-bar-primary {
  background-color: #00D0FF; }
.theme-info .progress-bar-info {
  background-color: #3246D3; }
.theme-info .progress-bar-success {
  background-color: #1dbfc1; }
.theme-info .progress-bar-danger {
  background-color: #ee3158; }
.theme-info .progress-bar-warning {
  background-color: #ffa800; }

/*---panel---*/
.theme-info .panel-primary {
  border-color: #00D0FF; }
  .theme-info .panel-primary > .panel-heading {
    color: #ffffff;
    background-color: #00D0FF;
    border-color: #00D0FF; }
    .theme-info .panel-primary > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #00D0FF; }
    .theme-info .panel-primary > .panel-heading .badge-pill {
      color: #00D0FF;
      background-color: #ffffff; }
  .theme-info .panel-primary .panel-title, .theme-info .panel-primary .panel-action {
    color: #ffffff; }
  .theme-info .panel-primary .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #00D0FF; }
.theme-info .panel-line.panel-primary .panel-heading {
  color: #00D0FF;
  border-top-color: #00D0FF;
  background: transparent; }
.theme-info .panel-line.panel-primary .panel-title, .theme-info .panel-line.panel-primary .panel-action {
  color: #00D0FF; }
.theme-info .panel-info {
  border-color: #3246D3; }
  .theme-info .panel-info > .panel-heading {
    color: #ffffff;
    background-color: #3246D3;
    border-color: #3246D3; }
    .theme-info .panel-info > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #3246D3; }
    .theme-info .panel-info > .panel-heading .badge-pill {
      color: #3246D3;
      background-color: #ffffff; }
  .theme-info .panel-info .panel-title, .theme-info .panel-info .panel-action {
    color: #ffffff; }
  .theme-info .panel-info .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #3246D3; }
.theme-info .panel-line.panel-info .panel-heading {
  color: #3246D3;
  border-top-color: #3246D3;
  background: transparent; }
.theme-info .panel-line.panel-info .panel-title, .theme-info .panel-line.panel-info .panel-action {
  color: #3246D3; }
.theme-info .panel-success {
  border-color: #1dbfc1; }
  .theme-info .panel-success > .panel-heading {
    color: #ffffff;
    background-color: #1dbfc1;
    border-color: #1dbfc1; }
    .theme-info .panel-success > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #1dbfc1; }
    .theme-info .panel-success > .panel-heading .badge-pill {
      color: #1dbfc1;
      background-color: #ffffff; }
  .theme-info .panel-success .panel-title, .theme-info .panel-success .panel-action {
    color: #ffffff; }
  .theme-info .panel-success .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #1dbfc1; }
.theme-info .panel-line.panel-success .panel-heading {
  color: #1dbfc1;
  border-top-color: #1dbfc1;
  background: transparent; }
.theme-info .panel-line.panel-success .panel-title, .theme-info .panel-line.panel-success .panel-action {
  color: #1dbfc1; }
.theme-info .panel-danger {
  border-color: #ee3158; }
  .theme-info .panel-danger > .panel-heading {
    color: #ffffff;
    background-color: #ee3158;
    border-color: #ee3158; }
    .theme-info .panel-danger > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #ee3158; }
    .theme-info .panel-danger > .panel-heading .badge-pill {
      color: #ee3158;
      background-color: #ffffff; }
  .theme-info .panel-danger .panel-title, .theme-info .panel-danger .panel-action {
    color: #ffffff; }
  .theme-info .panel-danger .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #ee3158; }
.theme-info .panel-line.panel-danger .panel-heading {
  color: #ee3158;
  border-top-color: #ee3158;
  background: transparent; }
.theme-info .panel-line.panel-danger .panel-title, .theme-info .panel-line.panel-danger .panel-action {
  color: #ee3158; }
.theme-info .panel-warning {
  border-color: #ffa800; }
  .theme-info .panel-warning > .panel-heading {
    color: #ffffff;
    background-color: #ffa800;
    border-color: #ffa800; }
    .theme-info .panel-warning > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #ffa800; }
    .theme-info .panel-warning > .panel-heading .badge-pill {
      color: #ffa800;
      background-color: #ffffff; }
  .theme-info .panel-warning .panel-title, .theme-info .panel-warning .panel-action {
    color: #ffffff; }
  .theme-info .panel-warning .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #ffa800; }
.theme-info .panel-line.panel-warning .panel-heading {
  color: #ffa800;
  border-top-color: #ffa800;
  background: transparent; }
.theme-info .panel-line.panel-warning .panel-title, .theme-info .panel-line.panel-warning .panel-action {
  color: #ffa800; }

/*---switch---*/
.theme-info .switch input:checked ~ .switch-indicator::after {
  background-color: #00D0FF; }
.theme-info .switch.switch-primary input:checked ~ .switch-indicator::after {
  background-color: #00D0FF; }
.theme-info .switch.switch-info input:checked ~ .switch-indicator::after {
  background-color: #3246D3; }
.theme-info .switch.switch-success input:checked ~ .switch-indicator::after {
  background-color: #1dbfc1; }
.theme-info .switch.switch-danger input:checked ~ .switch-indicator::after {
  background-color: #ee3158; }
.theme-info .switch.switch-warning input:checked ~ .switch-indicator::after {
  background-color: #ffa800; }

/*---badge---*/
.theme-info .badge-primary {
  background-color: #00D0FF;
  color: #ffffff; }
.theme-info .badge-primary[href]:hover, .theme-info .badge-primary[href]:focus {
  background-color: #00a6cc; }
.theme-info .badge-secondary {
  background-color: #e4e6ef;
  color: #172b4c; }
.theme-info .badge-secondary[href]:hover, .theme-info .badge-secondary[href]:focus {
  background-color: #c4c8dc; }
.theme-info .badge-info {
  background-color: #3246D3;
  color: #ffffff; }
.theme-info .badge-info[href]:hover, .theme-info .badge-info[href]:focus {
  background-color: #2536ad; }
.theme-info .badge-success {
  background-color: #1dbfc1;
  color: #ffffff; }
.theme-info .badge-success[href]:hover, .theme-info .badge-success[href]:focus {
  background-color: #169395; }
.theme-info .badge-danger {
  background-color: #ee3158;
  color: #ffffff; }
.theme-info .badge-danger[href]:hover, .theme-info .badge-danger[href]:focus {
  background-color: #da123b; }
.theme-info .badge-warning {
  background-color: #ffa800;
  color: #ffffff; }
.theme-info .badge-warning[href]:hover, .theme-info .badge-warning[href]:focus {
  background-color: #cc8600; }

/*---badge light---*/
.theme-info .badge-primary-light {
  background-color: #e1f9ff;
  color: #00D0FF; }
.theme-info .badge-primary-light[href]:hover, .theme-info .badge-primary-light[href]:focus {
  background-color: #aeefff; }
.theme-info .badge-secondary-light {
  background-color: #e9edf2;
  color: #172b4c; }
.theme-info .badge-secondary-light[href]:hover, .theme-info .badge-secondary-light[href]:focus {
  background-color: #c9d3df; }
.theme-info .badge-info-light {
  background-color: #dbdfff;
  color: #3246D3; }
.theme-info .badge-info-light[href]:hover, .theme-info .badge-info-light[href]:focus {
  background-color: #a8b2ff; }
.theme-info .badge-success-light {
  background-color: #e8f9f9;
  color: #1dbfc1; }
.theme-info .badge-success-light[href]:hover, .theme-info .badge-success-light[href]:focus {
  background-color: #c0eeee; }
.theme-info .badge-danger-light {
  background-color: #ffd6de;
  color: #ee3158; }
.theme-info .badge-danger-light[href]:hover, .theme-info .badge-danger-light[href]:focus {
  background-color: #ffa3b5; }
.theme-info .badge-warning-light {
  background-color: #fff8ea;
  color: #ffa800; }
.theme-info .badge-warning-light[href]:hover, .theme-info .badge-warning-light[href]:focus {
  background-color: #ffe7b7; }

/*---rating---*/
.theme-info .rating-primary .active {
  color: #00D0FF; }
.theme-info .rating-primary :checked ~ label {
  color: #00D0FF; }
.theme-info .rating-primary label:hover {
  color: #00D0FF; }
  .theme-info .rating-primary label:hover ~ label {
    color: #00D0FF; }
.theme-info .rating-info .active {
  color: #3246D3; }
.theme-info .rating-info :checked ~ label {
  color: #3246D3; }
.theme-info .rating-info label:hover {
  color: #3246D3; }
  .theme-info .rating-info label:hover ~ label {
    color: #3246D3; }
.theme-info .rating-success .active {
  color: #1dbfc1; }
.theme-info .rating-success :checked ~ label {
  color: #1dbfc1; }
.theme-info .rating-success label:hover {
  color: #1dbfc1; }
  .theme-info .rating-success label:hover ~ label {
    color: #1dbfc1; }
.theme-info .rating-danger .active {
  color: #ee3158; }
.theme-info .rating-danger :checked ~ label {
  color: #ee3158; }
.theme-info .rating-danger label:hover {
  color: #ee3158; }
  .theme-info .rating-danger label:hover ~ label {
    color: #ee3158; }
.theme-info .rating-warning .active {
  color: #ffa800; }
.theme-info .rating-warning :checked ~ label {
  color: #ffa800; }
.theme-info .rating-warning label:hover {
  color: #ffa800; }
  .theme-info .rating-warning label:hover ~ label {
    color: #ffa800; }

/*---toggler---*/
.theme-info .toggler-primary input:checked + i {
  color: #00D0FF; }
.theme-info .toggler-info input:checked + i {
  color: #3246D3; }
.theme-info .toggler-success input:checked + i {
  color: #1dbfc1; }
.theme-info .toggler-danger input:checked + i {
  color: #ee3158; }
.theme-info .toggler-warning input:checked + i {
  color: #ffa800; }

/*---nav tabs---*/
.theme-info .nav-tabs.nav-tabs-primary .nav-link:hover, .theme-info .nav-tabs.nav-tabs-primary .nav-link:active, .theme-info .nav-tabs.nav-tabs-primary .nav-link:focus, .theme-info .nav-tabs.nav-tabs-primary .nav-link.active {
  border-color: #00a6cc;
  background-color: transparent;
  color: #00a6cc; }
.theme-info .nav-tabs.nav-tabs-info .nav-link:hover, .theme-info .nav-tabs.nav-tabs-info .nav-link:active, .theme-info .nav-tabs.nav-tabs-info .nav-link:focus, .theme-info .nav-tabs.nav-tabs-info .nav-link.active {
  border-color: #2536ad;
  background-color: #3246D3;
  color: #ffffff; }
.theme-info .nav-tabs.nav-tabs-success .nav-link:hover, .theme-info .nav-tabs.nav-tabs-success .nav-link:active, .theme-info .nav-tabs.nav-tabs-success .nav-link:focus, .theme-info .nav-tabs.nav-tabs-success .nav-link.active {
  border-color: #169395;
  background-color: transparent;
  color: #169395; }
.theme-info .nav-tabs.nav-tabs-danger .nav-link:hover, .theme-info .nav-tabs.nav-tabs-danger .nav-link:active, .theme-info .nav-tabs.nav-tabs-danger .nav-link:focus, .theme-info .nav-tabs.nav-tabs-danger .nav-link.active {
  border-color: #da123b;
  background-color: transparent;
  color: #da123b; }
.theme-info .nav-tabs.nav-tabs-warning .nav-link:hover, .theme-info .nav-tabs.nav-tabs-warning .nav-link:active, .theme-info .nav-tabs.nav-tabs-warning .nav-link:focus, .theme-info .nav-tabs.nav-tabs-warning .nav-link.active {
  border-color: #cc8600;
  background-color: transparent;
  color: #cc8600; }
.theme-info .nav-tabs-custom.tab-primary > .nav-tabs > li a.active {
  border-top-color: #00a6cc; }
.theme-info .nav-tabs-custom.tab-info > .nav-tabs > li a.active {
  border-top-color: #2536ad; }
.theme-info .nav-tabs-custom.tab-success > .nav-tabs > li a.active {
  border-top-color: #169395; }
.theme-info .nav-tabs-custom.tab-danger > .nav-tabs > li a.active {
  border-top-color: #da123b; }
.theme-info .nav-tabs-custom.tab-warning > .nav-tabs > li a.active {
  border-top-color: #cc8600; }
.theme-info .nav-tabs .nav-link.active {
  border-bottom-color: #00D0FF;
  background-color: #00D0FF;
  color: #ffffff; }
  .theme-info .nav-tabs .nav-link.active:hover, .theme-info .nav-tabs .nav-link.active:focus {
    border-bottom-color: #00D0FF;
    background-color: #00D0FF;
    color: #ffffff; }
.theme-info .nav-tabs .nav-item.open .nav-link {
  border-bottom-color: #00D0FF;
  background-color: #00D0FF; }
  .theme-info .nav-tabs .nav-item.open .nav-link:hover, .theme-info .nav-tabs .nav-item.open .nav-link:focus {
    border-bottom-color: #00D0FF;
    background-color: #00D0FF; }

/*---todo---*/
.theme-info .todo-list .primary {
  border-left-color: #00D0FF; }
.theme-info .todo-list .info {
  border-left-color: #00D0FF; }
.theme-info .todo-list .success {
  border-left-color: #1dbfc1; }
.theme-info .todo-list .danger {
  border-left-color: #ee3158; }
.theme-info .todo-list .warning {
  border-left-color: #ffa800; }

/*---timeline---*/
.theme-info .timeline .timeline-item > .timeline-event.timeline-event-primary {
  background-color: #00D0FF;
  border: 1px solid #00D0FF;
  color: #ffffff; }
  .theme-info .timeline .timeline-item > .timeline-event.timeline-event-primary:before, .theme-info .timeline .timeline-item > .timeline-event.timeline-event-primary:after {
    border-left-color: #00D0FF;
    border-right-color: #00D0FF; }
  .theme-info .timeline .timeline-item > .timeline-event.timeline-event-primary * {
    color: inherit; }
.theme-info .timeline .timeline-item > .timeline-event.timeline-event-info {
  background-color: #3246D3;
  border: 1px solid #3246D3;
  color: #ffffff; }
  .theme-info .timeline .timeline-item > .timeline-event.timeline-event-info:before, .theme-info .timeline .timeline-item > .timeline-event.timeline-event-info:after {
    border-left-color: #3246D3;
    border-right-color: #3246D3; }
  .theme-info .timeline .timeline-item > .timeline-event.timeline-event-info * {
    color: inherit; }
.theme-info .timeline .timeline-item > .timeline-event.timeline-event-success {
  background-color: #1dbfc1;
  border: 1px solid #1dbfc1;
  color: #ffffff; }
  .theme-info .timeline .timeline-item > .timeline-event.timeline-event-success:before, .theme-info .timeline .timeline-item > .timeline-event.timeline-event-success:after {
    border-left-color: #1dbfc1;
    border-right-color: #1dbfc1; }
  .theme-info .timeline .timeline-item > .timeline-event.timeline-event-success * {
    color: inherit; }
.theme-info .timeline .timeline-item > .timeline-event.timeline-event-danger {
  background-color: #ee3158;
  border: 1px solid #ee3158;
  color: #ffffff; }
  .theme-info .timeline .timeline-item > .timeline-event.timeline-event-danger:before, .theme-info .timeline .timeline-item > .timeline-event.timeline-event-danger:after {
    border-left-color: #ee3158;
    border-right-color: #ee3158; }
  .theme-info .timeline .timeline-item > .timeline-event.timeline-event-danger * {
    color: inherit; }
.theme-info .timeline .timeline-item > .timeline-event.timeline-event-warning {
  background-color: #ffa800;
  border: 1px solid #ffa800;
  color: #ffffff; }
  .theme-info .timeline .timeline-item > .timeline-event.timeline-event-warning:before, .theme-info .timeline .timeline-item > .timeline-event.timeline-event-warning:after {
    border-left-color: #ffa800;
    border-right-color: #ffa800; }
  .theme-info .timeline .timeline-item > .timeline-event.timeline-event-warning * {
    color: inherit; }
.theme-info .timeline .timeline-item > .timeline-point.timeline-point-primary {
  color: #00D0FF;
  background-color: #ffffff; }
.theme-info .timeline .timeline-item > .timeline-point.timeline-point-info {
  color: #3246D3;
  background-color: #ffffff; }
.theme-info .timeline .timeline-item > .timeline-point.timeline-point-success {
  color: #1dbfc1;
  background-color: #ffffff; }
.theme-info .timeline .timeline-item > .timeline-point.timeline-point-danger {
  color: #ee3158;
  background-color: #ffffff; }
.theme-info .timeline .timeline-item > .timeline-point.timeline-point-warning {
  color: #ffa800;
  background-color: #ffffff; }
.theme-info .timeline .timeline-label .label-primary {
  background-color: #00D0FF; }
.theme-info .timeline .timeline-label .label-info {
  background-color: #3246D3; }
.theme-info .timeline .timeline-label .label-success {
  background-color: #1dbfc1; }
.theme-info .timeline .timeline-label .label-danger {
  background-color: #ee3158; }
.theme-info .timeline .timeline-label .label-warning {
  background-color: #ffa800; }
.theme-info .timeline__year, .theme-info .timeline5:before, .theme-info .timeline__box:before, .theme-info .timeline__date {
  background-color: #00D0FF; }
.theme-info .timeline__post {
  border-left: 3px solid #00D0FF; }

/*---daterangepicker---*/
.theme-info .daterangepicker td.active {
  background-color: #00D0FF; }
  .theme-info .daterangepicker td.active:hover {
    background-color: #00D0FF; }
.theme-info .daterangepicker .input-mini.active {
  border: 1px solid #00D0FF; }
.theme-info .ranges li:hover, .theme-info .ranges li:active, .theme-info .ranges li.active {
  border: 1px solid #00D0FF;
  background-color: #00D0FF; }

/*---control-sidebar---*/
.theme-info .control-sidebar .nav-tabs.control-sidebar-tabs > li > a:hover, .theme-info .control-sidebar .nav-tabs.control-sidebar-tabs > li > a:active, .theme-info .control-sidebar .nav-tabs.control-sidebar-tabs > li > a:focus {
  border-color: #00D0FF;
  color: #00D0FF; }
.theme-info .control-sidebar .nav-tabs.control-sidebar-tabs > li > a.active {
  border-color: #00D0FF;
  color: #00D0FF; }
  .theme-info .control-sidebar .nav-tabs.control-sidebar-tabs > li > a.active:hover, .theme-info .control-sidebar .nav-tabs.control-sidebar-tabs > li > a.active:active, .theme-info .control-sidebar .nav-tabs.control-sidebar-tabs > li > a.active:focus {
    border-color: #00D0FF;
    color: #00D0FF; }
.theme-info .control-sidebar .rpanel-title .btn:hover {
  color: #00D0FF; }

/*---nav---*/
.theme-info .nav > li > a:hover, .theme-info .nav > li > a:active, .theme-info .nav > li > a:focus {
  color: #00D0FF; }
.theme-info .nav-pills > li > a.active {
  border-top-color: #00D0FF;
  background-color: #00D0FF !important;
  color: #ffffff; }
  .theme-info .nav-pills > li > a.active:hover, .theme-info .nav-pills > li > a.active:focus {
    border-top-color: #00D0FF;
    background-color: #00D0FF !important;
    color: #ffffff; }
.theme-info .mailbox-nav .nav-pills > li > a:hover, .theme-info .mailbox-nav .nav-pills > li > a:focus {
  border-color: #00D0FF; }
.theme-info .mailbox-nav .nav-pills > li > a.active {
  border-color: #00D0FF; }
  .theme-info .mailbox-nav .nav-pills > li > a.active:hover, .theme-info .mailbox-nav .nav-pills > li > a.active:focus {
    border-color: #00D0FF; }
.theme-info .nav-tabs-custom > .nav-tabs > li a.active {
  border-top-color: #00D0FF; }
.theme-info .profile-tab li a.nav-link.active {
  border-bottom: 2px solid #00D0FF; }
.theme-info .customtab li a.nav-link.active {
  border-bottom: 2px solid #00D0FF; }

/*---form-element---*/
.theme-info .form-element .input-group .input-group-addon {
  background-image: linear-gradient(45deg, #00D0FF, #3246D3), linear-gradient(#3b6dc1, #3b6dc1); }
.theme-info .form-element .form-control {
  background-image: linear-gradient(45deg, #00D0FF, #3246D3), linear-gradient(#3b6dc1, #3b6dc1); }
  .theme-info .form-element .form-control:focus {
    background-image: linear-gradient(45deg, #00D0FF, #3246D3), linear-gradient(#3b6dc1, #3b6dc1); }
.theme-info .form-control:focus {
  border-color: #00D0FF; }
.theme-info [type=checkbox]:checked.chk-col-primary + label:before {
  border-right: 2px solid #00D0FF;
  border-bottom: 2px solid #00D0FF; }
.theme-info [type=checkbox]:checked.chk-col-info + label:before {
  border-right: 2px solid #3246D3;
  border-bottom: 2px solid #3246D3; }
.theme-info [type=checkbox]:checked.chk-col-success + label:before {
  border-right: 2px solid #1dbfc1;
  border-bottom: 2px solid #1dbfc1; }
.theme-info [type=checkbox]:checked.chk-col-danger + label:before {
  border-right: 2px solid #ee3158;
  border-bottom: 2px solid #ee3158; }
.theme-info [type=checkbox]:checked.chk-col-warning + label:before {
  border-right: 2px solid #ffa800;
  border-bottom: 2px solid #ffa800; }
.theme-info [type=checkbox].filled-in:checked.chk-col-primary + label:after {
  border: 2px solid #00D0FF;
  background-color: #00D0FF; }
.theme-info [type=checkbox].filled-in:checked.chk-col-info + label:after {
  border: 2px solid #3246D3;
  background-color: #3246D3; }
.theme-info [type=checkbox].filled-in:checked.chk-col-success + label:after {
  border: 2px solid #1dbfc1;
  background-color: #1dbfc1; }
.theme-info [type=checkbox].filled-in:checked.chk-col-danger + label:after {
  border: 2px solid #ee3158;
  background-color: #ee3158; }
.theme-info [type=checkbox].filled-in:checked.chk-col-warning + label:after {
  border: 2px solid #ffa800;
  background-color: #ffa800; }
.theme-info [type=radio].radio-col-primary:checked + label:after {
  background-color: #00D0FF;
  border-color: #00D0FF;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-info [type=radio].with-gap.radio-col-primary:checked + label:before {
  border: 2px solid #00D0FF;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-info [type=radio].with-gap.radio-col-primary:checked + label:after {
  background-color: #00D0FF;
  border: 2px solid #00D0FF;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-info [type=radio].radio-col-info:checked + label:after {
  background-color: #3246D3;
  border-color: #3246D3;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-info [type=radio].with-gap.radio-col-info:checked + label:before {
  border: 2px solid #3246D3;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-info [type=radio].with-gap.radio-col-info:checked + label:after {
  background-color: #3246D3;
  border: 2px solid #3246D3;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-info [type=radio].radio-col-success:checked + label:after {
  background-color: #1dbfc1;
  border-color: #1dbfc1;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-info [type=radio].with-gap.radio-col-success:checked + label:before {
  border: 2px solid #1dbfc1;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-info [type=radio].with-gap.radio-col-success:checked + label:after {
  background-color: #1dbfc1;
  border: 2px solid #1dbfc1;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-info [type=radio].radio-col-danger:checked + label:after {
  background-color: #ee3158;
  border-color: #ee3158;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-info [type=radio].with-gap.radio-col-danger:checked + label:before {
  border: 2px solid #ee3158;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-info [type=radio].with-gap.radio-col-danger:checked + label:after {
  background-color: #ee3158;
  border: 2px solid #ee3158;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-info [type=radio].radio-col-warning:checked + label:after {
  background-color: #ffa800;
  border-color: #ffa800;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-info [type=radio].with-gap.radio-col-warning:checked + label:before {
  border: 2px solid #ffa800;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-info [type=radio].with-gap.radio-col-warning:checked + label:after {
  background-color: #ffa800;
  border: 2px solid #ffa800;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-info [type=checkbox]:checked + label:before {
  border-right: 2px solid #00D0FF;
  border-bottom: 2px solid #00D0FF; }
.theme-info [type=checkbox].filled-in:checked + label:after {
  border: 2px solid #00D0FF;
  background-color: #00D0FF; }
.theme-info [type=radio].with-gap:checked + label:before, .theme-info [type=radio].with-gap:checked + label:after {
  border: 2px solid #00D0FF; }
.theme-info [type=radio].with-gap:checked + label:after {
  background-color: #00D0FF;
  z-index: 0; }
.theme-info [type=radio]:checked + label:after {
  border: 2px solid #00D0FF;
  background-color: #00D0FF;
  z-index: 0; }
.theme-info [type=checkbox].filled-in.tabbed:checked:focus + label:after {
  border-color: #00D0FF;
  background-color: #00D0FF; }

/*---Calender---*/
.theme-info .fx-element-overlay .fx-card-item .fx-card-content a:hover {
  color: #00D0FF; }
.theme-info .fx-element-overlay .fx-card-item .fx-overlay-1 .fx-info > li a:hover {
  background: #00D0FF;
  border-color: #00D0FF; }
.theme-info .fc-event, .theme-info .calendar-event {
  background: #00D0FF; }

/*---Tabs---*/
.theme-info .tabs-vertical li .nav-link:hover, .theme-info .tabs-vertical li .nav-link:active, .theme-info .tabs-vertical li .nav-link:focus, .theme-info .tabs-vertical li .nav-link.active {
  background-color: #00D0FF;
  color: #ffffff; }
.theme-info .customvtab .tabs-vertical li .nav-link:hover, .theme-info .customvtab .tabs-vertical li .nav-link:active, .theme-info .customvtab .tabs-vertical li .nav-link:focus, .theme-info .customvtab .tabs-vertical li .nav-link.active {
  border-right: 2px solid #00D0FF;
  color: #00D0FF; }
.theme-info .customtab2 li a.nav-link:hover, .theme-info .customtab2 li a.nav-link:active, .theme-info .customtab2 li a.nav-link.active {
  background-color: #00D0FF; }

/*---Notification---*/
.theme-info .jq-icon-primary {
  background-color: #00D0FF;
  color: #ffffff;
  border-color: #00D0FF; }
.theme-info .jq-icon-info {
  background-color: #3246D3;
  color: #ffffff;
  border-color: #3246D3; }
.theme-info .jq-icon-success {
  background-color: #1dbfc1;
  color: #ffffff;
  border-color: #00D0FF; }
.theme-info .jq-icon-error {
  background-color: #ee3158;
  color: #ffffff;
  border-color: #ee3158; }
.theme-info .jq-icon-danger {
  background-color: #ee3158;
  color: #ffffff;
  border-color: #ee3158; }
.theme-info .jq-icon-warning {
  background-color: #ffa800;
  color: #ffffff;
  border-color: #ffa800; }

/*---avatar---*/
.theme-info .avatar.status-primary::after {
  background-color: #00D0FF; }
.theme-info .avatar.status-info::after {
  background-color: #3246D3; }
.theme-info .avatar.status-success::after {
  background-color: #1dbfc1; }
.theme-info .avatar.status-danger::after {
  background-color: #ee3158; }
.theme-info .avatar.status-warning::after {
  background-color: #ffa800; }
.theme-info .avatar[class*='status-']::after {
  background-color: #00D0FF; }
.theme-info .avatar-add:hover {
  background-color: #00a6cc;
  border-color: #00a6cc; }

/*---media---*/
.theme-info .media-chat.media-chat-reverse .media-body p {
  background-color: #00D0FF; }
.theme-info .media-right-out a:hover {
  color: #00a6cc; }

/*---control---*/
.theme-info .control input:checked:focus ~ .control_indicator {
  background-color: #00D0FF; }
.theme-info .control input:checked ~ .control_indicator {
  background-color: #00D0FF; }
.theme-info .control:hover input:not([disabled]):checked ~ .control_indicator {
  background-color: #00D0FF; }

/*---flex---*/
.theme-info .flex-column > li > a.nav-link.active {
  border-left-color: #00D0FF; }
  .theme-info .flex-column > li > a.nav-link.active:hover {
    border-left-color: #00D0FF; }

/*---pagination---*/
.theme-info .pagination li a.current {
  border: 1px solid #00D0FF;
  background-color: #00D0FF; }
  .theme-info .pagination li a.current:hover {
    border: 1px solid #00D0FF;
    background-color: #00D0FF; }
.theme-info .pagination li a:hover {
  border: 1px solid #00a6cc;
  background-color: #00a6cc !important; }
.theme-info .dataTables_wrapper .dataTables_paginate .paginate_button.current {
  border: 1px solid #00D0FF;
  background-color: #00D0FF; }
  .theme-info .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    border: 1px solid #00D0FF;
    background-color: #00D0FF; }
.theme-info .paging_simple_numbers .pagination .paginate_button.active a {
  background-color: #00D0FF; }
.theme-info .paging_simple_numbers .pagination .paginate_button:hover a {
  background-color: #00D0FF; }
.theme-info .footable .pagination li a:hover, .theme-info .footable .pagination li a:active, .theme-info .footable .pagination li a.active {
  background-color: #00D0FF; }

/*---dataTables---*/
.theme-info .dt-buttons .dt-button {
  background-color: #00D0FF; }

/*---select2---*/
.theme-info .select2-container--default.select2-container--open {
  border-color: #00D0FF; }
.theme-info .select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: #00D0FF; }
.theme-info .select2-container--default .select2-search--dropdown .select2-search__field {
  border-color: #00D0FF !important; }
.theme-info .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #00D0FF !important; }
.theme-info .select2-container--default .select2-selection--multiple:focus {
  border-color: #00D0FF !important; }
.theme-info .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #00D0FF;
  border-color: #00D0FF; }

/*---Other---*/
.theme-info .myadmin-dd .dd-list .dd-list .dd-handle:hover {
  color: #00a6cc; }
.theme-info .myadmin-dd-empty .dd-list .dd3-handle:hover {
  color: #00a6cc; }
.theme-info .myadmin-dd-empty .dd-list .dd3-content:hover {
  color: #00a6cc; }
.theme-info [data-overlay-primary]::before {
  background: #00a6cc; }

/*---wizard---*/
.theme-info .wizard-content .wizard > .steps > ul > li.current {
  border: 2px solid #00D0FF;
  background-color: #00D0FF; }
.theme-info .wizard-content .wizard > .steps > ul > li.done {
  border-color: #00a6cc;
  background-color: #00a6cc; }
.theme-info .wizard-content .wizard > .actions > ul > li > a {
  background-color: #00D0FF; }
.theme-info .wizard-content .wizard.wizard-circle > .steps > ul > li:after {
  background-color: #00D0FF; }
.theme-info .wizard-content .wizard.wizard-circle > .steps > ul > li:before {
  background-color: #00D0FF; }
.theme-info .wizard-content .wizard.wizard-notification > .steps > ul > li:after {
  background-color: #00D0FF; }
.theme-info .wizard-content .wizard.wizard-notification > .steps > ul > li:before {
  background-color: #00D0FF; }
.theme-info .wizard-content .wizard.wizard-notification > .steps > ul > li.current .step {
  border: 2px solid #00D0FF;
  color: #00D0FF; }
  .theme-info .wizard-content .wizard.wizard-notification > .steps > ul > li.current .step:after {
    border-top-color: #00D0FF; }
.theme-info .wizard-content .wizard.wizard-notification > .steps > ul > li.done .step:after {
  border-top-color: #00D0FF; }

@media (max-width: 767px) {
  .theme-info .wizard-content .wizard > .steps > ul > li:last-child:after {
    background-color: #00D0FF; } }
@media (max-width: 575px) {
  .theme-info .wizard-content .wizard > .steps > ul > li.current:after {
    background-color: #00D0FF; } }
/*---slider---*/
.theme-info #primary .slider-selection {
  background-color: #00D0FF; }
.theme-info #info .slider-selection {
  background-color: #3246D3; }
.theme-info #success .slider-selection {
  background-color: #1dbfc1; }
.theme-info #danger .slider-selection {
  background-color: #ee3158; }
.theme-info #warning .slider-selection {
  background-color: #ffa800; }

/*---horizontal-timeline---*/
.theme-info .cd-horizontal-timeline .events a.selected::after {
  background: #00D0FF;
  border-color: #00D0FF; }
.theme-info .cd-horizontal-timeline .events a.older-event::after {
  border-color: #00D0FF; }
.theme-info .cd-horizontal-timeline .filling-line {
  background: #00D0FF; }
.theme-info .cd-horizontal-timeline a {
  color: #00D0FF; }
  .theme-info .cd-horizontal-timeline a:hover, .theme-info .cd-horizontal-timeline a:focus {
    color: #00D0FF; }
.theme-info .cd-timeline-navigation a:hover, .theme-info .cd-timeline-navigation a:focus {
  border-color: #00D0FF; }

/**************************************
Theme Success Color
**************************************/
.bg-gradient-success, .theme-success .bg-gradient-success, .theme-success .art-bg {
  background: linear-gradient(45deg, #1dbfc1, #00D0FF); }

.bg-light-body {
  background: transparent; }

.theme-success.fixed .main-header {
  background: transparent; }
.theme-success .main-header {
  background: transparent; }

.theme-success.onlyheader .art-bg {
  background-image: none; }

.bg-gradient-success-dark, .dark-skin.theme-success .bg-gradient-success, .dark-skin.theme-success .art-bg {
  background-image: linear-gradient(45deg, #106768, #007d99); }

.bg-dark-body {
  background: #0c1a32; }

.dark-skin.theme-success.fixed .main-header {
  background: transparent; }
.dark-skin.theme-success .main-header {
  background: transparent; }

@media (max-width: 767px) {
  .theme-success.fixed .main-header {
    background-image: #e4e6ef; }
    .theme-success.fixed .main-header.navbar {
      background: none; }

  .dark-skin.theme-success.fixed .main-header {
    background-image: #0c1a32; } }
.theme-success a:hover, .theme-success a:active, .theme-success a:focus {
  color: #1dbfc1; }
.theme-success .main-sidebar .svg-icon {
  filter: invert(0.6) sepia(1) saturate(1) hue-rotate(185deg); }
  .theme-success .main-sidebar .svg-icon:hover, .theme-success .main-sidebar .svg-icon:active, .theme-success .main-sidebar .svg-icon:focus {
    filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg); }
.theme-success .main-sidebar a:hover .svg-icon, .theme-success .main-sidebar a:active .svg-icon, .theme-success .main-sidebar a:focus .svg-icon {
  filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg); }
.theme-success .svg-icon {
  filter: invert(0.6) sepia(1) saturate(1) hue-rotate(185deg); }
  .theme-success .svg-icon:hover, .theme-success .svg-icon:active, .theme-success .svg-icon:focus {
    filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg); }
.theme-success a:hover .svg-icon, .theme-success a:active .svg-icon, .theme-success a:focus .svg-icon {
  filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg); }

.theme-success.light-skin .sidebar-menu > li.active.treeview > a {
  background: transparent;
  color: #b5b5c3 !important; }
  .theme-success.light-skin .sidebar-menu > li.active.treeview > a > i {
    color: #ffffff; }
  .theme-success.light-skin .sidebar-menu > li.active.treeview > a > svg {
    color: #ffffff;
    fill: rgba(1, 104, 250, 0.2); }
  .theme-success.light-skin .sidebar-menu > li.active.treeview > a:after {
    border-color: transparent #fafafa transparent transparent !important; }
.theme-success.light-skin .sidebar-menu > li.treeview .treeview-menu li a {
  color: #b5b5c3; }
.theme-success.light-skin.sidebar-mini.sidebar-collapse .sidebar-menu > li.active > a > span {
  background: #1dbfc1 !important; }
.theme-success.dark-skin .sidebar-menu > li.active > a:after {
  border-color: transparent #333333 transparent transparent !important; }
.theme-success.dark-skin .sidebar-menu > li.active.treeview > a {
  background: transparent;
  color: #b5b5c3 !important; }
  .theme-success.dark-skin .sidebar-menu > li.active.treeview > a > i {
    color: #ffffff; }
  .theme-success.dark-skin .sidebar-menu > li.active.treeview > a:after {
    border-color: transparent #fafafa transparent transparent !important; }
.theme-success.dark-skin .sidebar-menu > li.active.treeview .treeview-menu li a {
  color: #b5b5c3; }
.theme-success.dark-skin.sidebar-mini.sidebar-collapse .sidebar-menu > li.active > a > span {
  background: #1dbfc1 !important; }
.theme-success.light-skin .sidebar-menu > li:hover, .theme-success.light-skin .sidebar-menu > li:active, .theme-success.light-skin .sidebar-menu > li.active {
  background-color: rgba(29, 191, 193, 0);
  color: white;
  border-left: 5px solid rgba(29, 191, 193, 0); }
  .theme-success.light-skin .sidebar-menu > li:hover a, .theme-success.light-skin .sidebar-menu > li:active a, .theme-success.light-skin .sidebar-menu > li.active a {
    color: white; }
.theme-success.light-skin .sidebar-menu > li.active {
  background-color: rgba(29, 191, 193, 0);
  color: white;
  border-left: 5px solid #1dbfc1; }
  .theme-success.light-skin .sidebar-menu > li.active a {
    color: white;
    background-color: transparent; }
    .theme-success.light-skin .sidebar-menu > li.active a > i {
      color: #ffffff;
      background-color: rgba(29, 191, 193, 0); }
    .theme-success.light-skin .sidebar-menu > li.active a > svg {
      color: #ffffff;
      fill: rgba(1, 104, 250, 0.2); }
    .theme-success.light-skin .sidebar-menu > li.active a img.svg-icon {
      filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg); }
  .theme-success.light-skin .sidebar-menu > li.active .treeview-menu li.active {
    background-color: rgba(29, 191, 193, 0);
    color: white; }
    .theme-success.light-skin .sidebar-menu > li.active .treeview-menu li.active a {
      color: white; }
      .theme-success.light-skin .sidebar-menu > li.active .treeview-menu li.active a > i {
        color: white;
        background-color: rgba(29, 191, 193, 0); }
  .theme-success.light-skin .sidebar-menu > li.active .treeview-menu li a > i {
    color: #b5b5c3;
    background-color: rgba(29, 191, 193, 0); }
  .theme-success.light-skin .sidebar-menu > li.active .treeview-menu li.treeview.active {
    background-color: rgba(29, 191, 193, 0);
    color: white; }
    .theme-success.light-skin .sidebar-menu > li.active .treeview-menu li.treeview.active a {
      color: white; }
      .theme-success.light-skin .sidebar-menu > li.active .treeview-menu li.treeview.active a > i {
        color: white;
        background-color: rgba(29, 191, 193, 0); }
  .theme-success.light-skin .sidebar-menu > li.active .treeview-menu li.treeview .treeview-menu li.active {
    background-color: rgba(29, 191, 193, 0);
    color: white; }
    .theme-success.light-skin .sidebar-menu > li.active .treeview-menu li.treeview .treeview-menu li.active a {
      color: white; }
      .theme-success.light-skin .sidebar-menu > li.active .treeview-menu li.treeview .treeview-menu li.active a > i {
        color: white;
        background-color: rgba(29, 191, 193, 0); }
  .theme-success.light-skin .sidebar-menu > li.active .treeview-menu li.treeview .treeview-menu li a {
    color: #b5b5c3; }
    .theme-success.light-skin .sidebar-menu > li.active .treeview-menu li.treeview .treeview-menu li a > i {
      color: #b5b5c3;
      background-color: rgba(29, 191, 193, 0); }
.theme-success.rtl.light-skin .sidebar-menu > li:hover, .theme-success.rtl.light-skin .sidebar-menu > li:active, .theme-success.rtl.light-skin .sidebar-menu > li.active {
  border-left: 0px solid rgba(29, 191, 193, 0);
  border-right: 5px solid rgba(29, 191, 193, 0); }
.theme-success.rtl.light-skin .sidebar-menu > li.active {
  border-left: 0px solid #1dbfc1;
  border-right: 5px solid #1dbfc1; }
.theme-success.dark-skin .sidebar-menu > li.active {
  background-color: rgba(29, 191, 193, 0);
  color: white;
  border-left: 5px solid #1dbfc1; }
  .theme-success.dark-skin .sidebar-menu > li.active a {
    color: white;
    background-color: transparent; }
    .theme-success.dark-skin .sidebar-menu > li.active a > i {
      color: white; }
    .theme-success.dark-skin .sidebar-menu > li.active a > svg {
      color: #ffffff;
      fill: rgba(1, 104, 250, 0.2); }
    .theme-success.dark-skin .sidebar-menu > li.active a img.svg-icon {
      filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg); }
  .theme-success.dark-skin .sidebar-menu > li.active .treeview-menu li.active {
    background-color: rgba(29, 191, 193, 0);
    color: white; }
    .theme-success.dark-skin .sidebar-menu > li.active .treeview-menu li.active a {
      color: white !important; }
.theme-success.rtl.dark-skin .sidebar-menu > li.active {
  border-left: 0px solid #1dbfc1;
  border-right: 5px solid #1dbfc1; }

@media (min-width: 768px) {
  .sidebar-mini.sidebar-collapse .sidebar-menu > li.active.menu-open {
    background-color: rgba(29, 191, 193, 0.2);
    color: #1dbfc1; } }
/*---Main Nav---*/
.theme-success .sm-blue li.current > a, .theme-success .sm-blue li.highlighted > a {
  background: #1dbfc1;
  color: #ffffff !important; }
  .theme-success .sm-blue li.current > a:hover, .theme-success .sm-blue li.current > a:active, .theme-success .sm-blue li.current > a:focus, .theme-success .sm-blue li.highlighted > a:hover, .theme-success .sm-blue li.highlighted > a:active, .theme-success .sm-blue li.highlighted > a:focus {
    background: #1dbfc1;
    color: #ffffff !important; }
.theme-success .sm-blue a.current, .theme-success .sm-blue a.highlighted {
  background: #1dbfc1;
  color: #ffffff !important; }
.theme-success .sm-blue a:hover, .theme-success .sm-blue a:active, .theme-success .sm-blue a:focus {
  background: #1dbfc1;
  color: #ffffff !important; }
.theme-success .sm-blue ul a:hover, .theme-success .sm-blue ul a:active, .theme-success .sm-blue ul a:focus {
  background: #ebedf3;
  color: #1dbfc1 !important; }
.theme-success .sm-blue ul a.highlighted {
  background: #ebedf3;
  color: #1dbfc1 !important; }

.dark-skin.theme-success .sm-blue a.current, .dark-skin.theme-success .sm-blue a.highlighted {
  background: #1dbfc1;
  color: #ffffff !important; }
.dark-skin.theme-success .sm-blue a:hover, .dark-skin.theme-success .sm-blue a:active, .dark-skin.theme-success .sm-blue a:focus {
  background: #1dbfc1;
  color: #ffffff !important; }
.dark-skin.theme-success .sm-blue ul a:hover, .dark-skin.theme-success .sm-blue ul a:active, .dark-skin.theme-success .sm-blue ul a:focus {
  background: #29354b;
  color: #1dbfc1 !important; }
.dark-skin.theme-success .sm-blue ul a.highlighted {
  background: #29354b;
  color: #1dbfc1 !important; }

/*---Primary Button---*/
.theme-success .btn-link {
  color: #1dbfc1; }
.theme-success .btn-primary {
  background-color: #1dbfc1;
  border-color: #1dbfc1;
  color: #ffffff; }
  .theme-success .btn-primary:hover, .theme-success .btn-primary:active, .theme-success .btn-primary:focus, .theme-success .btn-primary.active {
    background-color: #169395 !important;
    border-color: #169395 !important;
    color: #ffffff !important; }
  .theme-success .btn-primary:disabled {
    background-color: #5de5e7;
    border-color: #1dbfc1;
    opacity: 0.5; }
  .theme-success .btn-primary.disabled {
    background-color: #5de5e7;
    border-color: #1dbfc1;
    opacity: 0.5; }
.theme-success .show > .btn-primary.dropdown-toggle {
  background-color: #169395 !important;
  border-color: #169395 !important;
  color: #ffffff; }
.theme-success .btn-outline.btn-primary {
  color: #1dbfc1;
  background-color: transparent;
  border-color: #1dbfc1 !important; }
  .theme-success .btn-outline.btn-primary:hover, .theme-success .btn-outline.btn-primary:active, .theme-success .btn-outline.btn-primary.active {
    background-color: #169395 !important;
    border-color: #169395 !important;
    color: #ffffff !important; }
.theme-success .show > .btn-outline.btn-primary.dropdown-toggle {
  background-color: #169395 !important;
  border-color: #169395 !important;
  color: #ffffff; }
.theme-success .btn-flat.btn-primary {
  color: #1dbfc1 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-success .btn-flat.btn-primary:hover, .theme-success .btn-flat.btn-primary:active, .theme-success .btn-flat.btn-primary.active {
    background-color: #169395 !important;
    border-color: #169395 !important;
    color: #ffffff !important; }

/*---info Button---*/
.theme-success .btn-info {
  background-color: #00D0FF;
  border-color: #00D0FF;
  color: #ffffff; }
  .theme-success .btn-info:hover, .theme-success .btn-info:active, .theme-success .btn-info:focus, .theme-success .btn-info.active {
    background-color: #00a6cc !important;
    border-color: #00a6cc !important;
    color: #ffffff !important; }
  .theme-success .btn-info:disabled {
    background-color: #66e3ff;
    border-color: #00D0FF;
    opacity: 0.5; }
  .theme-success .btn-info.disabled {
    background-color: #66e3ff;
    border-color: #00D0FF;
    opacity: 0.5; }
.theme-success .show > .btn-info.dropdown-toggle {
  background-color: #00a6cc !important;
  border-color: #00a6cc !important;
  color: #ffffff; }
.theme-success .btn-outline.btn-info {
  color: #00D0FF;
  background-color: transparent;
  border-color: #00D0FF !important; }
  .theme-success .btn-outline.btn-info:hover, .theme-success .btn-outline.btn-info:active, .theme-success .btn-outline.btn-info.active {
    background-color: #00a6cc !important;
    border-color: #00a6cc !important;
    color: #ffffff !important; }
.theme-success .show > .btn-outline.btn-info.dropdown-toggle {
  background-color: #00a6cc !important;
  border-color: #00a6cc !important;
  color: #ffffff; }
.theme-success .btn-flat.btn-info {
  color: #00D0FF !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-success .btn-flat.btn-info:hover, .theme-success .btn-flat.btn-info:active, .theme-success .btn-flat.btn-info.active {
    background-color: #00a6cc !important;
    border-color: #00a6cc !important;
    color: #ffffff !important; }

/*---Success Button---*/
.theme-success .btn-success {
  background-color: #3246D3;
  border-color: #3246D3;
  color: #ffffff; }
  .theme-success .btn-success:hover, .theme-success .btn-success:active, .theme-success .btn-success:focus, .theme-success .btn-success.active {
    background-color: #2536ad !important;
    border-color: #2536ad !important;
    color: #ffffff !important; }
  .theme-success .btn-success:disabled {
    background-color: #8692e5;
    border-color: #3246D3;
    opacity: 0.5; }
  .theme-success .btn-success.disabled {
    background-color: #8692e5;
    border-color: #3246D3;
    opacity: 0.5; }
.theme-success .show > .btn-success.dropdown-toggle {
  background-color: #2536ad !important;
  border-color: #2536ad !important;
  color: #ffffff; }
.theme-success .btn-outline.btn-success {
  color: #3246D3;
  background-color: transparent;
  border-color: #3246D3 !important; }
  .theme-success .btn-outline.btn-success:hover, .theme-success .btn-outline.btn-success:active, .theme-success .btn-outline.btn-success.active {
    background-color: #2536ad !important;
    border-color: #2536ad !important;
    color: #ffffff !important; }
.theme-success .show > .btn-outline.btn-success.dropdown-toggle {
  background-color: #2536ad !important;
  border-color: #2536ad !important;
  color: #ffffff; }
.theme-success .btn-flat.btn-success {
  color: #3246D3 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-success .btn-flat.btn-success:hover, .theme-success .btn-flat.btn-success:active, .theme-success .btn-flat.btn-success.active {
    background-color: #2536ad !important;
    border-color: #2536ad !important;
    color: #ffffff !important; }

/*---Danger Button---*/
.theme-success .btn-danger {
  background-color: #ee3158;
  border-color: #ee3158;
  color: #ffffff; }
  .theme-success .btn-danger:hover, .theme-success .btn-danger:active, .theme-success .btn-danger:focus, .theme-success .btn-danger.active {
    background-color: #da123b !important;
    border-color: #da123b !important;
    color: #ffffff !important; }
  .theme-success .btn-danger:disabled {
    background-color: #f68fa4;
    border-color: #ee3158;
    opacity: 0.5; }
  .theme-success .btn-danger.disabled {
    background-color: #f68fa4;
    border-color: #ee3158;
    opacity: 0.5; }
.theme-success .show > .btn-danger.dropdown-toggle {
  background-color: #da123b !important;
  border-color: #da123b !important;
  color: #ffffff; }
.theme-success .btn-outline.btn-danger {
  color: #ee3158;
  background-color: transparent;
  border-color: #ee3158 !important; }
  .theme-success .btn-outline.btn-danger:hover, .theme-success .btn-outline.btn-danger:active, .theme-success .btn-outline.btn-danger.active {
    background-color: #da123b !important;
    border-color: #da123b !important;
    color: #ffffff !important; }
.theme-success .show > .btn-outline.btn-danger.dropdown-toggle {
  background-color: #da123b !important;
  border-color: #da123b !important;
  color: #ffffff; }
.theme-success .btn-flat.btn-danger {
  color: #ee3158 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-success .btn-flat.btn-danger:hover, .theme-success .btn-flat.btn-danger:active, .theme-success .btn-flat.btn-danger.active {
    background-color: #da123b !important;
    border-color: #da123b !important;
    color: #ffffff !important; }

/*---Warning Button---*/
.theme-success .btn-warning {
  background-color: #ffa800;
  border-color: #ffa800;
  color: #ffffff; }
  .theme-success .btn-warning:hover, .theme-success .btn-warning:active, .theme-success .btn-warning:focus, .theme-success .btn-warning.active {
    background-color: #cc8600 !important;
    border-color: #cc8600 !important;
    color: #ffffff !important; }
  .theme-success .btn-warning:disabled {
    background-color: #ffcb66;
    border-color: #ffa800;
    opacity: 0.5; }
  .theme-success .btn-warning.disabled {
    background-color: #ffcb66;
    border-color: #ffa800;
    opacity: 0.5; }
.theme-success .show > .btn-warning.dropdown-toggle {
  background-color: #cc8600 !important;
  border-color: #cc8600 !important;
  color: #ffffff; }
.theme-success .btn-outline.btn-warning {
  color: #ffa800;
  background-color: transparent;
  border-color: #ffa800 !important; }
  .theme-success .btn-outline.btn-warning:hover, .theme-success .btn-outline.btn-warning:active, .theme-success .btn-outline.btn-warning.active {
    background-color: #cc8600 !important;
    border-color: #cc8600 !important;
    color: #ffffff !important; }
.theme-success .show > .btn-outline.btn-warning.dropdown-toggle {
  background-color: #cc8600 !important;
  border-color: #cc8600 !important;
  color: #ffffff; }
.theme-success .btn-flat.btn-warning {
  color: #ffa800 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-success .btn-flat.btn-warning:hover, .theme-success .btn-flat.btn-warning:active, .theme-success .btn-flat.btn-warning.active {
    background-color: #cc8600 !important;
    border-color: #cc8600 !important;
    color: #ffffff !important; }

/*---Primary Button light---*/
.theme-success .btn-primary-light {
  background-color: #e8f9f9;
  border-color: #e8f9f9;
  color: #1dbfc1; }
  .theme-success .btn-primary-light:hover, .theme-success .btn-primary-light:active, .theme-success .btn-primary-light:focus, .theme-success .btn-primary-light.active {
    background-color: #1dbfc1 !important;
    border-color: #1dbfc1 !important;
    color: #ffffff !important; }
  .theme-success .btn-primary-light:disabled {
    background-color: white;
    border-color: #e8f9f9;
    opacity: 0.5; }
  .theme-success .btn-primary-light.disabled {
    background-color: white;
    border-color: #e8f9f9;
    opacity: 0.5; }
.theme-success .show > .btn-primary-light.dropdown-toggle {
  background-color: #1dbfc1 !important;
  border-color: #1dbfc1 !important;
  color: #ffffff; }
.theme-success .btn-outline.btn-primary-light {
  color: #1dbfc1;
  background-color: transparent;
  border-color: #e8f9f9 !important; }
  .theme-success .btn-outline.btn-primary-light:hover, .theme-success .btn-outline.btn-primary-light:active, .theme-success .btn-outline.btn-primary-light.active {
    background-color: #1dbfc1 !important;
    border-color: #1dbfc1 !important;
    color: #ffffff !important; }
.theme-success .show > .btn-outline.btn-primary-light.dropdown-toggle {
  background-color: #1dbfc1 !important;
  border-color: #1dbfc1 !important;
  color: #ffffff; }
.theme-success .btn-flat.btn-primary-light {
  color: #1dbfc1 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-success .btn-flat.btn-primary-light:hover, .theme-success .btn-flat.btn-primary-light:active, .theme-success .btn-flat.btn-primary-light.active {
    background-color: #1dbfc1 !important;
    border-color: #1dbfc1 !important;
    color: #ffffff !important; }

/*---info Button light---*/
.theme-success .btn-info-light {
  background-color: #e1f9ff;
  border-color: #e1f9ff;
  color: #00D0FF; }
  .theme-success .btn-info-light:hover, .theme-success .btn-info-light:active, .theme-success .btn-info-light:focus, .theme-success .btn-info-light.active {
    background-color: #00D0FF !important;
    border-color: #00D0FF !important;
    color: #ffffff !important; }
  .theme-success .btn-info-light:disabled {
    background-color: white;
    border-color: #e1f9ff;
    opacity: 0.5; }
  .theme-success .btn-info-light.disabled {
    background-color: white;
    border-color: #e1f9ff;
    opacity: 0.5; }
.theme-success .show > .btn-info.dropdown-toggle {
  background-color: #00D0FF !important;
  border-color: #00D0FF !important;
  color: #ffffff; }
.theme-success .btn-outline.btn-info-light {
  color: #00D0FF;
  background-color: transparent;
  border-color: #e1f9ff !important; }
  .theme-success .btn-outline.btn-info-light:hover, .theme-success .btn-outline.btn-info-light:active, .theme-success .btn-outline.btn-info-light.active {
    background-color: #00D0FF !important;
    border-color: #00D0FF !important;
    color: #ffffff !important; }
.theme-success .show > .btn-outline.btn-info-light.dropdown-toggle {
  background-color: #00D0FF !important;
  border-color: #00D0FF !important;
  color: #ffffff; }
.theme-success .btn-flat.btn-info-light {
  color: #00D0FF !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-success .btn-flat.btn-info-light:hover, .theme-success .btn-flat.btn-info-light:active, .theme-success .btn-flat.btn-info-light.active {
    background-color: #00D0FF !important;
    border-color: #00D0FF !important;
    color: #ffffff !important; }

/*---Success Button light---*/
.theme-success .btn-success-light {
  background-color: #dbdfff;
  border-color: #dbdfff;
  color: #3246D3; }
  .theme-success .btn-success-light:hover, .theme-success .btn-success-light:active, .theme-success .btn-success-light:focus, .theme-success .btn-success-light.active {
    background-color: #3246D3 !important;
    border-color: #3246D3 !important;
    color: #ffffff !important; }
  .theme-success .btn-success-light:disabled {
    background-color: white;
    border-color: #dbdfff;
    opacity: 0.5; }
  .theme-success .btn-success-light.disabled {
    background-color: white;
    border-color: #dbdfff;
    opacity: 0.5; }
.theme-success .show > .btn-success-light.dropdown-toggle {
  background-color: #3246D3 !important;
  border-color: #3246D3 !important;
  color: #ffffff; }
.theme-success .btn-outline.btn-success-light {
  color: #3246D3;
  background-color: transparent;
  border-color: #dbdfff !important; }
  .theme-success .btn-outline.btn-success-light:hover, .theme-success .btn-outline.btn-success-light:active, .theme-success .btn-outline.btn-success-light.active {
    background-color: #3246D3 !important;
    border-color: #3246D3 !important;
    color: #ffffff !important; }
.theme-success .show > .btn-outline.btn-success-light.dropdown-toggle {
  background-color: #3246D3 !important;
  border-color: #3246D3 !important;
  color: #ffffff; }
.theme-success .btn-flat.btn-success-light {
  color: #3246D3 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-success .btn-flat.btn-success-light:hover, .theme-success .btn-flat.btn-success-light:active, .theme-success .btn-flat.btn-success-light.active {
    background-color: #3246D3 !important;
    border-color: #3246D3 !important;
    color: #ffffff !important; }

/*---Danger Button light---*/
.theme-success .btn-danger-light {
  background-color: #ffd6de;
  border-color: #ffd6de;
  color: #ee3158; }
  .theme-success .btn-danger-light:hover, .theme-success .btn-danger-light:active, .theme-success .btn-danger-light:focus, .theme-success .btn-danger-light.active {
    background-color: #ee3158 !important;
    border-color: #ee3158 !important;
    color: #ffffff !important; }
  .theme-success .btn-danger-light:disabled {
    background-color: white;
    border-color: #ffd6de;
    opacity: 0.5; }
  .theme-success .btn-danger-light.disabled {
    background-color: white;
    border-color: #ffd6de;
    opacity: 0.5; }
.theme-success .show > .btn-danger-light.dropdown-toggle {
  background-color: #ee3158 !important;
  border-color: #ee3158 !important;
  color: #ffffff; }
.theme-success .btn-outline.btn-danger-light {
  color: #ee3158;
  background-color: transparent;
  border-color: #ffd6de !important; }
  .theme-success .btn-outline.btn-danger-light:hover, .theme-success .btn-outline.btn-danger-light:active, .theme-success .btn-outline.btn-danger-light.active {
    background-color: #ee3158 !important;
    border-color: #ee3158 !important;
    color: #ffffff !important; }
.theme-success .show > .btn-outline.btn-danger-light.dropdown-toggle {
  background-color: #ee3158 !important;
  border-color: #ee3158 !important;
  color: #ffffff; }
.theme-success .btn-flat.btn-danger-light {
  color: #ee3158 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-success .btn-flat.btn-danger-light:hover, .theme-success .btn-flat.btn-danger-light:active, .theme-success .btn-flat.btn-danger-light.active {
    background-color: #ee3158 !important;
    border-color: #ee3158 !important;
    color: #ffffff !important; }

/*---Warning Button light---*/
.theme-success .btn-warning-light {
  background-color: #fff8ea;
  border-color: #fff8ea;
  color: #ffa800; }
  .theme-success .btn-warning-light:hover, .theme-success .btn-warning-light:active, .theme-success .btn-warning-light:focus, .theme-success .btn-warning-light.active {
    background-color: #ffa800 !important;
    border-color: #ffa800 !important;
    color: #ffffff !important; }
  .theme-success .btn-warning-light:disabled {
    background-color: white;
    border-color: #fff8ea;
    opacity: 0.5; }
  .theme-success .btn-warning-light.disabled {
    background-color: white;
    border-color: #fff8ea;
    opacity: 0.5; }
.theme-success .show > .btn-warning-light.dropdown-toggle {
  background-color: #ffa800 !important;
  border-color: #ffa800 !important;
  color: #ffffff; }
.theme-success .btn-outline.btn-warning-light {
  color: #ffa800;
  background-color: transparent;
  border-color: #fff8ea !important; }
  .theme-success .btn-outline.btn-warning-light:hover, .theme-success .btn-outline.btn-warning-light:active, .theme-success .btn-outline.btn-warning-light.active {
    background-color: #ffa800 !important;
    border-color: #ffa800 !important;
    color: #ffffff !important; }
.theme-success .show > .btn-outline.btn-warning-light.dropdown-toggle {
  background-color: #ffa800 !important;
  border-color: #ffa800 !important;
  color: #ffffff; }
.theme-success .btn-flat.btn-warning-light {
  color: #ffa800 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-success .btn-flat.btn-warning-light:hover, .theme-success .btn-flat.btn-warning-light:active, .theme-success .btn-flat.btn-warning-light.active {
    background-color: #ffa800 !important;
    border-color: #ffa800 !important;
    color: #ffffff !important; }

/*---callout---*/
.theme-success .callout.callout-primary {
  border-color: #1dbfc1;
  background-color: #1dbfc1 !important; }
.theme-success .callout.callout-info {
  border-color: #00D0FF;
  background-color: #00D0FF !important; }
.theme-success .callout.callout-success {
  border-color: #3246D3;
  background-color: #3246D3 !important; }
.theme-success .callout.callout-danger {
  border-color: #ee3158;
  background-color: #ee3158 !important; }
.theme-success .callout.callout-warning {
  border-color: #ffa800;
  background-color: #ffa800 !important; }

/*---alert---*/
.theme-success .alert-primary {
  border-color: #1dbfc1;
  background-color: #1dbfc1 !important;
  color: #ffffff; }
.theme-success .alert-info {
  border-color: #00D0FF;
  background-color: #00D0FF !important;
  color: #ffffff; }
.theme-success .alert-success {
  border-color: #3246D3;
  background-color: #3246D3 !important;
  color: #ffffff; }
.theme-success .alert-danger {
  border-color: #ee3158;
  background-color: #ee3158 !important;
  color: #ffffff; }
.theme-success .alert-error {
  border-color: #ee3158;
  background-color: #ee3158 !important;
  color: #ffffff; }
.theme-success .alert-warning {
  border-color: #ffa800;
  background-color: #ffa800 !important;
  color: #ffffff; }

/*---direct-chat---*/
.theme-success .direct-chat-primary .right > .direct-chat-text p {
  background-color: #1dbfc1;
  color: #ffffff; }
.theme-success .direct-chat-primary .right > .direct-chat-text:before, .theme-success .direct-chat-primary .right > .direct-chat-text:after {
  border-left-color: #1dbfc1; }
.theme-success .direct-chat-info .right > .direct-chat-text p {
  background-color: #00D0FF;
  color: #ffffff; }
.theme-success .direct-chat-info .right > .direct-chat-text:before, .theme-success .direct-chat-info .right > .direct-chat-text:after {
  border-left-color: #00D0FF; }
.theme-success .direct-chat-success .right > .direct-chat-text p {
  background-color: #3246D3;
  color: #ffffff; }
.theme-success .direct-chat-success .right > .direct-chat-text:before, .theme-success .direct-chat-success .right > .direct-chat-text:after {
  border-left-color: #3246D3; }
.theme-success .direct-chat-danger .right > .direct-chat-text p {
  background-color: #ee3158;
  color: #ffffff; }
.theme-success .direct-chat-danger .right > .direct-chat-text:before, .theme-success .direct-chat-danger .right > .direct-chat-text:after {
  border-left-color: #ee3158; }
.theme-success .direct-chat-warning .right > .direct-chat-text p {
  background-color: #ffa800;
  color: #ffffff; }
.theme-success .direct-chat-warning .right > .direct-chat-text:before, .theme-success .direct-chat-warning .right > .direct-chat-text:after {
  border-left-color: #ffa800; }
.theme-success .right .direct-chat-text p {
  background-color: #1dbfc1; }

/*---modal---*/
.theme-success .modal-primary .modal-footer, .theme-success .modal-primary .modal-header {
  border-color: #1dbfc1; }
.theme-success .modal-primary .modal-body {
  background-color: #1dbfc1 !important; }
.theme-success .modal-info .modal-footer, .theme-success .modal-info .modal-header {
  border-color: #00D0FF; }
.theme-success .modal-info .modal-body {
  background-color: #00D0FF !important; }
.theme-success .modal-success .modal-footer, .theme-success .modal-success .modal-header {
  border-color: #3246D3; }
.theme-success .modal-success .modal-body {
  background-color: #3246D3 !important; }
.theme-success .modal-danger .modal-footer, .theme-success .modal-danger .modal-header {
  border-color: #ee3158; }
.theme-success .modal-danger .modal-body {
  background-color: #ee3158 !important; }
.theme-success .modal-warning .modal-footer, .theme-success .modal-warning .modal-header {
  border-color: #ffa800; }
.theme-success .modal-warning .modal-body {
  background-color: #ffa800 !important; }

/*---border---*/
.theme-success .border-primary {
  border-color: #1dbfc1 !important; }
.theme-success .border-info {
  border-color: #00D0FF !important; }
.theme-success .border-success {
  border-color: #3246D3 !important; }
.theme-success .border-danger {
  border-color: #ee3158 !important; }
.theme-success .border-warning {
  border-color: #ffa800 !important; }

/*---Background---*/
.theme-success .bg-primary {
  background-color: #1dbfc1 !important;
  color: #ffffff; }
.theme-success .bg-primary-light {
  background-color: #e8f9f9 !important;
  color: #1dbfc1; }
.theme-success .bg-info {
  background-color: #00D0FF !important;
  color: #ffffff; }
.theme-success .bg-info-light {
  background-color: #e1f9ff !important;
  color: #00D0FF; }
.theme-success .bg-success {
  background-color: #3246D3 !important;
  color: #ffffff; }
.theme-success .bg-success-light {
  background-color: #dbdfff !important;
  color: #3246D3; }
.theme-success .bg-danger {
  background-color: #ee3158 !important;
  color: #ffffff; }
.theme-success .bg-danger-light {
  background-color: #ffd6de !important;
  color: #ee3158; }
.theme-success .bg-warning {
  background-color: #ffa800 !important;
  color: #ffffff; }
.theme-success .bg-warning-light {
  background-color: #fff8ea !important;
  color: #ffa800; }

/*---text---*/
.theme-success .text-primary {
  color: #1dbfc1 !important; }
.theme-success a.text-primary:hover, .theme-success a.text-primary:focus {
  color: #1dbfc1 !important; }
.theme-success .hover-primary:hover, .theme-success .hover-primary:focus {
  color: #1dbfc1 !important; }
.theme-success .text-info {
  color: #00D0FF !important; }
.theme-success a.text-info:hover, .theme-success a.text-info:focus {
  color: #00D0FF !important; }
.theme-success .hover-info:hover, .theme-success .hover-info:focus {
  color: #00D0FF !important; }
.theme-success .text-success {
  color: #3246D3 !important; }
.theme-success a.text-success:hover, .theme-success a.text-success:focus {
  color: #3246D3 !important; }
.theme-success .hover-success:hover, .theme-success .hover-success:focus {
  color: #3246D3 !important; }
.theme-success .text-danger {
  color: #ee3158 !important; }
.theme-success a.text-danger:hover, .theme-success a.text-danger:focus {
  color: #ee3158 !important; }
.theme-success .hover-danger:hover, .theme-success .hover-danger:focus {
  color: #ee3158 !important; }
.theme-success .text-warning {
  color: #ffa800 !important; }
.theme-success a.text-warning:hover, .theme-success a.text-warning:focus {
  color: #ffa800 !important; }
.theme-success .hover-warning:hover, .theme-success .hover-warning:focus {
  color: #ffa800 !important; }

/*---active background---*/
.theme-success .active.active-primary {
  background-color: #169395 !important; }
.theme-success .active.active-info {
  background-color: #00a6cc !important; }
.theme-success .active.active-success {
  background-color: #2536ad !important; }
.theme-success .active.active-danger {
  background-color: #da123b !important; }
.theme-success .active.active-warning {
  background-color: #cc8600 !important; }

/*---label background---*/
.theme-success .label-primary {
  background-color: #1dbfc1 !important; }
.theme-success .label-info {
  background-color: #00D0FF !important; }
.theme-success .label-success {
  background-color: #3246D3 !important; }
.theme-success .label-danger {
  background-color: #ee3158 !important; }
.theme-success .label-warning {
  background-color: #ffa800 !important; }

/*---ribbon---*/
.theme-success .ribbon-box .ribbon-primary {
  background-color: #1dbfc1; }
  .theme-success .ribbon-box .ribbon-primary:before {
    border-color: #1dbfc1 transparent transparent; }
.theme-success .ribbon-box .ribbon-two-primary span {
  background-color: #1dbfc1; }
  .theme-success .ribbon-box .ribbon-two-primary span:before {
    border-left: 3px solid #169395;
    border-top: 3px solid #169395; }
  .theme-success .ribbon-box .ribbon-two-primary span:after {
    border-right: 3px solid #169395;
    border-top: 3px solid #169395; }
.theme-success .ribbon-box .ribbon-info {
  background-color: #00D0FF; }
  .theme-success .ribbon-box .ribbon-info:before {
    border-color: #00D0FF transparent transparent; }
.theme-success .ribbon-box .ribbon-two-info span {
  background-color: #00D0FF; }
  .theme-success .ribbon-box .ribbon-two-info span:before {
    border-left: 3px solid #00a6cc;
    border-top: 3px solid #00a6cc; }
  .theme-success .ribbon-box .ribbon-two-info span:after {
    border-right: 3px solid #00a6cc;
    border-top: 3px solid #00a6cc; }
.theme-success .ribbon-box .ribbon-success {
  background-color: #3246D3; }
  .theme-success .ribbon-box .ribbon-success:before {
    border-color: #3246D3 transparent transparent; }
.theme-success .ribbon-box .ribbon-two-success span {
  background-color: #3246D3; }
  .theme-success .ribbon-box .ribbon-two-success span:before {
    border-left: 3px solid #2536ad;
    border-top: 3px solid #2536ad; }
  .theme-success .ribbon-box .ribbon-two-success span:after {
    border-right: 3px solid #2536ad;
    border-top: 3px solid #2536ad; }
.theme-success .ribbon-box .ribbon-danger {
  background-color: #ee3158; }
  .theme-success .ribbon-box .ribbon-danger:before {
    border-color: #ee3158 transparent transparent; }
.theme-success .ribbon-box .ribbon-two-danger span {
  background-color: #ee3158; }
  .theme-success .ribbon-box .ribbon-two-danger span:before {
    border-left: 3px solid #da123b;
    border-top: 3px solid #da123b; }
  .theme-success .ribbon-box .ribbon-two-danger span:after {
    border-right: 3px solid #da123b;
    border-top: 3px solid #da123b; }
.theme-success .ribbon-box .ribbon-warning {
  background-color: #ffa800; }
  .theme-success .ribbon-box .ribbon-warning:before {
    border-color: #ffa800 transparent transparent; }
.theme-success .ribbon-box .ribbon-two-warning span {
  background-color: #ffa800; }
  .theme-success .ribbon-box .ribbon-two-warning span:before {
    border-left: 3px solid #cc8600;
    border-top: 3px solid #cc8600; }
  .theme-success .ribbon-box .ribbon-two-warning span:after {
    border-right: 3px solid #cc8600;
    border-top: 3px solid #cc8600; }

/*---Box---*/
.theme-success .box-primary {
  background-color: #1dbfc1 !important; }
  .theme-success .box-primary.box-bordered {
    border-color: #1dbfc1; }
.theme-success .box-outline-primary {
  background-color: #ffffff;
  border: 1px solid #1dbfc1; }
.theme-success .box.box-solid.box-primary > .box-header {
  color: #ffffff;
  background-color: #1dbfc1; }
  .theme-success .box.box-solid.box-primary > .box-header .btn {
    color: #ffffff; }
  .theme-success .box.box-solid.box-primary > .box-header > a {
    color: #ffffff; }
.theme-success .box-info {
  background-color: #00D0FF !important; }
  .theme-success .box-info.box-bordered {
    border-color: #00D0FF; }
.theme-success .box-outline-info {
  background-color: #ffffff;
  border: 1px solid #00D0FF; }
.theme-success .box.box-solid.box-info > .box-header {
  color: #ffffff;
  background-color: #00D0FF; }
  .theme-success .box.box-solid.box-info > .box-header .btn {
    color: #ffffff; }
  .theme-success .box.box-solid.box-info > .box-header > a {
    color: #ffffff; }
.theme-success .box-success {
  background-color: #3246D3 !important; }
  .theme-success .box-success.box-bordered {
    border-color: #3246D3; }
.theme-success .box-outline-success {
  background-color: #ffffff;
  border: 1px solid #3246D3; }
.theme-success .box.box-solid.box-success > .box-header {
  color: #ffffff;
  background-color: #3246D3; }
  .theme-success .box.box-solid.box-success > .box-header .btn {
    color: #ffffff; }
  .theme-success .box.box-solid.box-success > .box-header > a {
    color: #ffffff; }
.theme-success .box-danger {
  background-color: #ee3158 !important; }
  .theme-success .box-danger.box-bordered {
    border-color: #ee3158; }
.theme-success .box-outline-danger {
  background-color: #ffffff;
  border: 1px solid #ee3158; }
.theme-success .box.box-solid.box-danger > .box-header {
  color: #ffffff;
  background-color: #ee3158; }
  .theme-success .box.box-solid.box-danger > .box-header .btn {
    color: #ffffff; }
  .theme-success .box.box-solid.box-danger > .box-header > a {
    color: #ffffff; }
.theme-success .box-warning {
  background-color: #ffa800 !important; }
  .theme-success .box-warning.box-bordered {
    border-color: #ffa800; }
.theme-success .box-outline-warning {
  background-color: #ffffff;
  border: 1px solid #ffa800; }
.theme-success .box.box-solid.box-warning > .box-header {
  color: #ffffff;
  background-color: #ffa800; }
  .theme-success .box.box-solid.box-warning > .box-header .btn {
    color: #ffffff; }
  .theme-success .box.box-solid.box-warning > .box-header > a {
    color: #ffffff; }
.theme-success .box-profile .social-states a:hover {
  color: #169395; }
.theme-success .box-controls li > a:hover {
  color: #169395; }
.theme-success .box-controls .dropdown.show > a {
  color: #169395; }
.theme-success .box-fullscreen .box-btn-fullscreen {
  color: #169395; }

/*---progress bar---*/
.theme-success .progress-bar-primary {
  background-color: #1dbfc1; }
.theme-success .progress-bar-info {
  background-color: #00D0FF; }
.theme-success .progress-bar-success {
  background-color: #3246D3; }
.theme-success .progress-bar-danger {
  background-color: #ee3158; }
.theme-success .progress-bar-warning {
  background-color: #ffa800; }

/*---panel---*/
.theme-success .panel-primary {
  border-color: #1dbfc1; }
  .theme-success .panel-primary > .panel-heading {
    color: #ffffff;
    background-color: #1dbfc1;
    border-color: #1dbfc1; }
    .theme-success .panel-primary > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #1dbfc1; }
    .theme-success .panel-primary > .panel-heading .badge-pill {
      color: #1dbfc1;
      background-color: #ffffff; }
  .theme-success .panel-primary .panel-title, .theme-success .panel-primary .panel-action {
    color: #ffffff; }
  .theme-success .panel-primary .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #1dbfc1; }
.theme-success .panel-line.panel-primary .panel-heading {
  color: #1dbfc1;
  border-top-color: #1dbfc1;
  background: transparent; }
.theme-success .panel-line.panel-primary .panel-title, .theme-success .panel-line.panel-primary .panel-action {
  color: #1dbfc1; }
.theme-success .panel-info {
  border-color: #00D0FF; }
  .theme-success .panel-info > .panel-heading {
    color: #ffffff;
    background-color: #00D0FF;
    border-color: #00D0FF; }
    .theme-success .panel-info > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #00D0FF; }
    .theme-success .panel-info > .panel-heading .badge-pill {
      color: #00D0FF;
      background-color: #ffffff; }
  .theme-success .panel-info .panel-title, .theme-success .panel-info .panel-action {
    color: #ffffff; }
  .theme-success .panel-info .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #00D0FF; }
.theme-success .panel-line.panel-info .panel-heading {
  color: #00D0FF;
  border-top-color: #00D0FF;
  background: transparent; }
.theme-success .panel-line.panel-info .panel-title, .theme-success .panel-line.panel-info .panel-action {
  color: #00D0FF; }
.theme-success .panel-success {
  border-color: #3246D3; }
  .theme-success .panel-success > .panel-heading {
    color: #ffffff;
    background-color: #3246D3;
    border-color: #3246D3; }
    .theme-success .panel-success > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #3246D3; }
    .theme-success .panel-success > .panel-heading .badge-pill {
      color: #3246D3;
      background-color: #ffffff; }
  .theme-success .panel-success .panel-title, .theme-success .panel-success .panel-action {
    color: #ffffff; }
  .theme-success .panel-success .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #3246D3; }
.theme-success .panel-line.panel-success .panel-heading {
  color: #3246D3;
  border-top-color: #3246D3;
  background: transparent; }
.theme-success .panel-line.panel-success .panel-title, .theme-success .panel-line.panel-success .panel-action {
  color: #3246D3; }
.theme-success .panel-danger {
  border-color: #ee3158; }
  .theme-success .panel-danger > .panel-heading {
    color: #ffffff;
    background-color: #ee3158;
    border-color: #ee3158; }
    .theme-success .panel-danger > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #ee3158; }
    .theme-success .panel-danger > .panel-heading .badge-pill {
      color: #ee3158;
      background-color: #ffffff; }
  .theme-success .panel-danger .panel-title, .theme-success .panel-danger .panel-action {
    color: #ffffff; }
  .theme-success .panel-danger .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #ee3158; }
.theme-success .panel-line.panel-danger .panel-heading {
  color: #ee3158;
  border-top-color: #ee3158;
  background: transparent; }
.theme-success .panel-line.panel-danger .panel-title, .theme-success .panel-line.panel-danger .panel-action {
  color: #ee3158; }
.theme-success .panel-warning {
  border-color: #ffa800; }
  .theme-success .panel-warning > .panel-heading {
    color: #ffffff;
    background-color: #ffa800;
    border-color: #ffa800; }
    .theme-success .panel-warning > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #ffa800; }
    .theme-success .panel-warning > .panel-heading .badge-pill {
      color: #ffa800;
      background-color: #ffffff; }
  .theme-success .panel-warning .panel-title, .theme-success .panel-warning .panel-action {
    color: #ffffff; }
  .theme-success .panel-warning .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #ffa800; }
.theme-success .panel-line.panel-warning .panel-heading {
  color: #ffa800;
  border-top-color: #ffa800;
  background: transparent; }
.theme-success .panel-line.panel-warning .panel-title, .theme-success .panel-line.panel-warning .panel-action {
  color: #ffa800; }

/*---switch---*/
.theme-success .switch input:checked ~ .switch-indicator::after {
  background-color: #1dbfc1; }
.theme-success .switch.switch-primary input:checked ~ .switch-indicator::after {
  background-color: #1dbfc1; }
.theme-success .switch.switch-info input:checked ~ .switch-indicator::after {
  background-color: #00D0FF; }
.theme-success .switch.switch-success input:checked ~ .switch-indicator::after {
  background-color: #3246D3; }
.theme-success .switch.switch-danger input:checked ~ .switch-indicator::after {
  background-color: #ee3158; }
.theme-success .switch.switch-warning input:checked ~ .switch-indicator::after {
  background-color: #ffa800; }

/*---badge---*/
.theme-success .badge-primary {
  background-color: #1dbfc1;
  color: #ffffff; }
.theme-success .badge-primary[href]:hover, .theme-success .badge-primary[href]:focus {
  background-color: #169395; }
.theme-success .badge-secondary {
  background-color: #e4e6ef;
  color: #172b4c; }
.theme-success .badge-secondary[href]:hover, .theme-success .badge-secondary[href]:focus {
  background-color: #c4c8dc; }
.theme-success .badge-info {
  background-color: #00D0FF;
  color: #ffffff; }
.theme-success .badge-info[href]:hover, .theme-success .badge-info[href]:focus {
  background-color: #00a6cc; }
.theme-success .badge-success {
  background-color: #3246D3;
  color: #ffffff; }
.theme-success .badge-success[href]:hover, .theme-success .badge-success[href]:focus {
  background-color: #2536ad; }
.theme-success .badge-danger {
  background-color: #ee3158;
  color: #ffffff; }
.theme-success .badge-danger[href]:hover, .theme-success .badge-danger[href]:focus {
  background-color: #da123b; }
.theme-success .badge-warning {
  background-color: #ffa800;
  color: #ffffff; }
.theme-success .badge-warning[href]:hover, .theme-success .badge-warning[href]:focus {
  background-color: #cc8600; }

/*---badge light---*/
.theme-success .badge-primary-light {
  background-color: #e8f9f9;
  color: #1dbfc1; }
.theme-success .badge-primary-light[href]:hover, .theme-success .badge-primary-light[href]:focus {
  background-color: #c0eeee; }
.theme-success .badge-secondary-light {
  background-color: #e9edf2;
  color: #172b4c; }
.theme-success .badge-secondary-light[href]:hover, .theme-success .badge-secondary-light[href]:focus {
  background-color: #c9d3df; }
.theme-success .badge-info-light {
  background-color: #e1f9ff;
  color: #00D0FF; }
.theme-success .badge-info-light[href]:hover, .theme-success .badge-info-light[href]:focus {
  background-color: #aeefff; }
.theme-success .badge-success-light {
  background-color: #dbdfff;
  color: #3246D3; }
.theme-success .badge-success-light[href]:hover, .theme-success .badge-success-light[href]:focus {
  background-color: #a8b2ff; }
.theme-success .badge-danger-light {
  background-color: #ffd6de;
  color: #ee3158; }
.theme-success .badge-danger-light[href]:hover, .theme-success .badge-danger-light[href]:focus {
  background-color: #ffa3b5; }
.theme-success .badge-warning-light {
  background-color: #fff8ea;
  color: #ffa800; }
.theme-success .badge-warning-light[href]:hover, .theme-success .badge-warning-light[href]:focus {
  background-color: #ffe7b7; }

/*---rating---*/
.theme-success .rating-primary .active {
  color: #1dbfc1; }
.theme-success .rating-primary :checked ~ label {
  color: #1dbfc1; }
.theme-success .rating-primary label:hover {
  color: #1dbfc1; }
  .theme-success .rating-primary label:hover ~ label {
    color: #1dbfc1; }
.theme-success .rating-info .active {
  color: #00D0FF; }
.theme-success .rating-info :checked ~ label {
  color: #00D0FF; }
.theme-success .rating-info label:hover {
  color: #00D0FF; }
  .theme-success .rating-info label:hover ~ label {
    color: #00D0FF; }
.theme-success .rating-success .active {
  color: #3246D3; }
.theme-success .rating-success :checked ~ label {
  color: #3246D3; }
.theme-success .rating-success label:hover {
  color: #3246D3; }
  .theme-success .rating-success label:hover ~ label {
    color: #3246D3; }
.theme-success .rating-danger .active {
  color: #ee3158; }
.theme-success .rating-danger :checked ~ label {
  color: #ee3158; }
.theme-success .rating-danger label:hover {
  color: #ee3158; }
  .theme-success .rating-danger label:hover ~ label {
    color: #ee3158; }
.theme-success .rating-warning .active {
  color: #ffa800; }
.theme-success .rating-warning :checked ~ label {
  color: #ffa800; }
.theme-success .rating-warning label:hover {
  color: #ffa800; }
  .theme-success .rating-warning label:hover ~ label {
    color: #ffa800; }

/*---toggler---*/
.theme-success .toggler-primary input:checked + i {
  color: #1dbfc1; }
.theme-success .toggler-info input:checked + i {
  color: #00D0FF; }
.theme-success .toggler-success input:checked + i {
  color: #3246D3; }
.theme-success .toggler-danger input:checked + i {
  color: #ee3158; }
.theme-success .toggler-warning input:checked + i {
  color: #ffa800; }

/*---nav tabs---*/
.theme-success .nav-tabs.nav-tabs-primary .nav-link:hover, .theme-success .nav-tabs.nav-tabs-primary .nav-link:active, .theme-success .nav-tabs.nav-tabs-primary .nav-link:focus, .theme-success .nav-tabs.nav-tabs-primary .nav-link.active {
  border-color: #169395;
  background-color: transparent;
  color: #169395; }
.theme-success .nav-tabs.nav-tabs-info .nav-link:hover, .theme-success .nav-tabs.nav-tabs-info .nav-link:active, .theme-success .nav-tabs.nav-tabs-info .nav-link:focus, .theme-success .nav-tabs.nav-tabs-info .nav-link.active {
  border-color: #00a6cc;
  background-color: #00D0FF;
  color: #ffffff; }
.theme-success .nav-tabs.nav-tabs-success .nav-link:hover, .theme-success .nav-tabs.nav-tabs-success .nav-link:active, .theme-success .nav-tabs.nav-tabs-success .nav-link:focus, .theme-success .nav-tabs.nav-tabs-success .nav-link.active {
  border-color: #2536ad;
  background-color: transparent;
  color: #2536ad; }
.theme-success .nav-tabs.nav-tabs-danger .nav-link:hover, .theme-success .nav-tabs.nav-tabs-danger .nav-link:active, .theme-success .nav-tabs.nav-tabs-danger .nav-link:focus, .theme-success .nav-tabs.nav-tabs-danger .nav-link.active {
  border-color: #da123b;
  background-color: transparent;
  color: #da123b; }
.theme-success .nav-tabs.nav-tabs-warning .nav-link:hover, .theme-success .nav-tabs.nav-tabs-warning .nav-link:active, .theme-success .nav-tabs.nav-tabs-warning .nav-link:focus, .theme-success .nav-tabs.nav-tabs-warning .nav-link.active {
  border-color: #cc8600;
  background-color: transparent;
  color: #cc8600; }
.theme-success .nav-tabs-custom.tab-primary > .nav-tabs > li a.active {
  border-top-color: #169395; }
.theme-success .nav-tabs-custom.tab-info > .nav-tabs > li a.active {
  border-top-color: #00a6cc; }
.theme-success .nav-tabs-custom.tab-success > .nav-tabs > li a.active {
  border-top-color: #2536ad; }
.theme-success .nav-tabs-custom.tab-danger > .nav-tabs > li a.active {
  border-top-color: #da123b; }
.theme-success .nav-tabs-custom.tab-warning > .nav-tabs > li a.active {
  border-top-color: #cc8600; }
.theme-success .nav-tabs .nav-link.active {
  border-bottom-color: #1dbfc1;
  background-color: #1dbfc1;
  color: #ffffff; }
  .theme-success .nav-tabs .nav-link.active:hover, .theme-success .nav-tabs .nav-link.active:focus {
    border-bottom-color: #1dbfc1;
    background-color: #1dbfc1;
    color: #ffffff; }
.theme-success .nav-tabs .nav-item.open .nav-link {
  border-bottom-color: #1dbfc1;
  background-color: #1dbfc1; }
  .theme-success .nav-tabs .nav-item.open .nav-link:hover, .theme-success .nav-tabs .nav-item.open .nav-link:focus {
    border-bottom-color: #1dbfc1;
    background-color: #1dbfc1; }

/*---todo---*/
.theme-success .todo-list .primary {
  border-left-color: #1dbfc1; }
.theme-success .todo-list .info {
  border-left-color: #1dbfc1; }
.theme-success .todo-list .success {
  border-left-color: #3246D3; }
.theme-success .todo-list .danger {
  border-left-color: #ee3158; }
.theme-success .todo-list .warning {
  border-left-color: #ffa800; }

/*---timeline---*/
.theme-success .timeline .timeline-item > .timeline-event.timeline-event-primary {
  background-color: #1dbfc1;
  border: 1px solid #1dbfc1;
  color: #ffffff; }
  .theme-success .timeline .timeline-item > .timeline-event.timeline-event-primary:before, .theme-success .timeline .timeline-item > .timeline-event.timeline-event-primary:after {
    border-left-color: #1dbfc1;
    border-right-color: #1dbfc1; }
  .theme-success .timeline .timeline-item > .timeline-event.timeline-event-primary * {
    color: inherit; }
.theme-success .timeline .timeline-item > .timeline-event.timeline-event-info {
  background-color: #00D0FF;
  border: 1px solid #00D0FF;
  color: #ffffff; }
  .theme-success .timeline .timeline-item > .timeline-event.timeline-event-info:before, .theme-success .timeline .timeline-item > .timeline-event.timeline-event-info:after {
    border-left-color: #00D0FF;
    border-right-color: #00D0FF; }
  .theme-success .timeline .timeline-item > .timeline-event.timeline-event-info * {
    color: inherit; }
.theme-success .timeline .timeline-item > .timeline-event.timeline-event-success {
  background-color: #3246D3;
  border: 1px solid #3246D3;
  color: #ffffff; }
  .theme-success .timeline .timeline-item > .timeline-event.timeline-event-success:before, .theme-success .timeline .timeline-item > .timeline-event.timeline-event-success:after {
    border-left-color: #3246D3;
    border-right-color: #3246D3; }
  .theme-success .timeline .timeline-item > .timeline-event.timeline-event-success * {
    color: inherit; }
.theme-success .timeline .timeline-item > .timeline-event.timeline-event-danger {
  background-color: #ee3158;
  border: 1px solid #ee3158;
  color: #ffffff; }
  .theme-success .timeline .timeline-item > .timeline-event.timeline-event-danger:before, .theme-success .timeline .timeline-item > .timeline-event.timeline-event-danger:after {
    border-left-color: #ee3158;
    border-right-color: #ee3158; }
  .theme-success .timeline .timeline-item > .timeline-event.timeline-event-danger * {
    color: inherit; }
.theme-success .timeline .timeline-item > .timeline-event.timeline-event-warning {
  background-color: #ffa800;
  border: 1px solid #ffa800;
  color: #ffffff; }
  .theme-success .timeline .timeline-item > .timeline-event.timeline-event-warning:before, .theme-success .timeline .timeline-item > .timeline-event.timeline-event-warning:after {
    border-left-color: #ffa800;
    border-right-color: #ffa800; }
  .theme-success .timeline .timeline-item > .timeline-event.timeline-event-warning * {
    color: inherit; }
.theme-success .timeline .timeline-item > .timeline-point.timeline-point-primary {
  color: #1dbfc1;
  background-color: #ffffff; }
.theme-success .timeline .timeline-item > .timeline-point.timeline-point-info {
  color: #00D0FF;
  background-color: #ffffff; }
.theme-success .timeline .timeline-item > .timeline-point.timeline-point-success {
  color: #3246D3;
  background-color: #ffffff; }
.theme-success .timeline .timeline-item > .timeline-point.timeline-point-danger {
  color: #ee3158;
  background-color: #ffffff; }
.theme-success .timeline .timeline-item > .timeline-point.timeline-point-warning {
  color: #ffa800;
  background-color: #ffffff; }
.theme-success .timeline .timeline-label .label-primary {
  background-color: #1dbfc1; }
.theme-success .timeline .timeline-label .label-info {
  background-color: #00D0FF; }
.theme-success .timeline .timeline-label .label-success {
  background-color: #3246D3; }
.theme-success .timeline .timeline-label .label-danger {
  background-color: #ee3158; }
.theme-success .timeline .timeline-label .label-warning {
  background-color: #ffa800; }
.theme-success .timeline__year, .theme-success .timeline5:before, .theme-success .timeline__box:before, .theme-success .timeline__date {
  background-color: #1dbfc1; }
.theme-success .timeline__post {
  border-left: 3px solid #1dbfc1; }

/*---daterangepicker---*/
.theme-success .daterangepicker td.active {
  background-color: #1dbfc1; }
  .theme-success .daterangepicker td.active:hover {
    background-color: #1dbfc1; }
.theme-success .daterangepicker .input-mini.active {
  border: 1px solid #1dbfc1; }
.theme-success .ranges li:hover, .theme-success .ranges li:active, .theme-success .ranges li.active {
  border: 1px solid #1dbfc1;
  background-color: #1dbfc1; }

/*---control-sidebar---*/
.theme-success .control-sidebar .nav-tabs.control-sidebar-tabs > li > a:hover, .theme-success .control-sidebar .nav-tabs.control-sidebar-tabs > li > a:active, .theme-success .control-sidebar .nav-tabs.control-sidebar-tabs > li > a:focus {
  border-color: #1dbfc1;
  color: #1dbfc1; }
.theme-success .control-sidebar .nav-tabs.control-sidebar-tabs > li > a.active {
  border-color: #1dbfc1;
  color: #1dbfc1; }
  .theme-success .control-sidebar .nav-tabs.control-sidebar-tabs > li > a.active:hover, .theme-success .control-sidebar .nav-tabs.control-sidebar-tabs > li > a.active:active, .theme-success .control-sidebar .nav-tabs.control-sidebar-tabs > li > a.active:focus {
    border-color: #1dbfc1;
    color: #1dbfc1; }
.theme-success .control-sidebar .rpanel-title .btn:hover {
  color: #1dbfc1; }

/*---nav---*/
.theme-success .nav > li > a:hover, .theme-success .nav > li > a:active, .theme-success .nav > li > a:focus {
  color: #1dbfc1; }
.theme-success .nav-pills > li > a.active {
  border-top-color: #1dbfc1;
  background-color: #1dbfc1 !important;
  color: #ffffff; }
  .theme-success .nav-pills > li > a.active:hover, .theme-success .nav-pills > li > a.active:focus {
    border-top-color: #1dbfc1;
    background-color: #1dbfc1 !important;
    color: #ffffff; }
.theme-success .mailbox-nav .nav-pills > li > a:hover, .theme-success .mailbox-nav .nav-pills > li > a:focus {
  border-color: #1dbfc1; }
.theme-success .mailbox-nav .nav-pills > li > a.active {
  border-color: #1dbfc1; }
  .theme-success .mailbox-nav .nav-pills > li > a.active:hover, .theme-success .mailbox-nav .nav-pills > li > a.active:focus {
    border-color: #1dbfc1; }
.theme-success .nav-tabs-custom > .nav-tabs > li a.active {
  border-top-color: #1dbfc1; }
.theme-success .profile-tab li a.nav-link.active {
  border-bottom: 2px solid #1dbfc1; }
.theme-success .customtab li a.nav-link.active {
  border-bottom: 2px solid #1dbfc1; }

/*---form-element---*/
.theme-success .form-element .input-group .input-group-addon {
  background-image: linear-gradient(45deg, #1dbfc1, #00D0FF), linear-gradient(#3b6dc1, #3b6dc1); }
.theme-success .form-element .form-control {
  background-image: linear-gradient(45deg, #1dbfc1, #00D0FF), linear-gradient(#3b6dc1, #3b6dc1); }
  .theme-success .form-element .form-control:focus {
    background-image: linear-gradient(45deg, #1dbfc1, #00D0FF), linear-gradient(#3b6dc1, #3b6dc1); }
.theme-success .form-control:focus {
  border-color: #1dbfc1; }
.theme-success [type=checkbox]:checked.chk-col-primary + label:before {
  border-right: 2px solid #1dbfc1;
  border-bottom: 2px solid #1dbfc1; }
.theme-success [type=checkbox]:checked.chk-col-info + label:before {
  border-right: 2px solid #00D0FF;
  border-bottom: 2px solid #00D0FF; }
.theme-success [type=checkbox]:checked.chk-col-success + label:before {
  border-right: 2px solid #3246D3;
  border-bottom: 2px solid #3246D3; }
.theme-success [type=checkbox]:checked.chk-col-danger + label:before {
  border-right: 2px solid #ee3158;
  border-bottom: 2px solid #ee3158; }
.theme-success [type=checkbox]:checked.chk-col-warning + label:before {
  border-right: 2px solid #ffa800;
  border-bottom: 2px solid #ffa800; }
.theme-success [type=checkbox].filled-in:checked.chk-col-primary + label:after {
  border: 2px solid #1dbfc1;
  background-color: #1dbfc1; }
.theme-success [type=checkbox].filled-in:checked.chk-col-info + label:after {
  border: 2px solid #00D0FF;
  background-color: #00D0FF; }
.theme-success [type=checkbox].filled-in:checked.chk-col-success + label:after {
  border: 2px solid #3246D3;
  background-color: #3246D3; }
.theme-success [type=checkbox].filled-in:checked.chk-col-danger + label:after {
  border: 2px solid #ee3158;
  background-color: #ee3158; }
.theme-success [type=checkbox].filled-in:checked.chk-col-warning + label:after {
  border: 2px solid #ffa800;
  background-color: #ffa800; }
.theme-success [type=radio].radio-col-primary:checked + label:after {
  background-color: #1dbfc1;
  border-color: #1dbfc1;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-success [type=radio].with-gap.radio-col-primary:checked + label:before {
  border: 2px solid #1dbfc1;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-success [type=radio].with-gap.radio-col-primary:checked + label:after {
  background-color: #1dbfc1;
  border: 2px solid #1dbfc1;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-success [type=radio].radio-col-info:checked + label:after {
  background-color: #00D0FF;
  border-color: #00D0FF;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-success [type=radio].with-gap.radio-col-info:checked + label:before {
  border: 2px solid #00D0FF;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-success [type=radio].with-gap.radio-col-info:checked + label:after {
  background-color: #00D0FF;
  border: 2px solid #00D0FF;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-success [type=radio].radio-col-success:checked + label:after {
  background-color: #3246D3;
  border-color: #3246D3;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-success [type=radio].with-gap.radio-col-success:checked + label:before {
  border: 2px solid #3246D3;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-success [type=radio].with-gap.radio-col-success:checked + label:after {
  background-color: #3246D3;
  border: 2px solid #3246D3;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-success [type=radio].radio-col-danger:checked + label:after {
  background-color: #ee3158;
  border-color: #ee3158;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-success [type=radio].with-gap.radio-col-danger:checked + label:before {
  border: 2px solid #ee3158;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-success [type=radio].with-gap.radio-col-danger:checked + label:after {
  background-color: #ee3158;
  border: 2px solid #ee3158;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-success [type=radio].radio-col-warning:checked + label:after {
  background-color: #ffa800;
  border-color: #ffa800;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-success [type=radio].with-gap.radio-col-warning:checked + label:before {
  border: 2px solid #ffa800;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-success [type=radio].with-gap.radio-col-warning:checked + label:after {
  background-color: #ffa800;
  border: 2px solid #ffa800;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-success [type=checkbox]:checked + label:before {
  border-right: 2px solid #1dbfc1;
  border-bottom: 2px solid #1dbfc1; }
.theme-success [type=checkbox].filled-in:checked + label:after {
  border: 2px solid #1dbfc1;
  background-color: #1dbfc1; }
.theme-success [type=radio].with-gap:checked + label:before, .theme-success [type=radio].with-gap:checked + label:after {
  border: 2px solid #1dbfc1; }
.theme-success [type=radio].with-gap:checked + label:after {
  background-color: #1dbfc1;
  z-index: 0; }
.theme-success [type=radio]:checked + label:after {
  border: 2px solid #1dbfc1;
  background-color: #1dbfc1;
  z-index: 0; }
.theme-success [type=checkbox].filled-in.tabbed:checked:focus + label:after {
  border-color: #1dbfc1;
  background-color: #1dbfc1; }

/*---Calender---*/
.theme-success .fx-element-overlay .fx-card-item .fx-card-content a:hover {
  color: #1dbfc1; }
.theme-success .fx-element-overlay .fx-card-item .fx-overlay-1 .fx-info > li a:hover {
  background: #1dbfc1;
  border-color: #1dbfc1; }
.theme-success .fc-event, .theme-success .calendar-event {
  background: #1dbfc1; }

/*---Tabs---*/
.theme-success .tabs-vertical li .nav-link:hover, .theme-success .tabs-vertical li .nav-link:active, .theme-success .tabs-vertical li .nav-link:focus, .theme-success .tabs-vertical li .nav-link.active {
  background-color: #1dbfc1;
  color: #ffffff; }
.theme-success .customvtab .tabs-vertical li .nav-link:hover, .theme-success .customvtab .tabs-vertical li .nav-link:active, .theme-success .customvtab .tabs-vertical li .nav-link:focus, .theme-success .customvtab .tabs-vertical li .nav-link.active {
  border-right: 2px solid #1dbfc1;
  color: #1dbfc1; }
.theme-success .customtab2 li a.nav-link:hover, .theme-success .customtab2 li a.nav-link:active, .theme-success .customtab2 li a.nav-link.active {
  background-color: #1dbfc1; }

/*---Notification---*/
.theme-success .jq-icon-primary {
  background-color: #1dbfc1;
  color: #ffffff;
  border-color: #1dbfc1; }
.theme-success .jq-icon-info {
  background-color: #00D0FF;
  color: #ffffff;
  border-color: #00D0FF; }
.theme-success .jq-icon-success {
  background-color: #3246D3;
  color: #ffffff;
  border-color: #1dbfc1; }
.theme-success .jq-icon-error {
  background-color: #ee3158;
  color: #ffffff;
  border-color: #ee3158; }
.theme-success .jq-icon-danger {
  background-color: #ee3158;
  color: #ffffff;
  border-color: #ee3158; }
.theme-success .jq-icon-warning {
  background-color: #ffa800;
  color: #ffffff;
  border-color: #ffa800; }

/*---avatar---*/
.theme-success .avatar.status-primary::after {
  background-color: #1dbfc1; }
.theme-success .avatar.status-info::after {
  background-color: #00D0FF; }
.theme-success .avatar.status-success::after {
  background-color: #3246D3; }
.theme-success .avatar.status-danger::after {
  background-color: #ee3158; }
.theme-success .avatar.status-warning::after {
  background-color: #ffa800; }
.theme-success .avatar[class*='status-']::after {
  background-color: #1dbfc1; }
.theme-success .avatar-add:hover {
  background-color: #169395;
  border-color: #169395; }

/*---media---*/
.theme-success .media-chat.media-chat-reverse .media-body p {
  background-color: #1dbfc1; }
.theme-success .media-right-out a:hover {
  color: #169395; }

/*---control---*/
.theme-success .control input:checked:focus ~ .control_indicator {
  background-color: #1dbfc1; }
.theme-success .control input:checked ~ .control_indicator {
  background-color: #1dbfc1; }
.theme-success .control:hover input:not([disabled]):checked ~ .control_indicator {
  background-color: #1dbfc1; }

/*---flex---*/
.theme-success .flex-column > li > a.nav-link.active {
  border-left-color: #1dbfc1; }
  .theme-success .flex-column > li > a.nav-link.active:hover {
    border-left-color: #1dbfc1; }

/*---pagination---*/
.theme-success .pagination li a.current {
  border: 1px solid #1dbfc1;
  background-color: #1dbfc1; }
  .theme-success .pagination li a.current:hover {
    border: 1px solid #1dbfc1;
    background-color: #1dbfc1; }
.theme-success .pagination li a:hover {
  border: 1px solid #169395;
  background-color: #169395 !important; }
.theme-success .dataTables_wrapper .dataTables_paginate .paginate_button.current {
  border: 1px solid #1dbfc1;
  background-color: #1dbfc1; }
  .theme-success .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    border: 1px solid #1dbfc1;
    background-color: #1dbfc1; }
.theme-success .paging_simple_numbers .pagination .paginate_button.active a {
  background-color: #1dbfc1; }
.theme-success .paging_simple_numbers .pagination .paginate_button:hover a {
  background-color: #1dbfc1; }
.theme-success .footable .pagination li a:hover, .theme-success .footable .pagination li a:active, .theme-success .footable .pagination li a.active {
  background-color: #1dbfc1; }

/*---dataTables---*/
.theme-success .dt-buttons .dt-button {
  background-color: #1dbfc1; }

/*---select2---*/
.theme-success .select2-container--default.select2-container--open {
  border-color: #1dbfc1; }
.theme-success .select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: #1dbfc1; }
.theme-success .select2-container--default .select2-search--dropdown .select2-search__field {
  border-color: #1dbfc1 !important; }
.theme-success .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #1dbfc1 !important; }
.theme-success .select2-container--default .select2-selection--multiple:focus {
  border-color: #1dbfc1 !important; }
.theme-success .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #1dbfc1;
  border-color: #1dbfc1; }

/*---Other---*/
.theme-success .myadmin-dd .dd-list .dd-list .dd-handle:hover {
  color: #169395; }
.theme-success .myadmin-dd-empty .dd-list .dd3-handle:hover {
  color: #169395; }
.theme-success .myadmin-dd-empty .dd-list .dd3-content:hover {
  color: #169395; }
.theme-success [data-overlay-primary]::before {
  background: #169395; }

/*---wizard---*/
.theme-success .wizard-content .wizard > .steps > ul > li.current {
  border: 2px solid #1dbfc1;
  background-color: #1dbfc1; }
.theme-success .wizard-content .wizard > .steps > ul > li.done {
  border-color: #169395;
  background-color: #169395; }
.theme-success .wizard-content .wizard > .actions > ul > li > a {
  background-color: #1dbfc1; }
.theme-success .wizard-content .wizard.wizard-circle > .steps > ul > li:after {
  background-color: #1dbfc1; }
.theme-success .wizard-content .wizard.wizard-circle > .steps > ul > li:before {
  background-color: #1dbfc1; }
.theme-success .wizard-content .wizard.wizard-notification > .steps > ul > li:after {
  background-color: #1dbfc1; }
.theme-success .wizard-content .wizard.wizard-notification > .steps > ul > li:before {
  background-color: #1dbfc1; }
.theme-success .wizard-content .wizard.wizard-notification > .steps > ul > li.current .step {
  border: 2px solid #1dbfc1;
  color: #1dbfc1; }
  .theme-success .wizard-content .wizard.wizard-notification > .steps > ul > li.current .step:after {
    border-top-color: #1dbfc1; }
.theme-success .wizard-content .wizard.wizard-notification > .steps > ul > li.done .step:after {
  border-top-color: #1dbfc1; }

@media (max-width: 767px) {
  .theme-success .wizard-content .wizard > .steps > ul > li:last-child:after {
    background-color: #1dbfc1; } }
@media (max-width: 575px) {
  .theme-success .wizard-content .wizard > .steps > ul > li.current:after {
    background-color: #1dbfc1; } }
/*---slider---*/
.theme-success #primary .slider-selection {
  background-color: #1dbfc1; }
.theme-success #info .slider-selection {
  background-color: #00D0FF; }
.theme-success #success .slider-selection {
  background-color: #3246D3; }
.theme-success #danger .slider-selection {
  background-color: #ee3158; }
.theme-success #warning .slider-selection {
  background-color: #ffa800; }

/*---horizontal-timeline---*/
.theme-success .cd-horizontal-timeline .events a.selected::after {
  background: #1dbfc1;
  border-color: #1dbfc1; }
.theme-success .cd-horizontal-timeline .events a.older-event::after {
  border-color: #1dbfc1; }
.theme-success .cd-horizontal-timeline .filling-line {
  background: #1dbfc1; }
.theme-success .cd-horizontal-timeline a {
  color: #1dbfc1; }
  .theme-success .cd-horizontal-timeline a:hover, .theme-success .cd-horizontal-timeline a:focus {
    color: #1dbfc1; }
.theme-success .cd-timeline-navigation a:hover, .theme-success .cd-timeline-navigation a:focus {
  border-color: #1dbfc1; }

/**************************************
Theme Danger Color
**************************************/
.bg-gradient-danger, .theme-danger .bg-gradient-danger, .theme-danger .art-bg {
  background: linear-gradient(45deg, #ee3158, #00D0FF); }

.bg-light-body {
  background: transparent; }

.theme-danger.fixed .main-header {
  background: transparent; }
.theme-danger .main-header {
  background: transparent; }

.theme-danger.onlyheader .art-bg {
  background-image: none; }

.bg-gradient-danger-dark, .dark-skin.theme-danger .bg-gradient-danger, .dark-skin.theme-danger .art-bg {
  background-image: linear-gradient(45deg, #ab0e2e, #007d99); }

.bg-dark-body {
  background: #0c1a32; }

.dark-skin.theme-danger.fixed .main-header {
  background: transparent; }
.dark-skin.theme-danger .main-header {
  background: transparent; }

@media (max-width: 767px) {
  .theme-danger.fixed .main-header {
    background-image: #e4e6ef; }
    .theme-danger.fixed .main-header.navbar {
      background: none; }

  .dark-skin.theme-danger.fixed .main-header {
    background-image: #0c1a32; } }
.theme-danger a:hover, .theme-danger a:active, .theme-danger a:focus {
  color: #ee3158; }
.theme-danger .main-sidebar .svg-icon {
  filter: invert(0.6) sepia(1) saturate(1) hue-rotate(185deg); }
  .theme-danger .main-sidebar .svg-icon:hover, .theme-danger .main-sidebar .svg-icon:active, .theme-danger .main-sidebar .svg-icon:focus {
    filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg); }
.theme-danger .main-sidebar a:hover .svg-icon, .theme-danger .main-sidebar a:active .svg-icon, .theme-danger .main-sidebar a:focus .svg-icon {
  filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg); }
.theme-danger .svg-icon {
  filter: invert(0.6) sepia(1) saturate(1) hue-rotate(185deg); }
  .theme-danger .svg-icon:hover, .theme-danger .svg-icon:active, .theme-danger .svg-icon:focus {
    filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg); }
.theme-danger a:hover .svg-icon, .theme-danger a:active .svg-icon, .theme-danger a:focus .svg-icon {
  filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg); }

.theme-danger.light-skin .sidebar-menu > li.active.treeview > a {
  background: transparent;
  color: #b5b5c3 !important; }
  .theme-danger.light-skin .sidebar-menu > li.active.treeview > a > i {
    color: #ffffff; }
  .theme-danger.light-skin .sidebar-menu > li.active.treeview > a > svg {
    color: #ffffff;
    fill: rgba(1, 104, 250, 0.2); }
  .theme-danger.light-skin .sidebar-menu > li.active.treeview > a:after {
    border-color: transparent #fafafa transparent transparent !important; }
.theme-danger.light-skin .sidebar-menu > li.treeview .treeview-menu li a {
  color: #b5b5c3; }
.theme-danger.light-skin.sidebar-mini.sidebar-collapse .sidebar-menu > li.active > a > span {
  background: #ee3158 !important; }
.theme-danger.dark-skin .sidebar-menu > li.active > a:after {
  border-color: transparent #333333 transparent transparent !important; }
.theme-danger.dark-skin .sidebar-menu > li.active.treeview > a {
  background: transparent;
  color: #b5b5c3 !important; }
  .theme-danger.dark-skin .sidebar-menu > li.active.treeview > a > i {
    color: #ffffff; }
  .theme-danger.dark-skin .sidebar-menu > li.active.treeview > a:after {
    border-color: transparent #fafafa transparent transparent !important; }
.theme-danger.dark-skin .sidebar-menu > li.active.treeview .treeview-menu li a {
  color: #b5b5c3; }
.theme-danger.dark-skin.sidebar-mini.sidebar-collapse .sidebar-menu > li.active > a > span {
  background: #ee3158 !important; }
.theme-danger.light-skin .sidebar-menu > li:hover, .theme-danger.light-skin .sidebar-menu > li:active, .theme-danger.light-skin .sidebar-menu > li.active {
  background-color: rgba(238, 49, 88, 0);
  color: white;
  border-left: 5px solid rgba(238, 49, 88, 0); }
  .theme-danger.light-skin .sidebar-menu > li:hover a, .theme-danger.light-skin .sidebar-menu > li:active a, .theme-danger.light-skin .sidebar-menu > li.active a {
    color: white; }
.theme-danger.light-skin .sidebar-menu > li.active {
  background-color: rgba(238, 49, 88, 0);
  color: white;
  border-left: 5px solid #ee3158; }
  .theme-danger.light-skin .sidebar-menu > li.active a {
    color: white;
    background-color: transparent; }
    .theme-danger.light-skin .sidebar-menu > li.active a > i {
      color: #ffffff;
      background-color: rgba(238, 49, 88, 0); }
    .theme-danger.light-skin .sidebar-menu > li.active a > svg {
      color: #ffffff;
      fill: rgba(1, 104, 250, 0.2); }
    .theme-danger.light-skin .sidebar-menu > li.active a img.svg-icon {
      filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg); }
  .theme-danger.light-skin .sidebar-menu > li.active .treeview-menu li.active {
    background-color: rgba(238, 49, 88, 0);
    color: white; }
    .theme-danger.light-skin .sidebar-menu > li.active .treeview-menu li.active a {
      color: white; }
      .theme-danger.light-skin .sidebar-menu > li.active .treeview-menu li.active a > i {
        color: white;
        background-color: rgba(238, 49, 88, 0); }
  .theme-danger.light-skin .sidebar-menu > li.active .treeview-menu li a > i {
    color: #b5b5c3;
    background-color: rgba(238, 49, 88, 0); }
  .theme-danger.light-skin .sidebar-menu > li.active .treeview-menu li.treeview.active {
    background-color: rgba(238, 49, 88, 0);
    color: white; }
    .theme-danger.light-skin .sidebar-menu > li.active .treeview-menu li.treeview.active a {
      color: white; }
      .theme-danger.light-skin .sidebar-menu > li.active .treeview-menu li.treeview.active a > i {
        color: white;
        background-color: rgba(238, 49, 88, 0); }
  .theme-danger.light-skin .sidebar-menu > li.active .treeview-menu li.treeview .treeview-menu li.active {
    background-color: rgba(238, 49, 88, 0);
    color: white; }
    .theme-danger.light-skin .sidebar-menu > li.active .treeview-menu li.treeview .treeview-menu li.active a {
      color: white; }
      .theme-danger.light-skin .sidebar-menu > li.active .treeview-menu li.treeview .treeview-menu li.active a > i {
        color: white;
        background-color: rgba(238, 49, 88, 0); }
  .theme-danger.light-skin .sidebar-menu > li.active .treeview-menu li.treeview .treeview-menu li a {
    color: #b5b5c3; }
    .theme-danger.light-skin .sidebar-menu > li.active .treeview-menu li.treeview .treeview-menu li a > i {
      color: #b5b5c3;
      background-color: rgba(238, 49, 88, 0); }
.theme-danger.rtl.light-skin .sidebar-menu > li:hover, .theme-danger.rtl.light-skin .sidebar-menu > li:active, .theme-danger.rtl.light-skin .sidebar-menu > li.active {
  border-left: 0px solid rgba(238, 49, 88, 0);
  border-right: 5px solid rgba(238, 49, 88, 0); }
.theme-danger.rtl.light-skin .sidebar-menu > li.active {
  border-left: 0px solid #ee3158;
  border-right: 5px solid #ee3158; }
.theme-danger.dark-skin .sidebar-menu > li.active {
  background-color: rgba(238, 49, 88, 0);
  color: white;
  border-left: 5px solid #ee3158; }
  .theme-danger.dark-skin .sidebar-menu > li.active a {
    color: white;
    background-color: transparent; }
    .theme-danger.dark-skin .sidebar-menu > li.active a > i {
      color: white; }
    .theme-danger.dark-skin .sidebar-menu > li.active a > svg {
      color: #ffffff;
      fill: rgba(1, 104, 250, 0.2); }
    .theme-danger.dark-skin .sidebar-menu > li.active a img.svg-icon {
      filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg); }
  .theme-danger.dark-skin .sidebar-menu > li.active .treeview-menu li.active {
    background-color: rgba(238, 49, 88, 0);
    color: white; }
    .theme-danger.dark-skin .sidebar-menu > li.active .treeview-menu li.active a {
      color: white !important; }
.theme-danger.rtl.dark-skin .sidebar-menu > li.active {
  border-left: 0px solid #ee3158;
  border-right: 5px solid #ee3158; }

@media (min-width: 768px) {
  .sidebar-mini.sidebar-collapse .sidebar-menu > li.active.menu-open {
    background-color: rgba(238, 49, 88, 0.2);
    color: #ee3158; } }
/*---Main Nav---*/
.theme-danger .sm-blue li.current > a, .theme-danger .sm-blue li.highlighted > a {
  background: #ee3158;
  color: #ffffff !important; }
  .theme-danger .sm-blue li.current > a:hover, .theme-danger .sm-blue li.current > a:active, .theme-danger .sm-blue li.current > a:focus, .theme-danger .sm-blue li.highlighted > a:hover, .theme-danger .sm-blue li.highlighted > a:active, .theme-danger .sm-blue li.highlighted > a:focus {
    background: #ee3158;
    color: #ffffff !important; }
.theme-danger .sm-blue a.current, .theme-danger .sm-blue a.highlighted {
  background: #ee3158;
  color: #ffffff !important; }
.theme-danger .sm-blue a:hover, .theme-danger .sm-blue a:active, .theme-danger .sm-blue a:focus {
  background: #ee3158;
  color: #ffffff !important; }
.theme-danger .sm-blue ul a:hover, .theme-danger .sm-blue ul a:active, .theme-danger .sm-blue ul a:focus {
  background: #ebedf3;
  color: #ee3158 !important; }
.theme-danger .sm-blue ul a.highlighted {
  background: #ebedf3;
  color: #ee3158 !important; }

.dark-skin.theme-danger .sm-blue a.current, .dark-skin.theme-danger .sm-blue a.highlighted {
  background: #ee3158;
  color: #ffffff !important; }
.dark-skin.theme-danger .sm-blue a:hover, .dark-skin.theme-danger .sm-blue a:active, .dark-skin.theme-danger .sm-blue a:focus {
  background: #ee3158;
  color: #ffffff !important; }
.dark-skin.theme-danger .sm-blue ul a:hover, .dark-skin.theme-danger .sm-blue ul a:active, .dark-skin.theme-danger .sm-blue ul a:focus {
  background: #29354b;
  color: #ee3158 !important; }
.dark-skin.theme-danger .sm-blue ul a.highlighted {
  background: #29354b;
  color: #ee3158 !important; }

/*---Primary Button---*/
.theme-danger .btn-link {
  color: #ee3158; }
.theme-danger .btn-primary {
  background-color: #ee3158;
  border-color: #ee3158;
  color: #ffffff; }
  .theme-danger .btn-primary:hover, .theme-danger .btn-primary:active, .theme-danger .btn-primary:focus, .theme-danger .btn-primary.active {
    background-color: #da123b !important;
    border-color: #da123b !important;
    color: #ffffff !important; }
  .theme-danger .btn-primary:disabled {
    background-color: #f68fa4;
    border-color: #ee3158;
    opacity: 0.5; }
  .theme-danger .btn-primary.disabled {
    background-color: #f68fa4;
    border-color: #ee3158;
    opacity: 0.5; }
.theme-danger .show > .btn-primary.dropdown-toggle {
  background-color: #da123b !important;
  border-color: #da123b !important;
  color: #ffffff; }
.theme-danger .btn-outline.btn-primary {
  color: #ee3158;
  background-color: transparent;
  border-color: #ee3158 !important; }
  .theme-danger .btn-outline.btn-primary:hover, .theme-danger .btn-outline.btn-primary:active, .theme-danger .btn-outline.btn-primary.active {
    background-color: #da123b !important;
    border-color: #da123b !important;
    color: #ffffff !important; }
.theme-danger .show > .btn-outline.btn-primary.dropdown-toggle {
  background-color: #da123b !important;
  border-color: #da123b !important;
  color: #ffffff; }
.theme-danger .btn-flat.btn-primary {
  color: #ee3158 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-danger .btn-flat.btn-primary:hover, .theme-danger .btn-flat.btn-primary:active, .theme-danger .btn-flat.btn-primary.active {
    background-color: #da123b !important;
    border-color: #da123b !important;
    color: #ffffff !important; }

/*---info Button---*/
.theme-danger .btn-info {
  background-color: #00D0FF;
  border-color: #00D0FF;
  color: #ffffff; }
  .theme-danger .btn-info:hover, .theme-danger .btn-info:active, .theme-danger .btn-info:focus, .theme-danger .btn-info.active {
    background-color: #00a6cc !important;
    border-color: #00a6cc !important;
    color: #ffffff !important; }
  .theme-danger .btn-info:disabled {
    background-color: #66e3ff;
    border-color: #00D0FF;
    opacity: 0.5; }
  .theme-danger .btn-info.disabled {
    background-color: #66e3ff;
    border-color: #00D0FF;
    opacity: 0.5; }
.theme-danger .show > .btn-info.dropdown-toggle {
  background-color: #00a6cc !important;
  border-color: #00a6cc !important;
  color: #ffffff; }
.theme-danger .btn-outline.btn-info {
  color: #00D0FF;
  background-color: transparent;
  border-color: #00D0FF !important; }
  .theme-danger .btn-outline.btn-info:hover, .theme-danger .btn-outline.btn-info:active, .theme-danger .btn-outline.btn-info.active {
    background-color: #00a6cc !important;
    border-color: #00a6cc !important;
    color: #ffffff !important; }
.theme-danger .show > .btn-outline.btn-info.dropdown-toggle {
  background-color: #00a6cc !important;
  border-color: #00a6cc !important;
  color: #ffffff; }
.theme-danger .btn-flat.btn-info {
  color: #00D0FF !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-danger .btn-flat.btn-info:hover, .theme-danger .btn-flat.btn-info:active, .theme-danger .btn-flat.btn-info.active {
    background-color: #00a6cc !important;
    border-color: #00a6cc !important;
    color: #ffffff !important; }

/*---Success Button---*/
.theme-danger .btn-success {
  background-color: #1dbfc1;
  border-color: #1dbfc1;
  color: #ffffff; }
  .theme-danger .btn-success:hover, .theme-danger .btn-success:active, .theme-danger .btn-success:focus, .theme-danger .btn-success.active {
    background-color: #169395 !important;
    border-color: #169395 !important;
    color: #ffffff !important; }
  .theme-danger .btn-success:disabled {
    background-color: #5de5e7;
    border-color: #1dbfc1;
    opacity: 0.5; }
  .theme-danger .btn-success.disabled {
    background-color: #5de5e7;
    border-color: #1dbfc1;
    opacity: 0.5; }
.theme-danger .show > .btn-success.dropdown-toggle {
  background-color: #169395 !important;
  border-color: #169395 !important;
  color: #ffffff; }
.theme-danger .btn-outline.btn-success {
  color: #1dbfc1;
  background-color: transparent;
  border-color: #1dbfc1 !important; }
  .theme-danger .btn-outline.btn-success:hover, .theme-danger .btn-outline.btn-success:active, .theme-danger .btn-outline.btn-success.active {
    background-color: #169395 !important;
    border-color: #169395 !important;
    color: #ffffff !important; }
.theme-danger .show > .btn-outline.btn-success.dropdown-toggle {
  background-color: #169395 !important;
  border-color: #169395 !important;
  color: #ffffff; }
.theme-danger .btn-flat.btn-success {
  color: #1dbfc1 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-danger .btn-flat.btn-success:hover, .theme-danger .btn-flat.btn-success:active, .theme-danger .btn-flat.btn-success.active {
    background-color: #169395 !important;
    border-color: #169395 !important;
    color: #ffffff !important; }

/*---Danger Button---*/
.theme-danger .btn-danger {
  background-color: #3246D3;
  border-color: #3246D3;
  color: #ffffff; }
  .theme-danger .btn-danger:hover, .theme-danger .btn-danger:active, .theme-danger .btn-danger:focus, .theme-danger .btn-danger.active {
    background-color: #2536ad !important;
    border-color: #2536ad !important;
    color: #ffffff !important; }
  .theme-danger .btn-danger:disabled {
    background-color: #8692e5;
    border-color: #3246D3;
    opacity: 0.5; }
  .theme-danger .btn-danger.disabled {
    background-color: #8692e5;
    border-color: #3246D3;
    opacity: 0.5; }
.theme-danger .show > .btn-danger.dropdown-toggle {
  background-color: #2536ad !important;
  border-color: #2536ad !important;
  color: #ffffff; }
.theme-danger .btn-outline.btn-danger {
  color: #3246D3;
  background-color: transparent;
  border-color: #3246D3 !important; }
  .theme-danger .btn-outline.btn-danger:hover, .theme-danger .btn-outline.btn-danger:active, .theme-danger .btn-outline.btn-danger.active {
    background-color: #2536ad !important;
    border-color: #2536ad !important;
    color: #ffffff !important; }
.theme-danger .show > .btn-outline.btn-danger.dropdown-toggle {
  background-color: #2536ad !important;
  border-color: #2536ad !important;
  color: #ffffff; }
.theme-danger .btn-flat.btn-danger {
  color: #3246D3 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-danger .btn-flat.btn-danger:hover, .theme-danger .btn-flat.btn-danger:active, .theme-danger .btn-flat.btn-danger.active {
    background-color: #2536ad !important;
    border-color: #2536ad !important;
    color: #ffffff !important; }

/*---Warning Button---*/
.theme-danger .btn-warning {
  background-color: #ffa800;
  border-color: #ffa800;
  color: #ffffff; }
  .theme-danger .btn-warning:hover, .theme-danger .btn-warning:active, .theme-danger .btn-warning:focus, .theme-danger .btn-warning.active {
    background-color: #cc8600 !important;
    border-color: #cc8600 !important;
    color: #ffffff !important; }
  .theme-danger .btn-warning:disabled {
    background-color: #ffcb66;
    border-color: #ffa800;
    opacity: 0.5; }
  .theme-danger .btn-warning.disabled {
    background-color: #ffcb66;
    border-color: #ffa800;
    opacity: 0.5; }
.theme-danger .show > .btn-warning.dropdown-toggle {
  background-color: #cc8600 !important;
  border-color: #cc8600 !important;
  color: #ffffff; }
.theme-danger .btn-outline.btn-warning {
  color: #ffa800;
  background-color: transparent;
  border-color: #ffa800 !important; }
  .theme-danger .btn-outline.btn-warning:hover, .theme-danger .btn-outline.btn-warning:active, .theme-danger .btn-outline.btn-warning.active {
    background-color: #cc8600 !important;
    border-color: #cc8600 !important;
    color: #ffffff !important; }
.theme-danger .show > .btn-outline.btn-warning.dropdown-toggle {
  background-color: #cc8600 !important;
  border-color: #cc8600 !important;
  color: #ffffff; }
.theme-danger .btn-flat.btn-warning {
  color: #ffa800 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-danger .btn-flat.btn-warning:hover, .theme-danger .btn-flat.btn-warning:active, .theme-danger .btn-flat.btn-warning.active {
    background-color: #cc8600 !important;
    border-color: #cc8600 !important;
    color: #ffffff !important; }

/*---Primary Button light---*/
.theme-danger .btn-primary-light {
  background-color: #ffd6de;
  border-color: #ffd6de;
  color: #ee3158; }
  .theme-danger .btn-primary-light:hover, .theme-danger .btn-primary-light:active, .theme-danger .btn-primary-light:focus, .theme-danger .btn-primary-light.active {
    background-color: #ee3158 !important;
    border-color: #ee3158 !important;
    color: #ffffff !important; }
  .theme-danger .btn-primary-light:disabled {
    background-color: white;
    border-color: #ffd6de;
    opacity: 0.5; }
  .theme-danger .btn-primary-light.disabled {
    background-color: white;
    border-color: #ffd6de;
    opacity: 0.5; }
.theme-danger .show > .btn-primary-light.dropdown-toggle {
  background-color: #ee3158 !important;
  border-color: #ee3158 !important;
  color: #ffffff; }
.theme-danger .btn-outline.btn-primary-light {
  color: #ee3158;
  background-color: transparent;
  border-color: #ffd6de !important; }
  .theme-danger .btn-outline.btn-primary-light:hover, .theme-danger .btn-outline.btn-primary-light:active, .theme-danger .btn-outline.btn-primary-light.active {
    background-color: #ee3158 !important;
    border-color: #ee3158 !important;
    color: #ffffff !important; }
.theme-danger .show > .btn-outline.btn-primary-light.dropdown-toggle {
  background-color: #ee3158 !important;
  border-color: #ee3158 !important;
  color: #ffffff; }
.theme-danger .btn-flat.btn-primary-light {
  color: #ee3158 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-danger .btn-flat.btn-primary-light:hover, .theme-danger .btn-flat.btn-primary-light:active, .theme-danger .btn-flat.btn-primary-light.active {
    background-color: #ee3158 !important;
    border-color: #ee3158 !important;
    color: #ffffff !important; }

/*---info Button light---*/
.theme-danger .btn-info-light {
  background-color: #e1f9ff;
  border-color: #e1f9ff;
  color: #00D0FF; }
  .theme-danger .btn-info-light:hover, .theme-danger .btn-info-light:active, .theme-danger .btn-info-light:focus, .theme-danger .btn-info-light.active {
    background-color: #00D0FF !important;
    border-color: #00D0FF !important;
    color: #ffffff !important; }
  .theme-danger .btn-info-light:disabled {
    background-color: white;
    border-color: #e1f9ff;
    opacity: 0.5; }
  .theme-danger .btn-info-light.disabled {
    background-color: white;
    border-color: #e1f9ff;
    opacity: 0.5; }
.theme-danger .show > .btn-info.dropdown-toggle {
  background-color: #00D0FF !important;
  border-color: #00D0FF !important;
  color: #ffffff; }
.theme-danger .btn-outline.btn-info-light {
  color: #00D0FF;
  background-color: transparent;
  border-color: #e1f9ff !important; }
  .theme-danger .btn-outline.btn-info-light:hover, .theme-danger .btn-outline.btn-info-light:active, .theme-danger .btn-outline.btn-info-light.active {
    background-color: #00D0FF !important;
    border-color: #00D0FF !important;
    color: #ffffff !important; }
.theme-danger .show > .btn-outline.btn-info-light.dropdown-toggle {
  background-color: #00D0FF !important;
  border-color: #00D0FF !important;
  color: #ffffff; }
.theme-danger .btn-flat.btn-info-light {
  color: #00D0FF !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-danger .btn-flat.btn-info-light:hover, .theme-danger .btn-flat.btn-info-light:active, .theme-danger .btn-flat.btn-info-light.active {
    background-color: #00D0FF !important;
    border-color: #00D0FF !important;
    color: #ffffff !important; }

/*---Success Button light---*/
.theme-danger .btn-success-light {
  background-color: #e8f9f9;
  border-color: #e8f9f9;
  color: #1dbfc1; }
  .theme-danger .btn-success-light:hover, .theme-danger .btn-success-light:active, .theme-danger .btn-success-light:focus, .theme-danger .btn-success-light.active {
    background-color: #1dbfc1 !important;
    border-color: #1dbfc1 !important;
    color: #ffffff !important; }
  .theme-danger .btn-success-light:disabled {
    background-color: white;
    border-color: #e8f9f9;
    opacity: 0.5; }
  .theme-danger .btn-success-light.disabled {
    background-color: white;
    border-color: #e8f9f9;
    opacity: 0.5; }
.theme-danger .show > .btn-success-light.dropdown-toggle {
  background-color: #1dbfc1 !important;
  border-color: #1dbfc1 !important;
  color: #ffffff; }
.theme-danger .btn-outline.btn-success-light {
  color: #1dbfc1;
  background-color: transparent;
  border-color: #e8f9f9 !important; }
  .theme-danger .btn-outline.btn-success-light:hover, .theme-danger .btn-outline.btn-success-light:active, .theme-danger .btn-outline.btn-success-light.active {
    background-color: #1dbfc1 !important;
    border-color: #1dbfc1 !important;
    color: #ffffff !important; }
.theme-danger .show > .btn-outline.btn-success-light.dropdown-toggle {
  background-color: #1dbfc1 !important;
  border-color: #1dbfc1 !important;
  color: #ffffff; }
.theme-danger .btn-flat.btn-success-light {
  color: #1dbfc1 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-danger .btn-flat.btn-success-light:hover, .theme-danger .btn-flat.btn-success-light:active, .theme-danger .btn-flat.btn-success-light.active {
    background-color: #1dbfc1 !important;
    border-color: #1dbfc1 !important;
    color: #ffffff !important; }

/*---Danger Button light---*/
.theme-danger .btn-danger-light {
  background-color: #dbdfff;
  border-color: #dbdfff;
  color: #3246D3; }
  .theme-danger .btn-danger-light:hover, .theme-danger .btn-danger-light:active, .theme-danger .btn-danger-light:focus, .theme-danger .btn-danger-light.active {
    background-color: #3246D3 !important;
    border-color: #3246D3 !important;
    color: #ffffff !important; }
  .theme-danger .btn-danger-light:disabled {
    background-color: white;
    border-color: #dbdfff;
    opacity: 0.5; }
  .theme-danger .btn-danger-light.disabled {
    background-color: white;
    border-color: #dbdfff;
    opacity: 0.5; }
.theme-danger .show > .btn-danger-light.dropdown-toggle {
  background-color: #3246D3 !important;
  border-color: #3246D3 !important;
  color: #ffffff; }
.theme-danger .btn-outline.btn-danger-light {
  color: #3246D3;
  background-color: transparent;
  border-color: #dbdfff !important; }
  .theme-danger .btn-outline.btn-danger-light:hover, .theme-danger .btn-outline.btn-danger-light:active, .theme-danger .btn-outline.btn-danger-light.active {
    background-color: #3246D3 !important;
    border-color: #3246D3 !important;
    color: #ffffff !important; }
.theme-danger .show > .btn-outline.btn-danger-light.dropdown-toggle {
  background-color: #3246D3 !important;
  border-color: #3246D3 !important;
  color: #ffffff; }
.theme-danger .btn-flat.btn-danger-light {
  color: #3246D3 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-danger .btn-flat.btn-danger-light:hover, .theme-danger .btn-flat.btn-danger-light:active, .theme-danger .btn-flat.btn-danger-light.active {
    background-color: #3246D3 !important;
    border-color: #3246D3 !important;
    color: #ffffff !important; }

/*---Warning Button light---*/
.theme-danger .btn-warning-light {
  background-color: #fff8ea;
  border-color: #fff8ea;
  color: #ffa800; }
  .theme-danger .btn-warning-light:hover, .theme-danger .btn-warning-light:active, .theme-danger .btn-warning-light:focus, .theme-danger .btn-warning-light.active {
    background-color: #ffa800 !important;
    border-color: #ffa800 !important;
    color: #ffffff !important; }
  .theme-danger .btn-warning-light:disabled {
    background-color: white;
    border-color: #fff8ea;
    opacity: 0.5; }
  .theme-danger .btn-warning-light.disabled {
    background-color: white;
    border-color: #fff8ea;
    opacity: 0.5; }
.theme-danger .show > .btn-warning-light.dropdown-toggle {
  background-color: #ffa800 !important;
  border-color: #ffa800 !important;
  color: #ffffff; }
.theme-danger .btn-outline.btn-warning-light {
  color: #ffa800;
  background-color: transparent;
  border-color: #fff8ea !important; }
  .theme-danger .btn-outline.btn-warning-light:hover, .theme-danger .btn-outline.btn-warning-light:active, .theme-danger .btn-outline.btn-warning-light.active {
    background-color: #ffa800 !important;
    border-color: #ffa800 !important;
    color: #ffffff !important; }
.theme-danger .show > .btn-outline.btn-warning-light.dropdown-toggle {
  background-color: #ffa800 !important;
  border-color: #ffa800 !important;
  color: #ffffff; }
.theme-danger .btn-flat.btn-warning-light {
  color: #ffa800 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-danger .btn-flat.btn-warning-light:hover, .theme-danger .btn-flat.btn-warning-light:active, .theme-danger .btn-flat.btn-warning-light.active {
    background-color: #ffa800 !important;
    border-color: #ffa800 !important;
    color: #ffffff !important; }

/*---callout---*/
.theme-danger .callout.callout-primary {
  border-color: #ee3158;
  background-color: #ee3158 !important; }
.theme-danger .callout.callout-info {
  border-color: #00D0FF;
  background-color: #00D0FF !important; }
.theme-danger .callout.callout-success {
  border-color: #1dbfc1;
  background-color: #1dbfc1 !important; }
.theme-danger .callout.callout-danger {
  border-color: #3246D3;
  background-color: #3246D3 !important; }
.theme-danger .callout.callout-warning {
  border-color: #ffa800;
  background-color: #ffa800 !important; }

/*---alert---*/
.theme-danger .alert-primary {
  border-color: #ee3158;
  background-color: #ee3158 !important;
  color: #ffffff; }
.theme-danger .alert-info {
  border-color: #00D0FF;
  background-color: #00D0FF !important;
  color: #ffffff; }
.theme-danger .alert-success {
  border-color: #1dbfc1;
  background-color: #1dbfc1 !important;
  color: #ffffff; }
.theme-danger .alert-danger {
  border-color: #3246D3;
  background-color: #3246D3 !important;
  color: #ffffff; }
.theme-danger .alert-error {
  border-color: #3246D3;
  background-color: #3246D3 !important;
  color: #ffffff; }
.theme-danger .alert-warning {
  border-color: #ffa800;
  background-color: #ffa800 !important;
  color: #ffffff; }

/*---direct-chat---*/
.theme-danger .direct-chat-primary .right > .direct-chat-text p {
  background-color: #ee3158;
  color: #ffffff; }
.theme-danger .direct-chat-primary .right > .direct-chat-text:before, .theme-danger .direct-chat-primary .right > .direct-chat-text:after {
  border-left-color: #ee3158; }
.theme-danger .direct-chat-info .right > .direct-chat-text p {
  background-color: #00D0FF;
  color: #ffffff; }
.theme-danger .direct-chat-info .right > .direct-chat-text:before, .theme-danger .direct-chat-info .right > .direct-chat-text:after {
  border-left-color: #00D0FF; }
.theme-danger .direct-chat-success .right > .direct-chat-text p {
  background-color: #1dbfc1;
  color: #ffffff; }
.theme-danger .direct-chat-success .right > .direct-chat-text:before, .theme-danger .direct-chat-success .right > .direct-chat-text:after {
  border-left-color: #1dbfc1; }
.theme-danger .direct-chat-danger .right > .direct-chat-text p {
  background-color: #3246D3;
  color: #ffffff; }
.theme-danger .direct-chat-danger .right > .direct-chat-text:before, .theme-danger .direct-chat-danger .right > .direct-chat-text:after {
  border-left-color: #3246D3; }
.theme-danger .direct-chat-warning .right > .direct-chat-text p {
  background-color: #ffa800;
  color: #ffffff; }
.theme-danger .direct-chat-warning .right > .direct-chat-text:before, .theme-danger .direct-chat-warning .right > .direct-chat-text:after {
  border-left-color: #ffa800; }
.theme-danger .right .direct-chat-text p {
  background-color: #ee3158; }

/*---modal---*/
.theme-danger .modal-primary .modal-footer, .theme-danger .modal-primary .modal-header {
  border-color: #ee3158; }
.theme-danger .modal-primary .modal-body {
  background-color: #ee3158 !important; }
.theme-danger .modal-info .modal-footer, .theme-danger .modal-info .modal-header {
  border-color: #00D0FF; }
.theme-danger .modal-info .modal-body {
  background-color: #00D0FF !important; }
.theme-danger .modal-success .modal-footer, .theme-danger .modal-success .modal-header {
  border-color: #1dbfc1; }
.theme-danger .modal-success .modal-body {
  background-color: #1dbfc1 !important; }
.theme-danger .modal-danger .modal-footer, .theme-danger .modal-danger .modal-header {
  border-color: #3246D3; }
.theme-danger .modal-danger .modal-body {
  background-color: #3246D3 !important; }
.theme-danger .modal-warning .modal-footer, .theme-danger .modal-warning .modal-header {
  border-color: #ffa800; }
.theme-danger .modal-warning .modal-body {
  background-color: #ffa800 !important; }

/*---border---*/
.theme-danger .border-primary {
  border-color: #ee3158 !important; }
.theme-danger .border-info {
  border-color: #00D0FF !important; }
.theme-danger .border-success {
  border-color: #1dbfc1 !important; }
.theme-danger .border-danger {
  border-color: #3246D3 !important; }
.theme-danger .border-warning {
  border-color: #ffa800 !important; }

/*---Background---*/
.theme-danger .bg-primary {
  background-color: #ee3158 !important;
  color: #ffffff; }
.theme-danger .bg-primary-light {
  background-color: #ffd6de !important;
  color: #ee3158; }
.theme-danger .bg-info {
  background-color: #00D0FF !important;
  color: #ffffff; }
.theme-danger .bg-info-light {
  background-color: #e1f9ff !important;
  color: #00D0FF; }
.theme-danger .bg-success {
  background-color: #1dbfc1 !important;
  color: #ffffff; }
.theme-danger .bg-success-light {
  background-color: #e8f9f9 !important;
  color: #1dbfc1; }
.theme-danger .bg-danger {
  background-color: #3246D3 !important;
  color: #ffffff; }
.theme-danger .bg-danger-light {
  background-color: #dbdfff !important;
  color: #3246D3; }
.theme-danger .bg-warning {
  background-color: #ffa800 !important;
  color: #ffffff; }
.theme-danger .bg-warning-light {
  background-color: #fff8ea !important;
  color: #ffa800; }

/*---text---*/
.theme-danger .text-primary {
  color: #ee3158 !important; }
.theme-danger a.text-primary:hover, .theme-danger a.text-primary:focus {
  color: #ee3158 !important; }
.theme-danger .hover-primary:hover, .theme-danger .hover-primary:focus {
  color: #ee3158 !important; }
.theme-danger .text-info {
  color: #00D0FF !important; }
.theme-danger a.text-info:hover, .theme-danger a.text-info:focus {
  color: #00D0FF !important; }
.theme-danger .hover-info:hover, .theme-danger .hover-info:focus {
  color: #00D0FF !important; }
.theme-danger .text-success {
  color: #1dbfc1 !important; }
.theme-danger a.text-success:hover, .theme-danger a.text-success:focus {
  color: #1dbfc1 !important; }
.theme-danger .hover-success:hover, .theme-danger .hover-success:focus {
  color: #1dbfc1 !important; }
.theme-danger .text-danger {
  color: #3246D3 !important; }
.theme-danger a.text-danger:hover, .theme-danger a.text-danger:focus {
  color: #3246D3 !important; }
.theme-danger .hover-danger:hover, .theme-danger .hover-danger:focus {
  color: #3246D3 !important; }
.theme-danger .text-warning {
  color: #ffa800 !important; }
.theme-danger a.text-warning:hover, .theme-danger a.text-warning:focus {
  color: #ffa800 !important; }
.theme-danger .hover-warning:hover, .theme-danger .hover-warning:focus {
  color: #ffa800 !important; }

/*---active background---*/
.theme-danger .active.active-primary {
  background-color: #da123b !important; }
.theme-danger .active.active-info {
  background-color: #00a6cc !important; }
.theme-danger .active.active-success {
  background-color: #169395 !important; }
.theme-danger .active.active-danger {
  background-color: #2536ad !important; }
.theme-danger .active.active-warning {
  background-color: #cc8600 !important; }

/*---label background---*/
.theme-danger .label-primary {
  background-color: #ee3158 !important; }
.theme-danger .label-info {
  background-color: #00D0FF !important; }
.theme-danger .label-success {
  background-color: #1dbfc1 !important; }
.theme-danger .label-danger {
  background-color: #3246D3 !important; }
.theme-danger .label-warning {
  background-color: #ffa800 !important; }

/*---ribbon---*/
.theme-danger .ribbon-box .ribbon-primary {
  background-color: #ee3158; }
  .theme-danger .ribbon-box .ribbon-primary:before {
    border-color: #ee3158 transparent transparent; }
.theme-danger .ribbon-box .ribbon-two-primary span {
  background-color: #ee3158; }
  .theme-danger .ribbon-box .ribbon-two-primary span:before {
    border-left: 3px solid #da123b;
    border-top: 3px solid #da123b; }
  .theme-danger .ribbon-box .ribbon-two-primary span:after {
    border-right: 3px solid #da123b;
    border-top: 3px solid #da123b; }
.theme-danger .ribbon-box .ribbon-info {
  background-color: #00D0FF; }
  .theme-danger .ribbon-box .ribbon-info:before {
    border-color: #00D0FF transparent transparent; }
.theme-danger .ribbon-box .ribbon-two-info span {
  background-color: #00D0FF; }
  .theme-danger .ribbon-box .ribbon-two-info span:before {
    border-left: 3px solid #00a6cc;
    border-top: 3px solid #00a6cc; }
  .theme-danger .ribbon-box .ribbon-two-info span:after {
    border-right: 3px solid #00a6cc;
    border-top: 3px solid #00a6cc; }
.theme-danger .ribbon-box .ribbon-success {
  background-color: #1dbfc1; }
  .theme-danger .ribbon-box .ribbon-success:before {
    border-color: #1dbfc1 transparent transparent; }
.theme-danger .ribbon-box .ribbon-two-success span {
  background-color: #1dbfc1; }
  .theme-danger .ribbon-box .ribbon-two-success span:before {
    border-left: 3px solid #169395;
    border-top: 3px solid #169395; }
  .theme-danger .ribbon-box .ribbon-two-success span:after {
    border-right: 3px solid #169395;
    border-top: 3px solid #169395; }
.theme-danger .ribbon-box .ribbon-danger {
  background-color: #3246D3; }
  .theme-danger .ribbon-box .ribbon-danger:before {
    border-color: #3246D3 transparent transparent; }
.theme-danger .ribbon-box .ribbon-two-danger span {
  background-color: #3246D3; }
  .theme-danger .ribbon-box .ribbon-two-danger span:before {
    border-left: 3px solid #2536ad;
    border-top: 3px solid #2536ad; }
  .theme-danger .ribbon-box .ribbon-two-danger span:after {
    border-right: 3px solid #2536ad;
    border-top: 3px solid #2536ad; }
.theme-danger .ribbon-box .ribbon-warning {
  background-color: #ffa800; }
  .theme-danger .ribbon-box .ribbon-warning:before {
    border-color: #ffa800 transparent transparent; }
.theme-danger .ribbon-box .ribbon-two-warning span {
  background-color: #ffa800; }
  .theme-danger .ribbon-box .ribbon-two-warning span:before {
    border-left: 3px solid #cc8600;
    border-top: 3px solid #cc8600; }
  .theme-danger .ribbon-box .ribbon-two-warning span:after {
    border-right: 3px solid #cc8600;
    border-top: 3px solid #cc8600; }

/*---Box---*/
.theme-danger .box-primary {
  background-color: #ee3158 !important; }
  .theme-danger .box-primary.box-bordered {
    border-color: #ee3158; }
.theme-danger .box-outline-primary {
  background-color: #ffffff;
  border: 1px solid #ee3158; }
.theme-danger .box.box-solid.box-primary > .box-header {
  color: #ffffff;
  background-color: #ee3158; }
  .theme-danger .box.box-solid.box-primary > .box-header .btn {
    color: #ffffff; }
  .theme-danger .box.box-solid.box-primary > .box-header > a {
    color: #ffffff; }
.theme-danger .box-info {
  background-color: #00D0FF !important; }
  .theme-danger .box-info.box-bordered {
    border-color: #00D0FF; }
.theme-danger .box-outline-info {
  background-color: #ffffff;
  border: 1px solid #00D0FF; }
.theme-danger .box.box-solid.box-info > .box-header {
  color: #ffffff;
  background-color: #00D0FF; }
  .theme-danger .box.box-solid.box-info > .box-header .btn {
    color: #ffffff; }
  .theme-danger .box.box-solid.box-info > .box-header > a {
    color: #ffffff; }
.theme-danger .box-success {
  background-color: #1dbfc1 !important; }
  .theme-danger .box-success.box-bordered {
    border-color: #1dbfc1; }
.theme-danger .box-outline-success {
  background-color: #ffffff;
  border: 1px solid #1dbfc1; }
.theme-danger .box.box-solid.box-success > .box-header {
  color: #ffffff;
  background-color: #1dbfc1; }
  .theme-danger .box.box-solid.box-success > .box-header .btn {
    color: #ffffff; }
  .theme-danger .box.box-solid.box-success > .box-header > a {
    color: #ffffff; }
.theme-danger .box-danger {
  background-color: #3246D3 !important; }
  .theme-danger .box-danger.box-bordered {
    border-color: #3246D3; }
.theme-danger .box-outline-danger {
  background-color: #ffffff;
  border: 1px solid #3246D3; }
.theme-danger .box.box-solid.box-danger > .box-header {
  color: #ffffff;
  background-color: #3246D3; }
  .theme-danger .box.box-solid.box-danger > .box-header .btn {
    color: #ffffff; }
  .theme-danger .box.box-solid.box-danger > .box-header > a {
    color: #ffffff; }
.theme-danger .box-warning {
  background-color: #ffa800 !important; }
  .theme-danger .box-warning.box-bordered {
    border-color: #ffa800; }
.theme-danger .box-outline-warning {
  background-color: #ffffff;
  border: 1px solid #ffa800; }
.theme-danger .box.box-solid.box-warning > .box-header {
  color: #ffffff;
  background-color: #ffa800; }
  .theme-danger .box.box-solid.box-warning > .box-header .btn {
    color: #ffffff; }
  .theme-danger .box.box-solid.box-warning > .box-header > a {
    color: #ffffff; }
.theme-danger .box-profile .social-states a:hover {
  color: #da123b; }
.theme-danger .box-controls li > a:hover {
  color: #da123b; }
.theme-danger .box-controls .dropdown.show > a {
  color: #da123b; }
.theme-danger .box-fullscreen .box-btn-fullscreen {
  color: #da123b; }

/*---progress bar---*/
.theme-danger .progress-bar-primary {
  background-color: #ee3158; }
.theme-danger .progress-bar-info {
  background-color: #00D0FF; }
.theme-danger .progress-bar-success {
  background-color: #1dbfc1; }
.theme-danger .progress-bar-danger {
  background-color: #3246D3; }
.theme-danger .progress-bar-warning {
  background-color: #ffa800; }

/*---panel---*/
.theme-danger .panel-primary {
  border-color: #ee3158; }
  .theme-danger .panel-primary > .panel-heading {
    color: #ffffff;
    background-color: #ee3158;
    border-color: #ee3158; }
    .theme-danger .panel-primary > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #ee3158; }
    .theme-danger .panel-primary > .panel-heading .badge-pill {
      color: #ee3158;
      background-color: #ffffff; }
  .theme-danger .panel-primary .panel-title, .theme-danger .panel-primary .panel-action {
    color: #ffffff; }
  .theme-danger .panel-primary .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #ee3158; }
.theme-danger .panel-line.panel-primary .panel-heading {
  color: #ee3158;
  border-top-color: #ee3158;
  background: transparent; }
.theme-danger .panel-line.panel-primary .panel-title, .theme-danger .panel-line.panel-primary .panel-action {
  color: #ee3158; }
.theme-danger .panel-info {
  border-color: #00D0FF; }
  .theme-danger .panel-info > .panel-heading {
    color: #ffffff;
    background-color: #00D0FF;
    border-color: #00D0FF; }
    .theme-danger .panel-info > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #00D0FF; }
    .theme-danger .panel-info > .panel-heading .badge-pill {
      color: #00D0FF;
      background-color: #ffffff; }
  .theme-danger .panel-info .panel-title, .theme-danger .panel-info .panel-action {
    color: #ffffff; }
  .theme-danger .panel-info .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #00D0FF; }
.theme-danger .panel-line.panel-info .panel-heading {
  color: #00D0FF;
  border-top-color: #00D0FF;
  background: transparent; }
.theme-danger .panel-line.panel-info .panel-title, .theme-danger .panel-line.panel-info .panel-action {
  color: #00D0FF; }
.theme-danger .panel-success {
  border-color: #1dbfc1; }
  .theme-danger .panel-success > .panel-heading {
    color: #ffffff;
    background-color: #1dbfc1;
    border-color: #1dbfc1; }
    .theme-danger .panel-success > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #1dbfc1; }
    .theme-danger .panel-success > .panel-heading .badge-pill {
      color: #1dbfc1;
      background-color: #ffffff; }
  .theme-danger .panel-success .panel-title, .theme-danger .panel-success .panel-action {
    color: #ffffff; }
  .theme-danger .panel-success .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #1dbfc1; }
.theme-danger .panel-line.panel-success .panel-heading {
  color: #1dbfc1;
  border-top-color: #1dbfc1;
  background: transparent; }
.theme-danger .panel-line.panel-success .panel-title, .theme-danger .panel-line.panel-success .panel-action {
  color: #1dbfc1; }
.theme-danger .panel-danger {
  border-color: #3246D3; }
  .theme-danger .panel-danger > .panel-heading {
    color: #ffffff;
    background-color: #3246D3;
    border-color: #3246D3; }
    .theme-danger .panel-danger > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #3246D3; }
    .theme-danger .panel-danger > .panel-heading .badge-pill {
      color: #3246D3;
      background-color: #ffffff; }
  .theme-danger .panel-danger .panel-title, .theme-danger .panel-danger .panel-action {
    color: #ffffff; }
  .theme-danger .panel-danger .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #3246D3; }
.theme-danger .panel-line.panel-danger .panel-heading {
  color: #3246D3;
  border-top-color: #3246D3;
  background: transparent; }
.theme-danger .panel-line.panel-danger .panel-title, .theme-danger .panel-line.panel-danger .panel-action {
  color: #3246D3; }
.theme-danger .panel-warning {
  border-color: #ffa800; }
  .theme-danger .panel-warning > .panel-heading {
    color: #ffffff;
    background-color: #ffa800;
    border-color: #ffa800; }
    .theme-danger .panel-warning > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #ffa800; }
    .theme-danger .panel-warning > .panel-heading .badge-pill {
      color: #ffa800;
      background-color: #ffffff; }
  .theme-danger .panel-warning .panel-title, .theme-danger .panel-warning .panel-action {
    color: #ffffff; }
  .theme-danger .panel-warning .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #ffa800; }
.theme-danger .panel-line.panel-warning .panel-heading {
  color: #ffa800;
  border-top-color: #ffa800;
  background: transparent; }
.theme-danger .panel-line.panel-warning .panel-title, .theme-danger .panel-line.panel-warning .panel-action {
  color: #ffa800; }

/*---switch---*/
.theme-danger .switch input:checked ~ .switch-indicator::after {
  background-color: #ee3158; }
.theme-danger .switch.switch-primary input:checked ~ .switch-indicator::after {
  background-color: #ee3158; }
.theme-danger .switch.switch-info input:checked ~ .switch-indicator::after {
  background-color: #00D0FF; }
.theme-danger .switch.switch-success input:checked ~ .switch-indicator::after {
  background-color: #1dbfc1; }
.theme-danger .switch.switch-danger input:checked ~ .switch-indicator::after {
  background-color: #3246D3; }
.theme-danger .switch.switch-warning input:checked ~ .switch-indicator::after {
  background-color: #ffa800; }

/*---badge---*/
.theme-danger .badge-primary {
  background-color: #ee3158;
  color: #ffffff; }
.theme-danger .badge-primary[href]:hover, .theme-danger .badge-primary[href]:focus {
  background-color: #da123b; }
.theme-danger .badge-secondary {
  background-color: #e4e6ef;
  color: #172b4c; }
.theme-danger .badge-secondary[href]:hover, .theme-danger .badge-secondary[href]:focus {
  background-color: #c4c8dc; }
.theme-danger .badge-info {
  background-color: #00D0FF;
  color: #ffffff; }
.theme-danger .badge-info[href]:hover, .theme-danger .badge-info[href]:focus {
  background-color: #00a6cc; }
.theme-danger .badge-success {
  background-color: #1dbfc1;
  color: #ffffff; }
.theme-danger .badge-success[href]:hover, .theme-danger .badge-success[href]:focus {
  background-color: #169395; }
.theme-danger .badge-danger {
  background-color: #3246D3;
  color: #ffffff; }
.theme-danger .badge-danger[href]:hover, .theme-danger .badge-danger[href]:focus {
  background-color: #2536ad; }
.theme-danger .badge-warning {
  background-color: #ffa800;
  color: #ffffff; }
.theme-danger .badge-warning[href]:hover, .theme-danger .badge-warning[href]:focus {
  background-color: #cc8600; }

/*---badge light---*/
.theme-danger .badge-primary-light {
  background-color: #ffd6de;
  color: #ee3158; }
.theme-danger .badge-primary-light[href]:hover, .theme-danger .badge-primary-light[href]:focus {
  background-color: #ffa3b5; }
.theme-danger .badge-secondary-light {
  background-color: #e9edf2;
  color: #172b4c; }
.theme-danger .badge-secondary-light[href]:hover, .theme-danger .badge-secondary-light[href]:focus {
  background-color: #c9d3df; }
.theme-danger .badge-info-light {
  background-color: #e1f9ff;
  color: #00D0FF; }
.theme-danger .badge-info-light[href]:hover, .theme-danger .badge-info-light[href]:focus {
  background-color: #aeefff; }
.theme-danger .badge-success-light {
  background-color: #e8f9f9;
  color: #1dbfc1; }
.theme-danger .badge-success-light[href]:hover, .theme-danger .badge-success-light[href]:focus {
  background-color: #c0eeee; }
.theme-danger .badge-danger-light {
  background-color: #dbdfff;
  color: #3246D3; }
.theme-danger .badge-danger-light[href]:hover, .theme-danger .badge-danger-light[href]:focus {
  background-color: #a8b2ff; }
.theme-danger .badge-warning-light {
  background-color: #fff8ea;
  color: #ffa800; }
.theme-danger .badge-warning-light[href]:hover, .theme-danger .badge-warning-light[href]:focus {
  background-color: #ffe7b7; }

/*---rating---*/
.theme-danger .rating-primary .active {
  color: #ee3158; }
.theme-danger .rating-primary :checked ~ label {
  color: #ee3158; }
.theme-danger .rating-primary label:hover {
  color: #ee3158; }
  .theme-danger .rating-primary label:hover ~ label {
    color: #ee3158; }
.theme-danger .rating-info .active {
  color: #00D0FF; }
.theme-danger .rating-info :checked ~ label {
  color: #00D0FF; }
.theme-danger .rating-info label:hover {
  color: #00D0FF; }
  .theme-danger .rating-info label:hover ~ label {
    color: #00D0FF; }
.theme-danger .rating-success .active {
  color: #1dbfc1; }
.theme-danger .rating-success :checked ~ label {
  color: #1dbfc1; }
.theme-danger .rating-success label:hover {
  color: #1dbfc1; }
  .theme-danger .rating-success label:hover ~ label {
    color: #1dbfc1; }
.theme-danger .rating-danger .active {
  color: #3246D3; }
.theme-danger .rating-danger :checked ~ label {
  color: #3246D3; }
.theme-danger .rating-danger label:hover {
  color: #3246D3; }
  .theme-danger .rating-danger label:hover ~ label {
    color: #3246D3; }
.theme-danger .rating-warning .active {
  color: #ffa800; }
.theme-danger .rating-warning :checked ~ label {
  color: #ffa800; }
.theme-danger .rating-warning label:hover {
  color: #ffa800; }
  .theme-danger .rating-warning label:hover ~ label {
    color: #ffa800; }

/*---toggler---*/
.theme-danger .toggler-primary input:checked + i {
  color: #ee3158; }
.theme-danger .toggler-info input:checked + i {
  color: #00D0FF; }
.theme-danger .toggler-success input:checked + i {
  color: #1dbfc1; }
.theme-danger .toggler-danger input:checked + i {
  color: #3246D3; }
.theme-danger .toggler-warning input:checked + i {
  color: #ffa800; }

/*---nav tabs---*/
.theme-danger .nav-tabs.nav-tabs-primary .nav-link:hover, .theme-danger .nav-tabs.nav-tabs-primary .nav-link:active, .theme-danger .nav-tabs.nav-tabs-primary .nav-link:focus, .theme-danger .nav-tabs.nav-tabs-primary .nav-link.active {
  border-color: #da123b;
  background-color: transparent;
  color: #da123b; }
.theme-danger .nav-tabs.nav-tabs-info .nav-link:hover, .theme-danger .nav-tabs.nav-tabs-info .nav-link:active, .theme-danger .nav-tabs.nav-tabs-info .nav-link:focus, .theme-danger .nav-tabs.nav-tabs-info .nav-link.active {
  border-color: #00a6cc;
  background-color: #00D0FF;
  color: #ffffff; }
.theme-danger .nav-tabs.nav-tabs-success .nav-link:hover, .theme-danger .nav-tabs.nav-tabs-success .nav-link:active, .theme-danger .nav-tabs.nav-tabs-success .nav-link:focus, .theme-danger .nav-tabs.nav-tabs-success .nav-link.active {
  border-color: #169395;
  background-color: transparent;
  color: #169395; }
.theme-danger .nav-tabs.nav-tabs-danger .nav-link:hover, .theme-danger .nav-tabs.nav-tabs-danger .nav-link:active, .theme-danger .nav-tabs.nav-tabs-danger .nav-link:focus, .theme-danger .nav-tabs.nav-tabs-danger .nav-link.active {
  border-color: #2536ad;
  background-color: transparent;
  color: #2536ad; }
.theme-danger .nav-tabs.nav-tabs-warning .nav-link:hover, .theme-danger .nav-tabs.nav-tabs-warning .nav-link:active, .theme-danger .nav-tabs.nav-tabs-warning .nav-link:focus, .theme-danger .nav-tabs.nav-tabs-warning .nav-link.active {
  border-color: #cc8600;
  background-color: transparent;
  color: #cc8600; }
.theme-danger .nav-tabs-custom.tab-primary > .nav-tabs > li a.active {
  border-top-color: #da123b; }
.theme-danger .nav-tabs-custom.tab-info > .nav-tabs > li a.active {
  border-top-color: #00a6cc; }
.theme-danger .nav-tabs-custom.tab-success > .nav-tabs > li a.active {
  border-top-color: #169395; }
.theme-danger .nav-tabs-custom.tab-danger > .nav-tabs > li a.active {
  border-top-color: #2536ad; }
.theme-danger .nav-tabs-custom.tab-warning > .nav-tabs > li a.active {
  border-top-color: #cc8600; }
.theme-danger .nav-tabs .nav-link.active {
  border-bottom-color: #ee3158;
  background-color: #ee3158;
  color: #ffffff; }
  .theme-danger .nav-tabs .nav-link.active:hover, .theme-danger .nav-tabs .nav-link.active:focus {
    border-bottom-color: #ee3158;
    background-color: #ee3158;
    color: #ffffff; }
.theme-danger .nav-tabs .nav-item.open .nav-link {
  border-bottom-color: #ee3158;
  background-color: #ee3158; }
  .theme-danger .nav-tabs .nav-item.open .nav-link:hover, .theme-danger .nav-tabs .nav-item.open .nav-link:focus {
    border-bottom-color: #ee3158;
    background-color: #ee3158; }

/*---todo---*/
.theme-danger .todo-list .primary {
  border-left-color: #ee3158; }
.theme-danger .todo-list .info {
  border-left-color: #ee3158; }
.theme-danger .todo-list .success {
  border-left-color: #1dbfc1; }
.theme-danger .todo-list .danger {
  border-left-color: #3246D3; }
.theme-danger .todo-list .warning {
  border-left-color: #ffa800; }

/*---timeline---*/
.theme-danger .timeline .timeline-item > .timeline-event.timeline-event-primary {
  background-color: #ee3158;
  border: 1px solid #ee3158;
  color: #ffffff; }
  .theme-danger .timeline .timeline-item > .timeline-event.timeline-event-primary:before, .theme-danger .timeline .timeline-item > .timeline-event.timeline-event-primary:after {
    border-left-color: #ee3158;
    border-right-color: #ee3158; }
  .theme-danger .timeline .timeline-item > .timeline-event.timeline-event-primary * {
    color: inherit; }
.theme-danger .timeline .timeline-item > .timeline-event.timeline-event-info {
  background-color: #00D0FF;
  border: 1px solid #00D0FF;
  color: #ffffff; }
  .theme-danger .timeline .timeline-item > .timeline-event.timeline-event-info:before, .theme-danger .timeline .timeline-item > .timeline-event.timeline-event-info:after {
    border-left-color: #00D0FF;
    border-right-color: #00D0FF; }
  .theme-danger .timeline .timeline-item > .timeline-event.timeline-event-info * {
    color: inherit; }
.theme-danger .timeline .timeline-item > .timeline-event.timeline-event-success {
  background-color: #1dbfc1;
  border: 1px solid #1dbfc1;
  color: #ffffff; }
  .theme-danger .timeline .timeline-item > .timeline-event.timeline-event-success:before, .theme-danger .timeline .timeline-item > .timeline-event.timeline-event-success:after {
    border-left-color: #1dbfc1;
    border-right-color: #1dbfc1; }
  .theme-danger .timeline .timeline-item > .timeline-event.timeline-event-success * {
    color: inherit; }
.theme-danger .timeline .timeline-item > .timeline-event.timeline-event-danger {
  background-color: #3246D3;
  border: 1px solid #3246D3;
  color: #ffffff; }
  .theme-danger .timeline .timeline-item > .timeline-event.timeline-event-danger:before, .theme-danger .timeline .timeline-item > .timeline-event.timeline-event-danger:after {
    border-left-color: #3246D3;
    border-right-color: #3246D3; }
  .theme-danger .timeline .timeline-item > .timeline-event.timeline-event-danger * {
    color: inherit; }
.theme-danger .timeline .timeline-item > .timeline-event.timeline-event-warning {
  background-color: #ffa800;
  border: 1px solid #ffa800;
  color: #ffffff; }
  .theme-danger .timeline .timeline-item > .timeline-event.timeline-event-warning:before, .theme-danger .timeline .timeline-item > .timeline-event.timeline-event-warning:after {
    border-left-color: #ffa800;
    border-right-color: #ffa800; }
  .theme-danger .timeline .timeline-item > .timeline-event.timeline-event-warning * {
    color: inherit; }
.theme-danger .timeline .timeline-item > .timeline-point.timeline-point-primary {
  color: #ee3158;
  background-color: #ffffff; }
.theme-danger .timeline .timeline-item > .timeline-point.timeline-point-info {
  color: #00D0FF;
  background-color: #ffffff; }
.theme-danger .timeline .timeline-item > .timeline-point.timeline-point-success {
  color: #1dbfc1;
  background-color: #ffffff; }
.theme-danger .timeline .timeline-item > .timeline-point.timeline-point-danger {
  color: #3246D3;
  background-color: #ffffff; }
.theme-danger .timeline .timeline-item > .timeline-point.timeline-point-warning {
  color: #ffa800;
  background-color: #ffffff; }
.theme-danger .timeline .timeline-label .label-primary {
  background-color: #ee3158; }
.theme-danger .timeline .timeline-label .label-info {
  background-color: #00D0FF; }
.theme-danger .timeline .timeline-label .label-success {
  background-color: #1dbfc1; }
.theme-danger .timeline .timeline-label .label-danger {
  background-color: #3246D3; }
.theme-danger .timeline .timeline-label .label-warning {
  background-color: #ffa800; }
.theme-danger .timeline__year, .theme-danger .timeline5:before, .theme-danger .timeline__box:before, .theme-danger .timeline__date {
  background-color: #ee3158; }
.theme-danger .timeline__post {
  border-left: 3px solid #ee3158; }

/*---daterangepicker---*/
.theme-danger .daterangepicker td.active {
  background-color: #ee3158; }
  .theme-danger .daterangepicker td.active:hover {
    background-color: #ee3158; }
.theme-danger .daterangepicker .input-mini.active {
  border: 1px solid #ee3158; }
.theme-danger .ranges li:hover, .theme-danger .ranges li:active, .theme-danger .ranges li.active {
  border: 1px solid #ee3158;
  background-color: #ee3158; }

/*---control-sidebar---*/
.theme-danger .control-sidebar .nav-tabs.control-sidebar-tabs > li > a:hover, .theme-danger .control-sidebar .nav-tabs.control-sidebar-tabs > li > a:active, .theme-danger .control-sidebar .nav-tabs.control-sidebar-tabs > li > a:focus {
  border-color: #ee3158;
  color: #ee3158; }
.theme-danger .control-sidebar .nav-tabs.control-sidebar-tabs > li > a.active {
  border-color: #ee3158;
  color: #ee3158; }
  .theme-danger .control-sidebar .nav-tabs.control-sidebar-tabs > li > a.active:hover, .theme-danger .control-sidebar .nav-tabs.control-sidebar-tabs > li > a.active:active, .theme-danger .control-sidebar .nav-tabs.control-sidebar-tabs > li > a.active:focus {
    border-color: #ee3158;
    color: #ee3158; }
.theme-danger .control-sidebar .rpanel-title .btn:hover {
  color: #ee3158; }

/*---nav---*/
.theme-danger .nav > li > a:hover, .theme-danger .nav > li > a:active, .theme-danger .nav > li > a:focus {
  color: #ee3158; }
.theme-danger .nav-pills > li > a.active {
  border-top-color: #ee3158;
  background-color: #ee3158 !important;
  color: #ffffff; }
  .theme-danger .nav-pills > li > a.active:hover, .theme-danger .nav-pills > li > a.active:focus {
    border-top-color: #ee3158;
    background-color: #ee3158 !important;
    color: #ffffff; }
.theme-danger .mailbox-nav .nav-pills > li > a:hover, .theme-danger .mailbox-nav .nav-pills > li > a:focus {
  border-color: #ee3158; }
.theme-danger .mailbox-nav .nav-pills > li > a.active {
  border-color: #ee3158; }
  .theme-danger .mailbox-nav .nav-pills > li > a.active:hover, .theme-danger .mailbox-nav .nav-pills > li > a.active:focus {
    border-color: #ee3158; }
.theme-danger .nav-tabs-custom > .nav-tabs > li a.active {
  border-top-color: #ee3158; }
.theme-danger .profile-tab li a.nav-link.active {
  border-bottom: 2px solid #ee3158; }
.theme-danger .customtab li a.nav-link.active {
  border-bottom: 2px solid #ee3158; }

/*---form-element---*/
.theme-danger .form-element .input-group .input-group-addon {
  background-image: linear-gradient(45deg, #ee3158, #00D0FF), linear-gradient(#3b6dc1, #3b6dc1); }
.theme-danger .form-element .form-control {
  background-image: linear-gradient(45deg, #ee3158, #00D0FF), linear-gradient(#3b6dc1, #3b6dc1); }
  .theme-danger .form-element .form-control:focus {
    background-image: linear-gradient(45deg, #ee3158, #00D0FF), linear-gradient(#3b6dc1, #3b6dc1); }
.theme-danger .form-control:focus {
  border-color: #ee3158; }
.theme-danger [type=checkbox]:checked.chk-col-primary + label:before {
  border-right: 2px solid #ee3158;
  border-bottom: 2px solid #ee3158; }
.theme-danger [type=checkbox]:checked.chk-col-info + label:before {
  border-right: 2px solid #00D0FF;
  border-bottom: 2px solid #00D0FF; }
.theme-danger [type=checkbox]:checked.chk-col-success + label:before {
  border-right: 2px solid #1dbfc1;
  border-bottom: 2px solid #1dbfc1; }
.theme-danger [type=checkbox]:checked.chk-col-danger + label:before {
  border-right: 2px solid #3246D3;
  border-bottom: 2px solid #3246D3; }
.theme-danger [type=checkbox]:checked.chk-col-warning + label:before {
  border-right: 2px solid #ffa800;
  border-bottom: 2px solid #ffa800; }
.theme-danger [type=checkbox].filled-in:checked.chk-col-primary + label:after {
  border: 2px solid #ee3158;
  background-color: #ee3158; }
.theme-danger [type=checkbox].filled-in:checked.chk-col-info + label:after {
  border: 2px solid #00D0FF;
  background-color: #00D0FF; }
.theme-danger [type=checkbox].filled-in:checked.chk-col-success + label:after {
  border: 2px solid #1dbfc1;
  background-color: #1dbfc1; }
.theme-danger [type=checkbox].filled-in:checked.chk-col-danger + label:after {
  border: 2px solid #3246D3;
  background-color: #3246D3; }
.theme-danger [type=checkbox].filled-in:checked.chk-col-warning + label:after {
  border: 2px solid #ffa800;
  background-color: #ffa800; }
.theme-danger [type=radio].radio-col-primary:checked + label:after {
  background-color: #ee3158;
  border-color: #ee3158;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-danger [type=radio].with-gap.radio-col-primary:checked + label:before {
  border: 2px solid #ee3158;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-danger [type=radio].with-gap.radio-col-primary:checked + label:after {
  background-color: #ee3158;
  border: 2px solid #ee3158;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-danger [type=radio].radio-col-info:checked + label:after {
  background-color: #00D0FF;
  border-color: #00D0FF;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-danger [type=radio].with-gap.radio-col-info:checked + label:before {
  border: 2px solid #00D0FF;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-danger [type=radio].with-gap.radio-col-info:checked + label:after {
  background-color: #00D0FF;
  border: 2px solid #00D0FF;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-danger [type=radio].radio-col-success:checked + label:after {
  background-color: #1dbfc1;
  border-color: #1dbfc1;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-danger [type=radio].with-gap.radio-col-success:checked + label:before {
  border: 2px solid #1dbfc1;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-danger [type=radio].with-gap.radio-col-success:checked + label:after {
  background-color: #1dbfc1;
  border: 2px solid #1dbfc1;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-danger [type=radio].radio-col-danger:checked + label:after {
  background-color: #3246D3;
  border-color: #3246D3;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-danger [type=radio].with-gap.radio-col-danger:checked + label:before {
  border: 2px solid #3246D3;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-danger [type=radio].with-gap.radio-col-danger:checked + label:after {
  background-color: #3246D3;
  border: 2px solid #3246D3;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-danger [type=radio].radio-col-warning:checked + label:after {
  background-color: #ffa800;
  border-color: #ffa800;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-danger [type=radio].with-gap.radio-col-warning:checked + label:before {
  border: 2px solid #ffa800;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-danger [type=radio].with-gap.radio-col-warning:checked + label:after {
  background-color: #ffa800;
  border: 2px solid #ffa800;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-danger [type=checkbox]:checked + label:before {
  border-right: 2px solid #ee3158;
  border-bottom: 2px solid #ee3158; }
.theme-danger [type=checkbox].filled-in:checked + label:after {
  border: 2px solid #ee3158;
  background-color: #ee3158; }
.theme-danger [type=radio].with-gap:checked + label:before, .theme-danger [type=radio].with-gap:checked + label:after {
  border: 2px solid #ee3158; }
.theme-danger [type=radio].with-gap:checked + label:after {
  background-color: #ee3158;
  z-index: 0; }
.theme-danger [type=radio]:checked + label:after {
  border: 2px solid #ee3158;
  background-color: #ee3158;
  z-index: 0; }
.theme-danger [type=checkbox].filled-in.tabbed:checked:focus + label:after {
  border-color: #ee3158;
  background-color: #ee3158; }

/*---Calender---*/
.theme-danger .fx-element-overlay .fx-card-item .fx-card-content a:hover {
  color: #ee3158; }
.theme-danger .fx-element-overlay .fx-card-item .fx-overlay-1 .fx-info > li a:hover {
  background: #ee3158;
  border-color: #ee3158; }
.theme-danger .fc-event, .theme-danger .calendar-event {
  background: #ee3158; }

/*---Tabs---*/
.theme-danger .tabs-vertical li .nav-link:hover, .theme-danger .tabs-vertical li .nav-link:active, .theme-danger .tabs-vertical li .nav-link:focus, .theme-danger .tabs-vertical li .nav-link.active {
  background-color: #ee3158;
  color: #ffffff; }
.theme-danger .customvtab .tabs-vertical li .nav-link:hover, .theme-danger .customvtab .tabs-vertical li .nav-link:active, .theme-danger .customvtab .tabs-vertical li .nav-link:focus, .theme-danger .customvtab .tabs-vertical li .nav-link.active {
  border-right: 2px solid #ee3158;
  color: #ee3158; }
.theme-danger .customtab2 li a.nav-link:hover, .theme-danger .customtab2 li a.nav-link:active, .theme-danger .customtab2 li a.nav-link.active {
  background-color: #ee3158; }

/*---Notification---*/
.theme-danger .jq-icon-primary {
  background-color: #ee3158;
  color: #ffffff;
  border-color: #ee3158; }
.theme-danger .jq-icon-info {
  background-color: #00D0FF;
  color: #ffffff;
  border-color: #00D0FF; }
.theme-danger .jq-icon-success {
  background-color: #1dbfc1;
  color: #ffffff;
  border-color: #ee3158; }
.theme-danger .jq-icon-error {
  background-color: #3246D3;
  color: #ffffff;
  border-color: #3246D3; }
.theme-danger .jq-icon-danger {
  background-color: #3246D3;
  color: #ffffff;
  border-color: #3246D3; }
.theme-danger .jq-icon-warning {
  background-color: #ffa800;
  color: #ffffff;
  border-color: #ffa800; }

/*---avatar---*/
.theme-danger .avatar.status-primary::after {
  background-color: #ee3158; }
.theme-danger .avatar.status-info::after {
  background-color: #00D0FF; }
.theme-danger .avatar.status-success::after {
  background-color: #1dbfc1; }
.theme-danger .avatar.status-danger::after {
  background-color: #3246D3; }
.theme-danger .avatar.status-warning::after {
  background-color: #ffa800; }
.theme-danger .avatar[class*='status-']::after {
  background-color: #ee3158; }
.theme-danger .avatar-add:hover {
  background-color: #da123b;
  border-color: #da123b; }

/*---media---*/
.theme-danger .media-chat.media-chat-reverse .media-body p {
  background-color: #ee3158; }
.theme-danger .media-right-out a:hover {
  color: #da123b; }

/*---control---*/
.theme-danger .control input:checked:focus ~ .control_indicator {
  background-color: #ee3158; }
.theme-danger .control input:checked ~ .control_indicator {
  background-color: #ee3158; }
.theme-danger .control:hover input:not([disabled]):checked ~ .control_indicator {
  background-color: #ee3158; }

/*---flex---*/
.theme-danger .flex-column > li > a.nav-link.active {
  border-left-color: #ee3158; }
  .theme-danger .flex-column > li > a.nav-link.active:hover {
    border-left-color: #ee3158; }

/*---pagination---*/
.theme-danger .pagination li a.current {
  border: 1px solid #ee3158;
  background-color: #ee3158; }
  .theme-danger .pagination li a.current:hover {
    border: 1px solid #ee3158;
    background-color: #ee3158; }
.theme-danger .pagination li a:hover {
  border: 1px solid #da123b;
  background-color: #da123b !important; }
.theme-danger .dataTables_wrapper .dataTables_paginate .paginate_button.current {
  border: 1px solid #ee3158;
  background-color: #ee3158; }
  .theme-danger .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    border: 1px solid #ee3158;
    background-color: #ee3158; }
.theme-danger .paging_simple_numbers .pagination .paginate_button.active a {
  background-color: #ee3158; }
.theme-danger .paging_simple_numbers .pagination .paginate_button:hover a {
  background-color: #ee3158; }
.theme-danger .footable .pagination li a:hover, .theme-danger .footable .pagination li a:active, .theme-danger .footable .pagination li a.active {
  background-color: #ee3158; }

/*---dataTables---*/
.theme-danger .dt-buttons .dt-button {
  background-color: #ee3158; }

/*---select2---*/
.theme-danger .select2-container--default.select2-container--open {
  border-color: #ee3158; }
.theme-danger .select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: #ee3158; }
.theme-danger .select2-container--default .select2-search--dropdown .select2-search__field {
  border-color: #ee3158 !important; }
.theme-danger .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #ee3158 !important; }
.theme-danger .select2-container--default .select2-selection--multiple:focus {
  border-color: #ee3158 !important; }
.theme-danger .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #ee3158;
  border-color: #ee3158; }

/*---Other---*/
.theme-danger .myadmin-dd .dd-list .dd-list .dd-handle:hover {
  color: #da123b; }
.theme-danger .myadmin-dd-empty .dd-list .dd3-handle:hover {
  color: #da123b; }
.theme-danger .myadmin-dd-empty .dd-list .dd3-content:hover {
  color: #da123b; }
.theme-danger [data-overlay-primary]::before {
  background: #da123b; }

/*---wizard---*/
.theme-danger .wizard-content .wizard > .steps > ul > li.current {
  border: 2px solid #ee3158;
  background-color: #ee3158; }
.theme-danger .wizard-content .wizard > .steps > ul > li.done {
  border-color: #da123b;
  background-color: #da123b; }
.theme-danger .wizard-content .wizard > .actions > ul > li > a {
  background-color: #ee3158; }
.theme-danger .wizard-content .wizard.wizard-circle > .steps > ul > li:after {
  background-color: #ee3158; }
.theme-danger .wizard-content .wizard.wizard-circle > .steps > ul > li:before {
  background-color: #ee3158; }
.theme-danger .wizard-content .wizard.wizard-notification > .steps > ul > li:after {
  background-color: #ee3158; }
.theme-danger .wizard-content .wizard.wizard-notification > .steps > ul > li:before {
  background-color: #ee3158; }
.theme-danger .wizard-content .wizard.wizard-notification > .steps > ul > li.current .step {
  border: 2px solid #ee3158;
  color: #ee3158; }
  .theme-danger .wizard-content .wizard.wizard-notification > .steps > ul > li.current .step:after {
    border-top-color: #ee3158; }
.theme-danger .wizard-content .wizard.wizard-notification > .steps > ul > li.done .step:after {
  border-top-color: #ee3158; }

@media (max-width: 767px) {
  .theme-danger .wizard-content .wizard > .steps > ul > li:last-child:after {
    background-color: #ee3158; } }
@media (max-width: 575px) {
  .theme-danger .wizard-content .wizard > .steps > ul > li.current:after {
    background-color: #ee3158; } }
/*---slider---*/
.theme-danger #primary .slider-selection {
  background-color: #ee3158; }
.theme-danger #info .slider-selection {
  background-color: #00D0FF; }
.theme-danger #success .slider-selection {
  background-color: #1dbfc1; }
.theme-danger #danger .slider-selection {
  background-color: #3246D3; }
.theme-danger #warning .slider-selection {
  background-color: #ffa800; }

/*---horizontal-timeline---*/
.theme-danger .cd-horizontal-timeline .events a.selected::after {
  background: #ee3158;
  border-color: #ee3158; }
.theme-danger .cd-horizontal-timeline .events a.older-event::after {
  border-color: #ee3158; }
.theme-danger .cd-horizontal-timeline .filling-line {
  background: #ee3158; }
.theme-danger .cd-horizontal-timeline a {
  color: #ee3158; }
  .theme-danger .cd-horizontal-timeline a:hover, .theme-danger .cd-horizontal-timeline a:focus {
    color: #ee3158; }
.theme-danger .cd-timeline-navigation a:hover, .theme-danger .cd-timeline-navigation a:focus {
  border-color: #ee3158; }

/**************************************
Theme Warning Color
**************************************/
.bg-gradient-warning, .theme-warning .bg-gradient-warning, .theme-warning .art-bg {
  background: linear-gradient(45deg, #ffa800, #00D0FF); }

.bg-light-body {
  background: transparent; }

.theme-warning.fixed .main-header {
  background: transparent; }
.theme-warning .main-header {
  background: transparent; }

.theme-warning.onlyheader .art-bg {
  background-image: none; }

.bg-gradient-warning-dark, .dark-skin.theme-warning .bg-gradient-warning, .dark-skin.theme-warning .art-bg {
  background-image: linear-gradient(45deg, #996500, #007d99); }

.bg-dark-body {
  background: #0c1a32; }

.dark-skin.theme-warning.fixed .main-header {
  background: transparent; }
.dark-skin.theme-warning .main-header {
  background: transparent; }

@media (max-width: 767px) {
  .theme-warning.fixed .main-header {
    background-image: #e4e6ef; }
    .theme-warning.fixed .main-header.navbar {
      background: none; }

  .dark-skin.theme-warning.fixed .main-header {
    background-image: #0c1a32; } }
.theme-warning a:hover, .theme-warning a:active, .theme-warning a:focus {
  color: #ffa800; }
.theme-warning .main-sidebar .svg-icon {
  filter: invert(0.6) sepia(1) saturate(1) hue-rotate(185deg); }
  .theme-warning .main-sidebar .svg-icon:hover, .theme-warning .main-sidebar .svg-icon:active, .theme-warning .main-sidebar .svg-icon:focus {
    filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg); }
.theme-warning .main-sidebar a:hover .svg-icon, .theme-warning .main-sidebar a:active .svg-icon, .theme-warning .main-sidebar a:focus .svg-icon {
  filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg); }
.theme-warning .svg-icon {
  filter: invert(0.6) sepia(1) saturate(1) hue-rotate(185deg); }
  .theme-warning .svg-icon:hover, .theme-warning .svg-icon:active, .theme-warning .svg-icon:focus {
    filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg); }
.theme-warning a:hover .svg-icon, .theme-warning a:active .svg-icon, .theme-warning a:focus .svg-icon {
  filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg); }

.theme-warning.light-skin .sidebar-menu > li.active.treeview > a {
  background: transparent;
  color: #b5b5c3 !important; }
  .theme-warning.light-skin .sidebar-menu > li.active.treeview > a > i {
    color: #ffffff; }
  .theme-warning.light-skin .sidebar-menu > li.active.treeview > a > svg {
    color: #ffffff;
    fill: rgba(1, 104, 250, 0.2); }
  .theme-warning.light-skin .sidebar-menu > li.active.treeview > a:after {
    border-color: transparent #fafafa transparent transparent !important; }
.theme-warning.light-skin .sidebar-menu > li.treeview .treeview-menu li a {
  color: #b5b5c3; }
.theme-warning.light-skin.sidebar-mini.sidebar-collapse .sidebar-menu > li.active > a > span {
  background: #ffa800 !important; }
.theme-warning.dark-skin .sidebar-menu > li.active > a:after {
  border-color: transparent #333333 transparent transparent !important; }
.theme-warning.dark-skin .sidebar-menu > li.active.treeview > a {
  background: transparent;
  color: #b5b5c3 !important; }
  .theme-warning.dark-skin .sidebar-menu > li.active.treeview > a > i {
    color: #ffffff; }
  .theme-warning.dark-skin .sidebar-menu > li.active.treeview > a:after {
    border-color: transparent #fafafa transparent transparent !important; }
.theme-warning.dark-skin .sidebar-menu > li.active.treeview .treeview-menu li a {
  color: #b5b5c3; }
.theme-warning.dark-skin.sidebar-mini.sidebar-collapse .sidebar-menu > li.active > a > span {
  background: #ffa800 !important; }
.theme-warning.light-skin .sidebar-menu > li:hover, .theme-warning.light-skin .sidebar-menu > li:active, .theme-warning.light-skin .sidebar-menu > li.active {
  background-color: rgba(255, 168, 0, 0);
  color: white;
  border-left: 5px solid rgba(255, 168, 0, 0); }
  .theme-warning.light-skin .sidebar-menu > li:hover a, .theme-warning.light-skin .sidebar-menu > li:active a, .theme-warning.light-skin .sidebar-menu > li.active a {
    color: white; }
.theme-warning.light-skin .sidebar-menu > li.active {
  background-color: rgba(255, 168, 0, 0);
  color: white;
  border-left: 5px solid #ffa800; }
  .theme-warning.light-skin .sidebar-menu > li.active a {
    color: white;
    background-color: transparent; }
    .theme-warning.light-skin .sidebar-menu > li.active a > i {
      color: #ffffff;
      background-color: rgba(255, 168, 0, 0); }
    .theme-warning.light-skin .sidebar-menu > li.active a > svg {
      color: #ffffff;
      fill: rgba(1, 104, 250, 0.2); }
    .theme-warning.light-skin .sidebar-menu > li.active a img.svg-icon {
      filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg); }
  .theme-warning.light-skin .sidebar-menu > li.active .treeview-menu li.active {
    background-color: rgba(255, 168, 0, 0);
    color: white; }
    .theme-warning.light-skin .sidebar-menu > li.active .treeview-menu li.active a {
      color: white; }
      .theme-warning.light-skin .sidebar-menu > li.active .treeview-menu li.active a > i {
        color: white;
        background-color: rgba(255, 168, 0, 0); }
  .theme-warning.light-skin .sidebar-menu > li.active .treeview-menu li a > i {
    color: #b5b5c3;
    background-color: rgba(255, 168, 0, 0); }
  .theme-warning.light-skin .sidebar-menu > li.active .treeview-menu li.treeview.active {
    background-color: rgba(255, 168, 0, 0);
    color: white; }
    .theme-warning.light-skin .sidebar-menu > li.active .treeview-menu li.treeview.active a {
      color: white; }
      .theme-warning.light-skin .sidebar-menu > li.active .treeview-menu li.treeview.active a > i {
        color: white;
        background-color: rgba(255, 168, 0, 0); }
  .theme-warning.light-skin .sidebar-menu > li.active .treeview-menu li.treeview .treeview-menu li.active {
    background-color: rgba(255, 168, 0, 0);
    color: white; }
    .theme-warning.light-skin .sidebar-menu > li.active .treeview-menu li.treeview .treeview-menu li.active a {
      color: white; }
      .theme-warning.light-skin .sidebar-menu > li.active .treeview-menu li.treeview .treeview-menu li.active a > i {
        color: white;
        background-color: rgba(255, 168, 0, 0); }
  .theme-warning.light-skin .sidebar-menu > li.active .treeview-menu li.treeview .treeview-menu li a {
    color: #b5b5c3; }
    .theme-warning.light-skin .sidebar-menu > li.active .treeview-menu li.treeview .treeview-menu li a > i {
      color: #b5b5c3;
      background-color: rgba(255, 168, 0, 0); }
.theme-warning.rtl.light-skin .sidebar-menu > li:hover, .theme-warning.rtl.light-skin .sidebar-menu > li:active, .theme-warning.rtl.light-skin .sidebar-menu > li.active {
  border-left: 0px solid rgba(255, 168, 0, 0);
  border-right: 5px solid rgba(255, 168, 0, 0); }
.theme-warning.rtl.light-skin .sidebar-menu > li.active {
  border-left: 0px solid #ffa800;
  border-right: 5px solid #ffa800; }
.theme-warning.dark-skin .sidebar-menu > li.active {
  background-color: rgba(255, 168, 0, 0);
  color: white;
  border-left: 5px solid #ffa800; }
  .theme-warning.dark-skin .sidebar-menu > li.active a {
    color: white;
    background-color: transparent; }
    .theme-warning.dark-skin .sidebar-menu > li.active a > i {
      color: white; }
    .theme-warning.dark-skin .sidebar-menu > li.active a > svg {
      color: #ffffff;
      fill: rgba(1, 104, 250, 0.2); }
    .theme-warning.dark-skin .sidebar-menu > li.active a img.svg-icon {
      filter: invert(0.7) sepia(1) saturate(14) hue-rotate(195deg); }
  .theme-warning.dark-skin .sidebar-menu > li.active .treeview-menu li.active {
    background-color: rgba(255, 168, 0, 0);
    color: white; }
    .theme-warning.dark-skin .sidebar-menu > li.active .treeview-menu li.active a {
      color: white !important; }
.theme-warning.rtl.dark-skin .sidebar-menu > li.active {
  border-left: 0px solid #ffa800;
  border-right: 5px solid #ffa800; }

@media (min-width: 768px) {
  .sidebar-mini.sidebar-collapse .sidebar-menu > li.active.menu-open {
    background-color: rgba(255, 168, 0, 0.2);
    color: #ffa800; } }
/*---Main Nav---*/
.theme-warning .sm-blue li.current > a, .theme-warning .sm-blue li.highlighted > a {
  background: #ffa800;
  color: #ffffff !important; }
  .theme-warning .sm-blue li.current > a:hover, .theme-warning .sm-blue li.current > a:active, .theme-warning .sm-blue li.current > a:focus, .theme-warning .sm-blue li.highlighted > a:hover, .theme-warning .sm-blue li.highlighted > a:active, .theme-warning .sm-blue li.highlighted > a:focus {
    background: #ffa800;
    color: #ffffff !important; }
.theme-warning .sm-blue a.current, .theme-warning .sm-blue a.highlighted {
  background: #ffa800;
  color: #ffffff !important; }
.theme-warning .sm-blue a:hover, .theme-warning .sm-blue a:active, .theme-warning .sm-blue a:focus {
  background: #ffa800;
  color: #ffffff !important; }
.theme-warning .sm-blue ul a:hover, .theme-warning .sm-blue ul a:active, .theme-warning .sm-blue ul a:focus {
  background: #ebedf3;
  color: #ffa800 !important; }
.theme-warning .sm-blue ul a.highlighted {
  background: #ebedf3;
  color: #ffa800 !important; }

.dark-skin.theme-warning .sm-blue a.current, .dark-skin.theme-warning .sm-blue a.highlighted {
  background: #ffa800;
  color: #ffffff !important; }
.dark-skin.theme-warning .sm-blue a:hover, .dark-skin.theme-warning .sm-blue a:active, .dark-skin.theme-warning .sm-blue a:focus {
  background: #ffa800;
  color: #ffffff !important; }
.dark-skin.theme-warning .sm-blue ul a:hover, .dark-skin.theme-warning .sm-blue ul a:active, .dark-skin.theme-warning .sm-blue ul a:focus {
  background: #29354b;
  color: #ffa800 !important; }
.dark-skin.theme-warning .sm-blue ul a.highlighted {
  background: #29354b;
  color: #ffa800 !important; }

/*---Primary Button---*/
.theme-warning .btn-link {
  color: #ffa800; }
.theme-warning .btn-primary {
  background-color: #ffa800;
  border-color: #ffa800;
  color: #ffffff; }
  .theme-warning .btn-primary:hover, .theme-warning .btn-primary:active, .theme-warning .btn-primary:focus, .theme-warning .btn-primary.active {
    background-color: #cc8600 !important;
    border-color: #cc8600 !important;
    color: #ffffff !important; }
  .theme-warning .btn-primary:disabled {
    background-color: #ffcb66;
    border-color: #ffa800;
    opacity: 0.5; }
  .theme-warning .btn-primary.disabled {
    background-color: #ffcb66;
    border-color: #ffa800;
    opacity: 0.5; }
.theme-warning .show > .btn-primary.dropdown-toggle {
  background-color: #cc8600 !important;
  border-color: #cc8600 !important;
  color: #ffffff; }
.theme-warning .btn-outline.btn-primary {
  color: #ffa800;
  background-color: transparent;
  border-color: #ffa800 !important; }
  .theme-warning .btn-outline.btn-primary:hover, .theme-warning .btn-outline.btn-primary:active, .theme-warning .btn-outline.btn-primary.active {
    background-color: #cc8600 !important;
    border-color: #cc8600 !important;
    color: #ffffff !important; }
.theme-warning .show > .btn-outline.btn-primary.dropdown-toggle {
  background-color: #cc8600 !important;
  border-color: #cc8600 !important;
  color: #ffffff; }
.theme-warning .btn-flat.btn-primary {
  color: #ffa800 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-warning .btn-flat.btn-primary:hover, .theme-warning .btn-flat.btn-primary:active, .theme-warning .btn-flat.btn-primary.active {
    background-color: #cc8600 !important;
    border-color: #cc8600 !important;
    color: #ffffff !important; }

/*---info Button---*/
.theme-warning .btn-info {
  background-color: #00D0FF;
  border-color: #00D0FF;
  color: #ffffff; }
  .theme-warning .btn-info:hover, .theme-warning .btn-info:active, .theme-warning .btn-info:focus, .theme-warning .btn-info.active {
    background-color: #00a6cc !important;
    border-color: #00a6cc !important;
    color: #ffffff !important; }
  .theme-warning .btn-info:disabled {
    background-color: #66e3ff;
    border-color: #00D0FF;
    opacity: 0.5; }
  .theme-warning .btn-info.disabled {
    background-color: #66e3ff;
    border-color: #00D0FF;
    opacity: 0.5; }
.theme-warning .show > .btn-info.dropdown-toggle {
  background-color: #00a6cc !important;
  border-color: #00a6cc !important;
  color: #ffffff; }
.theme-warning .btn-outline.btn-info {
  color: #00D0FF;
  background-color: transparent;
  border-color: #00D0FF !important; }
  .theme-warning .btn-outline.btn-info:hover, .theme-warning .btn-outline.btn-info:active, .theme-warning .btn-outline.btn-info.active {
    background-color: #00a6cc !important;
    border-color: #00a6cc !important;
    color: #ffffff !important; }
.theme-warning .show > .btn-outline.btn-info.dropdown-toggle {
  background-color: #00a6cc !important;
  border-color: #00a6cc !important;
  color: #ffffff; }
.theme-warning .btn-flat.btn-info {
  color: #00D0FF !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-warning .btn-flat.btn-info:hover, .theme-warning .btn-flat.btn-info:active, .theme-warning .btn-flat.btn-info.active {
    background-color: #00a6cc !important;
    border-color: #00a6cc !important;
    color: #ffffff !important; }

/*---Success Button---*/
.theme-warning .btn-success {
  background-color: #1dbfc1;
  border-color: #1dbfc1;
  color: #ffffff; }
  .theme-warning .btn-success:hover, .theme-warning .btn-success:active, .theme-warning .btn-success:focus, .theme-warning .btn-success.active {
    background-color: #169395 !important;
    border-color: #169395 !important;
    color: #ffffff !important; }
  .theme-warning .btn-success:disabled {
    background-color: #5de5e7;
    border-color: #1dbfc1;
    opacity: 0.5; }
  .theme-warning .btn-success.disabled {
    background-color: #5de5e7;
    border-color: #1dbfc1;
    opacity: 0.5; }
.theme-warning .show > .btn-success.dropdown-toggle {
  background-color: #169395 !important;
  border-color: #169395 !important;
  color: #ffffff; }
.theme-warning .btn-outline.btn-success {
  color: #1dbfc1;
  background-color: transparent;
  border-color: #1dbfc1 !important; }
  .theme-warning .btn-outline.btn-success:hover, .theme-warning .btn-outline.btn-success:active, .theme-warning .btn-outline.btn-success.active {
    background-color: #169395 !important;
    border-color: #169395 !important;
    color: #ffffff !important; }
.theme-warning .show > .btn-outline.btn-success.dropdown-toggle {
  background-color: #169395 !important;
  border-color: #169395 !important;
  color: #ffffff; }
.theme-warning .btn-flat.btn-success {
  color: #1dbfc1 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-warning .btn-flat.btn-success:hover, .theme-warning .btn-flat.btn-success:active, .theme-warning .btn-flat.btn-success.active {
    background-color: #169395 !important;
    border-color: #169395 !important;
    color: #ffffff !important; }

/*---Danger Button---*/
.theme-warning .btn-danger {
  background-color: #ee3158;
  border-color: #ee3158;
  color: #ffffff; }
  .theme-warning .btn-danger:hover, .theme-warning .btn-danger:active, .theme-warning .btn-danger:focus, .theme-warning .btn-danger.active {
    background-color: #da123b !important;
    border-color: #da123b !important;
    color: #ffffff !important; }
  .theme-warning .btn-danger:disabled {
    background-color: #f68fa4;
    border-color: #ee3158;
    opacity: 0.5; }
  .theme-warning .btn-danger.disabled {
    background-color: #f68fa4;
    border-color: #ee3158;
    opacity: 0.5; }
.theme-warning .show > .btn-danger.dropdown-toggle {
  background-color: #da123b !important;
  border-color: #da123b !important;
  color: #ffffff; }
.theme-warning .btn-outline.btn-danger {
  color: #ee3158;
  background-color: transparent;
  border-color: #ee3158 !important; }
  .theme-warning .btn-outline.btn-danger:hover, .theme-warning .btn-outline.btn-danger:active, .theme-warning .btn-outline.btn-danger.active {
    background-color: #da123b !important;
    border-color: #da123b !important;
    color: #ffffff !important; }
.theme-warning .show > .btn-outline.btn-danger.dropdown-toggle {
  background-color: #da123b !important;
  border-color: #da123b !important;
  color: #ffffff; }
.theme-warning .btn-flat.btn-danger {
  color: #ee3158 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-warning .btn-flat.btn-danger:hover, .theme-warning .btn-flat.btn-danger:active, .theme-warning .btn-flat.btn-danger.active {
    background-color: #da123b !important;
    border-color: #da123b !important;
    color: #ffffff !important; }

/*---Warning Button---*/
.theme-warning .btn-warning {
  background-color: #3246D3;
  border-color: #3246D3;
  color: #ffffff; }
  .theme-warning .btn-warning:hover, .theme-warning .btn-warning:active, .theme-warning .btn-warning:focus, .theme-warning .btn-warning.active {
    background-color: #2536ad !important;
    border-color: #2536ad !important;
    color: #ffffff !important; }
  .theme-warning .btn-warning:disabled {
    background-color: #8692e5;
    border-color: #3246D3;
    opacity: 0.5; }
  .theme-warning .btn-warning.disabled {
    background-color: #8692e5;
    border-color: #3246D3;
    opacity: 0.5; }
.theme-warning .show > .btn-warning.dropdown-toggle {
  background-color: #2536ad !important;
  border-color: #2536ad !important;
  color: #ffffff; }
.theme-warning .btn-outline.btn-warning {
  color: #3246D3;
  background-color: transparent;
  border-color: #3246D3 !important; }
  .theme-warning .btn-outline.btn-warning:hover, .theme-warning .btn-outline.btn-warning:active, .theme-warning .btn-outline.btn-warning.active {
    background-color: #2536ad !important;
    border-color: #2536ad !important;
    color: #ffffff !important; }
.theme-warning .show > .btn-outline.btn-warning.dropdown-toggle {
  background-color: #2536ad !important;
  border-color: #2536ad !important;
  color: #ffffff; }
.theme-warning .btn-flat.btn-warning {
  color: #3246D3 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-warning .btn-flat.btn-warning:hover, .theme-warning .btn-flat.btn-warning:active, .theme-warning .btn-flat.btn-warning.active {
    background-color: #2536ad !important;
    border-color: #2536ad !important;
    color: #ffffff !important; }

/*---Primary Button light---*/
.theme-warning .btn-primary-light {
  background-color: #fff8ea;
  border-color: #fff8ea;
  color: #ffa800; }
  .theme-warning .btn-primary-light:hover, .theme-warning .btn-primary-light:active, .theme-warning .btn-primary-light:focus, .theme-warning .btn-primary-light.active {
    background-color: #ffa800 !important;
    border-color: #ffa800 !important;
    color: #ffffff !important; }
  .theme-warning .btn-primary-light:disabled {
    background-color: white;
    border-color: #fff8ea;
    opacity: 0.5; }
  .theme-warning .btn-primary-light.disabled {
    background-color: white;
    border-color: #fff8ea;
    opacity: 0.5; }
.theme-warning .show > .btn-primary-light.dropdown-toggle {
  background-color: #ffa800 !important;
  border-color: #ffa800 !important;
  color: #ffffff; }
.theme-warning .btn-outline.btn-primary-light {
  color: #ffa800;
  background-color: transparent;
  border-color: #fff8ea !important; }
  .theme-warning .btn-outline.btn-primary-light:hover, .theme-warning .btn-outline.btn-primary-light:active, .theme-warning .btn-outline.btn-primary-light.active {
    background-color: #ffa800 !important;
    border-color: #ffa800 !important;
    color: #ffffff !important; }
.theme-warning .show > .btn-outline.btn-primary-light.dropdown-toggle {
  background-color: #ffa800 !important;
  border-color: #ffa800 !important;
  color: #ffffff; }
.theme-warning .btn-flat.btn-primary-light {
  color: #ffa800 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-warning .btn-flat.btn-primary-light:hover, .theme-warning .btn-flat.btn-primary-light:active, .theme-warning .btn-flat.btn-primary-light.active {
    background-color: #ffa800 !important;
    border-color: #ffa800 !important;
    color: #ffffff !important; }

/*---info Button light---*/
.theme-warning .btn-info-light {
  background-color: #e1f9ff;
  border-color: #e1f9ff;
  color: #00D0FF; }
  .theme-warning .btn-info-light:hover, .theme-warning .btn-info-light:active, .theme-warning .btn-info-light:focus, .theme-warning .btn-info-light.active {
    background-color: #00D0FF !important;
    border-color: #00D0FF !important;
    color: #ffffff !important; }
  .theme-warning .btn-info-light:disabled {
    background-color: white;
    border-color: #e1f9ff;
    opacity: 0.5; }
  .theme-warning .btn-info-light.disabled {
    background-color: white;
    border-color: #e1f9ff;
    opacity: 0.5; }
.theme-warning .show > .btn-info.dropdown-toggle {
  background-color: #00D0FF !important;
  border-color: #00D0FF !important;
  color: #ffffff; }
.theme-warning .btn-outline.btn-info-light {
  color: #00D0FF;
  background-color: transparent;
  border-color: #e1f9ff !important; }
  .theme-warning .btn-outline.btn-info-light:hover, .theme-warning .btn-outline.btn-info-light:active, .theme-warning .btn-outline.btn-info-light.active {
    background-color: #00D0FF !important;
    border-color: #00D0FF !important;
    color: #ffffff !important; }
.theme-warning .show > .btn-outline.btn-info-light.dropdown-toggle {
  background-color: #00D0FF !important;
  border-color: #00D0FF !important;
  color: #ffffff; }
.theme-warning .btn-flat.btn-info-light {
  color: #00D0FF !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-warning .btn-flat.btn-info-light:hover, .theme-warning .btn-flat.btn-info-light:active, .theme-warning .btn-flat.btn-info-light.active {
    background-color: #00D0FF !important;
    border-color: #00D0FF !important;
    color: #ffffff !important; }

/*---Success Button light---*/
.theme-warning .btn-success-light {
  background-color: #e8f9f9;
  border-color: #e8f9f9;
  color: #1dbfc1; }
  .theme-warning .btn-success-light:hover, .theme-warning .btn-success-light:active, .theme-warning .btn-success-light:focus, .theme-warning .btn-success-light.active {
    background-color: #1dbfc1 !important;
    border-color: #1dbfc1 !important;
    color: #ffffff !important; }
  .theme-warning .btn-success-light:disabled {
    background-color: white;
    border-color: #e8f9f9;
    opacity: 0.5; }
  .theme-warning .btn-success-light.disabled {
    background-color: white;
    border-color: #e8f9f9;
    opacity: 0.5; }
.theme-warning .show > .btn-success-light.dropdown-toggle {
  background-color: #1dbfc1 !important;
  border-color: #1dbfc1 !important;
  color: #ffffff; }
.theme-warning .btn-outline.btn-success-light {
  color: #1dbfc1;
  background-color: transparent;
  border-color: #e8f9f9 !important; }
  .theme-warning .btn-outline.btn-success-light:hover, .theme-warning .btn-outline.btn-success-light:active, .theme-warning .btn-outline.btn-success-light.active {
    background-color: #1dbfc1 !important;
    border-color: #1dbfc1 !important;
    color: #ffffff !important; }
.theme-warning .show > .btn-outline.btn-success-light.dropdown-toggle {
  background-color: #1dbfc1 !important;
  border-color: #1dbfc1 !important;
  color: #ffffff; }
.theme-warning .btn-flat.btn-success-light {
  color: #1dbfc1 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-warning .btn-flat.btn-success-light:hover, .theme-warning .btn-flat.btn-success-light:active, .theme-warning .btn-flat.btn-success-light.active {
    background-color: #1dbfc1 !important;
    border-color: #1dbfc1 !important;
    color: #ffffff !important; }

/*---Danger Button light---*/
.theme-warning .btn-danger-light {
  background-color: #ffd6de;
  border-color: #ffd6de;
  color: #ee3158; }
  .theme-warning .btn-danger-light:hover, .theme-warning .btn-danger-light:active, .theme-warning .btn-danger-light:focus, .theme-warning .btn-danger-light.active {
    background-color: #ee3158 !important;
    border-color: #ee3158 !important;
    color: #ffffff !important; }
  .theme-warning .btn-danger-light:disabled {
    background-color: white;
    border-color: #ffd6de;
    opacity: 0.5; }
  .theme-warning .btn-danger-light.disabled {
    background-color: white;
    border-color: #ffd6de;
    opacity: 0.5; }
.theme-warning .show > .btn-danger-light.dropdown-toggle {
  background-color: #ee3158 !important;
  border-color: #ee3158 !important;
  color: #ffffff; }
.theme-warning .btn-outline.btn-danger-light {
  color: #ee3158;
  background-color: transparent;
  border-color: #ffd6de !important; }
  .theme-warning .btn-outline.btn-danger-light:hover, .theme-warning .btn-outline.btn-danger-light:active, .theme-warning .btn-outline.btn-danger-light.active {
    background-color: #ee3158 !important;
    border-color: #ee3158 !important;
    color: #ffffff !important; }
.theme-warning .show > .btn-outline.btn-danger-light.dropdown-toggle {
  background-color: #ee3158 !important;
  border-color: #ee3158 !important;
  color: #ffffff; }
.theme-warning .btn-flat.btn-danger-light {
  color: #ee3158 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-warning .btn-flat.btn-danger-light:hover, .theme-warning .btn-flat.btn-danger-light:active, .theme-warning .btn-flat.btn-danger-light.active {
    background-color: #ee3158 !important;
    border-color: #ee3158 !important;
    color: #ffffff !important; }

/*---Warning Button light---*/
.theme-warning .btn-warning-light {
  background-color: #dbdfff;
  border-color: #dbdfff;
  color: #3246D3; }
  .theme-warning .btn-warning-light:hover, .theme-warning .btn-warning-light:active, .theme-warning .btn-warning-light:focus, .theme-warning .btn-warning-light.active {
    background-color: #3246D3 !important;
    border-color: #3246D3 !important;
    color: #ffffff !important; }
  .theme-warning .btn-warning-light:disabled {
    background-color: white;
    border-color: #dbdfff;
    opacity: 0.5; }
  .theme-warning .btn-warning-light.disabled {
    background-color: white;
    border-color: #dbdfff;
    opacity: 0.5; }
.theme-warning .show > .btn-warning-light.dropdown-toggle {
  background-color: #3246D3 !important;
  border-color: #3246D3 !important;
  color: #ffffff; }
.theme-warning .btn-outline.btn-warning-light {
  color: #3246D3;
  background-color: transparent;
  border-color: #dbdfff !important; }
  .theme-warning .btn-outline.btn-warning-light:hover, .theme-warning .btn-outline.btn-warning-light:active, .theme-warning .btn-outline.btn-warning-light.active {
    background-color: #3246D3 !important;
    border-color: #3246D3 !important;
    color: #ffffff !important; }
.theme-warning .show > .btn-outline.btn-warning-light.dropdown-toggle {
  background-color: #3246D3 !important;
  border-color: #3246D3 !important;
  color: #ffffff; }
.theme-warning .btn-flat.btn-warning-light {
  color: #3246D3 !important;
  background-color: #e4e6ef;
  border-color: transparent; }
  .theme-warning .btn-flat.btn-warning-light:hover, .theme-warning .btn-flat.btn-warning-light:active, .theme-warning .btn-flat.btn-warning-light.active {
    background-color: #3246D3 !important;
    border-color: #3246D3 !important;
    color: #ffffff !important; }

/*---callout---*/
.theme-warning .callout.callout-primary {
  border-color: #ffa800;
  background-color: #ffa800 !important; }
.theme-warning .callout.callout-info {
  border-color: #00D0FF;
  background-color: #00D0FF !important; }
.theme-warning .callout.callout-success {
  border-color: #1dbfc1;
  background-color: #1dbfc1 !important; }
.theme-warning .callout.callout-danger {
  border-color: #ee3158;
  background-color: #ee3158 !important; }
.theme-warning .callout.callout-warning {
  border-color: #3246D3;
  background-color: #3246D3 !important; }

/*---alert---*/
.theme-warning .alert-primary {
  border-color: #ffa800;
  background-color: #ffa800 !important;
  color: #ffffff; }
.theme-warning .alert-info {
  border-color: #00D0FF;
  background-color: #00D0FF !important;
  color: #ffffff; }
.theme-warning .alert-success {
  border-color: #1dbfc1;
  background-color: #1dbfc1 !important;
  color: #ffffff; }
.theme-warning .alert-danger {
  border-color: #ee3158;
  background-color: #ee3158 !important;
  color: #ffffff; }
.theme-warning .alert-error {
  border-color: #ee3158;
  background-color: #ee3158 !important;
  color: #ffffff; }
.theme-warning .alert-warning {
  border-color: #3246D3;
  background-color: #3246D3 !important;
  color: #ffffff; }

/*---direct-chat---*/
.theme-warning .direct-chat-primary .right > .direct-chat-text p {
  background-color: #ffa800;
  color: #ffffff; }
.theme-warning .direct-chat-primary .right > .direct-chat-text:before, .theme-warning .direct-chat-primary .right > .direct-chat-text:after {
  border-left-color: #ffa800; }
.theme-warning .direct-chat-info .right > .direct-chat-text p {
  background-color: #00D0FF;
  color: #ffffff; }
.theme-warning .direct-chat-info .right > .direct-chat-text:before, .theme-warning .direct-chat-info .right > .direct-chat-text:after {
  border-left-color: #00D0FF; }
.theme-warning .direct-chat-success .right > .direct-chat-text p {
  background-color: #1dbfc1;
  color: #ffffff; }
.theme-warning .direct-chat-success .right > .direct-chat-text:before, .theme-warning .direct-chat-success .right > .direct-chat-text:after {
  border-left-color: #1dbfc1; }
.theme-warning .direct-chat-danger .right > .direct-chat-text p {
  background-color: #ee3158;
  color: #ffffff; }
.theme-warning .direct-chat-danger .right > .direct-chat-text:before, .theme-warning .direct-chat-danger .right > .direct-chat-text:after {
  border-left-color: #ee3158; }
.theme-warning .direct-chat-warning .right > .direct-chat-text p {
  background-color: #3246D3;
  color: #ffffff; }
.theme-warning .direct-chat-warning .right > .direct-chat-text:before, .theme-warning .direct-chat-warning .right > .direct-chat-text:after {
  border-left-color: #3246D3; }
.theme-warning .right .direct-chat-text p {
  background-color: #ffa800; }

/*---modal---*/
.theme-warning .modal-primary .modal-footer, .theme-warning .modal-primary .modal-header {
  border-color: #ffa800; }
.theme-warning .modal-primary .modal-body {
  background-color: #ffa800 !important; }
.theme-warning .modal-info .modal-footer, .theme-warning .modal-info .modal-header {
  border-color: #00D0FF; }
.theme-warning .modal-info .modal-body {
  background-color: #00D0FF !important; }
.theme-warning .modal-success .modal-footer, .theme-warning .modal-success .modal-header {
  border-color: #1dbfc1; }
.theme-warning .modal-success .modal-body {
  background-color: #1dbfc1 !important; }
.theme-warning .modal-danger .modal-footer, .theme-warning .modal-danger .modal-header {
  border-color: #ee3158; }
.theme-warning .modal-danger .modal-body {
  background-color: #ee3158 !important; }
.theme-warning .modal-warning .modal-footer, .theme-warning .modal-warning .modal-header {
  border-color: #3246D3; }
.theme-warning .modal-warning .modal-body {
  background-color: #3246D3 !important; }

/*---border---*/
.theme-warning .border-primary {
  border-color: #ffa800 !important; }
.theme-warning .border-info {
  border-color: #00D0FF !important; }
.theme-warning .border-success {
  border-color: #1dbfc1 !important; }
.theme-warning .border-danger {
  border-color: #ee3158 !important; }
.theme-warning .border-warning {
  border-color: #3246D3 !important; }

/*---Background---*/
.theme-warning .bg-primary {
  background-color: #ffa800 !important;
  color: #ffffff; }
.theme-warning .bg-primary-light {
  background-color: #fff8ea !important;
  color: #ffa800; }
.theme-warning .bg-info {
  background-color: #00D0FF !important;
  color: #ffffff; }
.theme-warning .bg-info-light {
  background-color: #e1f9ff !important;
  color: #00D0FF; }
.theme-warning .bg-success {
  background-color: #1dbfc1 !important;
  color: #ffffff; }
.theme-warning .bg-success-light {
  background-color: #e8f9f9 !important;
  color: #1dbfc1; }
.theme-warning .bg-danger {
  background-color: #ee3158 !important;
  color: #ffffff; }
.theme-warning .bg-danger-light {
  background-color: #ffd6de !important;
  color: #ee3158; }
.theme-warning .bg-warning {
  background-color: #3246D3 !important;
  color: #ffffff; }
.theme-warning .bg-warning-light {
  background-color: #dbdfff !important;
  color: #3246D3; }

/*---text---*/
.theme-warning .text-primary {
  color: #ffa800 !important; }
.theme-warning a.text-primary:hover, .theme-warning a.text-primary:focus {
  color: #ffa800 !important; }
.theme-warning .hover-primary:hover, .theme-warning .hover-primary:focus {
  color: #ffa800 !important; }
.theme-warning .text-info {
  color: #00D0FF !important; }
.theme-warning a.text-info:hover, .theme-warning a.text-info:focus {
  color: #00D0FF !important; }
.theme-warning .hover-info:hover, .theme-warning .hover-info:focus {
  color: #00D0FF !important; }
.theme-warning .text-success {
  color: #1dbfc1 !important; }
.theme-warning a.text-success:hover, .theme-warning a.text-success:focus {
  color: #1dbfc1 !important; }
.theme-warning .hover-success:hover, .theme-warning .hover-success:focus {
  color: #1dbfc1 !important; }
.theme-warning .text-danger {
  color: #ee3158 !important; }
.theme-warning a.text-danger:hover, .theme-warning a.text-danger:focus {
  color: #ee3158 !important; }
.theme-warning .hover-danger:hover, .theme-warning .hover-danger:focus {
  color: #ee3158 !important; }
.theme-warning .text-warning {
  color: #3246D3 !important; }
.theme-warning a.text-warning:hover, .theme-warning a.text-warning:focus {
  color: #3246D3 !important; }
.theme-warning .hover-warning:hover, .theme-warning .hover-warning:focus {
  color: #3246D3 !important; }

/*---active background---*/
.theme-warning .active.active-primary {
  background-color: #cc8600 !important; }
.theme-warning .active.active-info {
  background-color: #00a6cc !important; }
.theme-warning .active.active-success {
  background-color: #169395 !important; }
.theme-warning .active.active-danger {
  background-color: #da123b !important; }
.theme-warning .active.active-warning {
  background-color: #2536ad !important; }

/*---label background---*/
.theme-warning .label-primary {
  background-color: #ffa800 !important; }
.theme-warning .label-info {
  background-color: #00D0FF !important; }
.theme-warning .label-success {
  background-color: #1dbfc1 !important; }
.theme-warning .label-danger {
  background-color: #ee3158 !important; }
.theme-warning .label-warning {
  background-color: #3246D3 !important; }

/*---ribbon---*/
.theme-warning .ribbon-box .ribbon-primary {
  background-color: #ffa800; }
  .theme-warning .ribbon-box .ribbon-primary:before {
    border-color: #ffa800 transparent transparent; }
.theme-warning .ribbon-box .ribbon-two-primary span {
  background-color: #ffa800; }
  .theme-warning .ribbon-box .ribbon-two-primary span:before {
    border-left: 3px solid #cc8600;
    border-top: 3px solid #cc8600; }
  .theme-warning .ribbon-box .ribbon-two-primary span:after {
    border-right: 3px solid #cc8600;
    border-top: 3px solid #cc8600; }
.theme-warning .ribbon-box .ribbon-info {
  background-color: #00D0FF; }
  .theme-warning .ribbon-box .ribbon-info:before {
    border-color: #00D0FF transparent transparent; }
.theme-warning .ribbon-box .ribbon-two-info span {
  background-color: #00D0FF; }
  .theme-warning .ribbon-box .ribbon-two-info span:before {
    border-left: 3px solid #00a6cc;
    border-top: 3px solid #00a6cc; }
  .theme-warning .ribbon-box .ribbon-two-info span:after {
    border-right: 3px solid #00a6cc;
    border-top: 3px solid #00a6cc; }
.theme-warning .ribbon-box .ribbon-success {
  background-color: #1dbfc1; }
  .theme-warning .ribbon-box .ribbon-success:before {
    border-color: #1dbfc1 transparent transparent; }
.theme-warning .ribbon-box .ribbon-two-success span {
  background-color: #1dbfc1; }
  .theme-warning .ribbon-box .ribbon-two-success span:before {
    border-left: 3px solid #169395;
    border-top: 3px solid #169395; }
  .theme-warning .ribbon-box .ribbon-two-success span:after {
    border-right: 3px solid #169395;
    border-top: 3px solid #169395; }
.theme-warning .ribbon-box .ribbon-danger {
  background-color: #ee3158; }
  .theme-warning .ribbon-box .ribbon-danger:before {
    border-color: #ee3158 transparent transparent; }
.theme-warning .ribbon-box .ribbon-two-danger span {
  background-color: #ee3158; }
  .theme-warning .ribbon-box .ribbon-two-danger span:before {
    border-left: 3px solid #da123b;
    border-top: 3px solid #da123b; }
  .theme-warning .ribbon-box .ribbon-two-danger span:after {
    border-right: 3px solid #da123b;
    border-top: 3px solid #da123b; }
.theme-warning .ribbon-box .ribbon-warning {
  background-color: #3246D3; }
  .theme-warning .ribbon-box .ribbon-warning:before {
    border-color: #3246D3 transparent transparent; }
.theme-warning .ribbon-box .ribbon-two-warning span {
  background-color: #3246D3; }
  .theme-warning .ribbon-box .ribbon-two-warning span:before {
    border-left: 3px solid #2536ad;
    border-top: 3px solid #2536ad; }
  .theme-warning .ribbon-box .ribbon-two-warning span:after {
    border-right: 3px solid #2536ad;
    border-top: 3px solid #2536ad; }

/*---Box---*/
.theme-warning .box-primary {
  background-color: #ffa800 !important; }
  .theme-warning .box-primary.box-bordered {
    border-color: #ffa800; }
.theme-warning .box-outline-primary {
  background-color: #ffffff;
  border: 1px solid #ffa800; }
.theme-warning .box.box-solid.box-primary > .box-header {
  color: #ffffff;
  background-color: #ffa800; }
  .theme-warning .box.box-solid.box-primary > .box-header .btn {
    color: #ffffff; }
  .theme-warning .box.box-solid.box-primary > .box-header > a {
    color: #ffffff; }
.theme-warning .box-info {
  background-color: #00D0FF !important; }
  .theme-warning .box-info.box-bordered {
    border-color: #00D0FF; }
.theme-warning .box-outline-info {
  background-color: #ffffff;
  border: 1px solid #00D0FF; }
.theme-warning .box.box-solid.box-info > .box-header {
  color: #ffffff;
  background-color: #00D0FF; }
  .theme-warning .box.box-solid.box-info > .box-header .btn {
    color: #ffffff; }
  .theme-warning .box.box-solid.box-info > .box-header > a {
    color: #ffffff; }
.theme-warning .box-success {
  background-color: #1dbfc1 !important; }
  .theme-warning .box-success.box-bordered {
    border-color: #1dbfc1; }
.theme-warning .box-outline-success {
  background-color: #ffffff;
  border: 1px solid #1dbfc1; }
.theme-warning .box.box-solid.box-success > .box-header {
  color: #ffffff;
  background-color: #1dbfc1; }
  .theme-warning .box.box-solid.box-success > .box-header .btn {
    color: #ffffff; }
  .theme-warning .box.box-solid.box-success > .box-header > a {
    color: #ffffff; }
.theme-warning .box-danger {
  background-color: #ee3158 !important; }
  .theme-warning .box-danger.box-bordered {
    border-color: #ee3158; }
.theme-warning .box-outline-danger {
  background-color: #ffffff;
  border: 1px solid #ee3158; }
.theme-warning .box.box-solid.box-danger > .box-header {
  color: #ffffff;
  background-color: #ee3158; }
  .theme-warning .box.box-solid.box-danger > .box-header .btn {
    color: #ffffff; }
  .theme-warning .box.box-solid.box-danger > .box-header > a {
    color: #ffffff; }
.theme-warning .box-warning {
  background-color: #3246D3 !important; }
  .theme-warning .box-warning.box-bordered {
    border-color: #3246D3; }
.theme-warning .box-outline-warning {
  background-color: #ffffff;
  border: 1px solid #3246D3; }
.theme-warning .box.box-solid.box-warning > .box-header {
  color: #ffffff;
  background-color: #3246D3; }
  .theme-warning .box.box-solid.box-warning > .box-header .btn {
    color: #ffffff; }
  .theme-warning .box.box-solid.box-warning > .box-header > a {
    color: #ffffff; }
.theme-warning .box-profile .social-states a:hover {
  color: #cc8600; }
.theme-warning .box-controls li > a:hover {
  color: #cc8600; }
.theme-warning .box-controls .dropdown.show > a {
  color: #cc8600; }
.theme-warning .box-fullscreen .box-btn-fullscreen {
  color: #cc8600; }

/*---progress bar---*/
.theme-warning .progress-bar-primary {
  background-color: #ffa800; }
.theme-warning .progress-bar-info {
  background-color: #00D0FF; }
.theme-warning .progress-bar-success {
  background-color: #1dbfc1; }
.theme-warning .progress-bar-danger {
  background-color: #ee3158; }
.theme-warning .progress-bar-warning {
  background-color: #3246D3; }

/*---panel---*/
.theme-warning .panel-primary {
  border-color: #ffa800; }
  .theme-warning .panel-primary > .panel-heading {
    color: #ffffff;
    background-color: #ffa800;
    border-color: #ffa800; }
    .theme-warning .panel-primary > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #ffa800; }
    .theme-warning .panel-primary > .panel-heading .badge-pill {
      color: #ffa800;
      background-color: #ffffff; }
  .theme-warning .panel-primary .panel-title, .theme-warning .panel-primary .panel-action {
    color: #ffffff; }
  .theme-warning .panel-primary .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #ffa800; }
.theme-warning .panel-line.panel-primary .panel-heading {
  color: #ffa800;
  border-top-color: #ffa800;
  background: transparent; }
.theme-warning .panel-line.panel-primary .panel-title, .theme-warning .panel-line.panel-primary .panel-action {
  color: #ffa800; }
.theme-warning .panel-info {
  border-color: #00D0FF; }
  .theme-warning .panel-info > .panel-heading {
    color: #ffffff;
    background-color: #00D0FF;
    border-color: #00D0FF; }
    .theme-warning .panel-info > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #00D0FF; }
    .theme-warning .panel-info > .panel-heading .badge-pill {
      color: #00D0FF;
      background-color: #ffffff; }
  .theme-warning .panel-info .panel-title, .theme-warning .panel-info .panel-action {
    color: #ffffff; }
  .theme-warning .panel-info .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #00D0FF; }
.theme-warning .panel-line.panel-info .panel-heading {
  color: #00D0FF;
  border-top-color: #00D0FF;
  background: transparent; }
.theme-warning .panel-line.panel-info .panel-title, .theme-warning .panel-line.panel-info .panel-action {
  color: #00D0FF; }
.theme-warning .panel-success {
  border-color: #1dbfc1; }
  .theme-warning .panel-success > .panel-heading {
    color: #ffffff;
    background-color: #1dbfc1;
    border-color: #1dbfc1; }
    .theme-warning .panel-success > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #1dbfc1; }
    .theme-warning .panel-success > .panel-heading .badge-pill {
      color: #1dbfc1;
      background-color: #ffffff; }
  .theme-warning .panel-success .panel-title, .theme-warning .panel-success .panel-action {
    color: #ffffff; }
  .theme-warning .panel-success .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #1dbfc1; }
.theme-warning .panel-line.panel-success .panel-heading {
  color: #1dbfc1;
  border-top-color: #1dbfc1;
  background: transparent; }
.theme-warning .panel-line.panel-success .panel-title, .theme-warning .panel-line.panel-success .panel-action {
  color: #1dbfc1; }
.theme-warning .panel-danger {
  border-color: #ee3158; }
  .theme-warning .panel-danger > .panel-heading {
    color: #ffffff;
    background-color: #ee3158;
    border-color: #ee3158; }
    .theme-warning .panel-danger > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #ee3158; }
    .theme-warning .panel-danger > .panel-heading .badge-pill {
      color: #ee3158;
      background-color: #ffffff; }
  .theme-warning .panel-danger .panel-title, .theme-warning .panel-danger .panel-action {
    color: #ffffff; }
  .theme-warning .panel-danger .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #ee3158; }
.theme-warning .panel-line.panel-danger .panel-heading {
  color: #ee3158;
  border-top-color: #ee3158;
  background: transparent; }
.theme-warning .panel-line.panel-danger .panel-title, .theme-warning .panel-line.panel-danger .panel-action {
  color: #ee3158; }
.theme-warning .panel-warning {
  border-color: #3246D3; }
  .theme-warning .panel-warning > .panel-heading {
    color: #ffffff;
    background-color: #3246D3;
    border-color: #3246D3; }
    .theme-warning .panel-warning > .panel-heading + .panel-collapse > .panel-body {
      border-top-color: #3246D3; }
    .theme-warning .panel-warning > .panel-heading .badge-pill {
      color: #3246D3;
      background-color: #ffffff; }
  .theme-warning .panel-warning .panel-title, .theme-warning .panel-warning .panel-action {
    color: #ffffff; }
  .theme-warning .panel-warning .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #3246D3; }
.theme-warning .panel-line.panel-warning .panel-heading {
  color: #3246D3;
  border-top-color: #3246D3;
  background: transparent; }
.theme-warning .panel-line.panel-warning .panel-title, .theme-warning .panel-line.panel-warning .panel-action {
  color: #3246D3; }

/*---switch---*/
.theme-warning .switch input:checked ~ .switch-indicator::after {
  background-color: #ffa800; }
.theme-warning .switch.switch-primary input:checked ~ .switch-indicator::after {
  background-color: #ffa800; }
.theme-warning .switch.switch-info input:checked ~ .switch-indicator::after {
  background-color: #00D0FF; }
.theme-warning .switch.switch-success input:checked ~ .switch-indicator::after {
  background-color: #1dbfc1; }
.theme-warning .switch.switch-danger input:checked ~ .switch-indicator::after {
  background-color: #ee3158; }
.theme-warning .switch.switch-warning input:checked ~ .switch-indicator::after {
  background-color: #3246D3; }

/*---badge---*/
.theme-warning .badge-primary {
  background-color: #ffa800;
  color: #ffffff; }
.theme-warning .badge-primary[href]:hover, .theme-warning .badge-primary[href]:focus {
  background-color: #cc8600; }
.theme-warning .badge-secondary {
  background-color: #e4e6ef;
  color: #172b4c; }
.theme-warning .badge-secondary[href]:hover, .theme-warning .badge-secondary[href]:focus {
  background-color: #c4c8dc; }
.theme-warning .badge-info {
  background-color: #00D0FF;
  color: #ffffff; }
.theme-warning .badge-info[href]:hover, .theme-warning .badge-info[href]:focus {
  background-color: #00a6cc; }
.theme-warning .badge-success {
  background-color: #1dbfc1;
  color: #ffffff; }
.theme-warning .badge-success[href]:hover, .theme-warning .badge-success[href]:focus {
  background-color: #169395; }
.theme-warning .badge-danger {
  background-color: #ee3158;
  color: #ffffff; }
.theme-warning .badge-danger[href]:hover, .theme-warning .badge-danger[href]:focus {
  background-color: #da123b; }
.theme-warning .badge-warning {
  background-color: #3246D3;
  color: #ffffff; }
.theme-warning .badge-warning[href]:hover, .theme-warning .badge-warning[href]:focus {
  background-color: #2536ad; }

/*---badge light---*/
.theme-warning .badge-primary-light {
  background-color: #fff8ea;
  color: #ffa800; }
.theme-warning .badge-primary-light[href]:hover, .theme-warning .badge-primary-light[href]:focus {
  background-color: #ffe7b7; }
.theme-warning .badge-secondary-light {
  background-color: #e9edf2;
  color: #172b4c; }
.theme-warning .badge-secondary-light[href]:hover, .theme-warning .badge-secondary-light[href]:focus {
  background-color: #c9d3df; }
.theme-warning .badge-info-light {
  background-color: #e1f9ff;
  color: #00D0FF; }
.theme-warning .badge-info-light[href]:hover, .theme-warning .badge-info-light[href]:focus {
  background-color: #aeefff; }
.theme-warning .badge-success-light {
  background-color: #e8f9f9;
  color: #1dbfc1; }
.theme-warning .badge-success-light[href]:hover, .theme-warning .badge-success-light[href]:focus {
  background-color: #c0eeee; }
.theme-warning .badge-danger-light {
  background-color: #ffd6de;
  color: #ee3158; }
.theme-warning .badge-danger-light[href]:hover, .theme-warning .badge-danger-light[href]:focus {
  background-color: #ffa3b5; }
.theme-warning .badge-warning-light {
  background-color: #dbdfff;
  color: #3246D3; }
.theme-warning .badge-warning-light[href]:hover, .theme-warning .badge-warning-light[href]:focus {
  background-color: #a8b2ff; }

/*---rating---*/
.theme-warning .rating-primary .active {
  color: #ffa800; }
.theme-warning .rating-primary :checked ~ label {
  color: #ffa800; }
.theme-warning .rating-primary label:hover {
  color: #ffa800; }
  .theme-warning .rating-primary label:hover ~ label {
    color: #ffa800; }
.theme-warning .rating-info .active {
  color: #00D0FF; }
.theme-warning .rating-info :checked ~ label {
  color: #00D0FF; }
.theme-warning .rating-info label:hover {
  color: #00D0FF; }
  .theme-warning .rating-info label:hover ~ label {
    color: #00D0FF; }
.theme-warning .rating-success .active {
  color: #1dbfc1; }
.theme-warning .rating-success :checked ~ label {
  color: #1dbfc1; }
.theme-warning .rating-success label:hover {
  color: #1dbfc1; }
  .theme-warning .rating-success label:hover ~ label {
    color: #1dbfc1; }
.theme-warning .rating-danger .active {
  color: #ee3158; }
.theme-warning .rating-danger :checked ~ label {
  color: #ee3158; }
.theme-warning .rating-danger label:hover {
  color: #ee3158; }
  .theme-warning .rating-danger label:hover ~ label {
    color: #ee3158; }
.theme-warning .rating-warning .active {
  color: #3246D3; }
.theme-warning .rating-warning :checked ~ label {
  color: #3246D3; }
.theme-warning .rating-warning label:hover {
  color: #3246D3; }
  .theme-warning .rating-warning label:hover ~ label {
    color: #3246D3; }

/*---toggler---*/
.theme-warning .toggler-primary input:checked + i {
  color: #ffa800; }
.theme-warning .toggler-info input:checked + i {
  color: #00D0FF; }
.theme-warning .toggler-success input:checked + i {
  color: #1dbfc1; }
.theme-warning .toggler-danger input:checked + i {
  color: #ee3158; }
.theme-warning .toggler-warning input:checked + i {
  color: #3246D3; }

/*---nav tabs---*/
.theme-warning .nav-tabs.nav-tabs-primary .nav-link:hover, .theme-warning .nav-tabs.nav-tabs-primary .nav-link:active, .theme-warning .nav-tabs.nav-tabs-primary .nav-link:focus, .theme-warning .nav-tabs.nav-tabs-primary .nav-link.active {
  border-color: #cc8600;
  background-color: transparent;
  color: #cc8600; }
.theme-warning .nav-tabs.nav-tabs-info .nav-link:hover, .theme-warning .nav-tabs.nav-tabs-info .nav-link:active, .theme-warning .nav-tabs.nav-tabs-info .nav-link:focus, .theme-warning .nav-tabs.nav-tabs-info .nav-link.active {
  border-color: #00a6cc;
  background-color: #00D0FF;
  color: #ffffff; }
.theme-warning .nav-tabs.nav-tabs-success .nav-link:hover, .theme-warning .nav-tabs.nav-tabs-success .nav-link:active, .theme-warning .nav-tabs.nav-tabs-success .nav-link:focus, .theme-warning .nav-tabs.nav-tabs-success .nav-link.active {
  border-color: #169395;
  background-color: transparent;
  color: #169395; }
.theme-warning .nav-tabs.nav-tabs-danger .nav-link:hover, .theme-warning .nav-tabs.nav-tabs-danger .nav-link:active, .theme-warning .nav-tabs.nav-tabs-danger .nav-link:focus, .theme-warning .nav-tabs.nav-tabs-danger .nav-link.active {
  border-color: #da123b;
  background-color: transparent;
  color: #da123b; }
.theme-warning .nav-tabs.nav-tabs-warning .nav-link:hover, .theme-warning .nav-tabs.nav-tabs-warning .nav-link:active, .theme-warning .nav-tabs.nav-tabs-warning .nav-link:focus, .theme-warning .nav-tabs.nav-tabs-warning .nav-link.active {
  border-color: #2536ad;
  background-color: transparent;
  color: #2536ad; }
.theme-warning .nav-tabs-custom.tab-primary > .nav-tabs > li a.active {
  border-top-color: #cc8600; }
.theme-warning .nav-tabs-custom.tab-info > .nav-tabs > li a.active {
  border-top-color: #00a6cc; }
.theme-warning .nav-tabs-custom.tab-success > .nav-tabs > li a.active {
  border-top-color: #169395; }
.theme-warning .nav-tabs-custom.tab-danger > .nav-tabs > li a.active {
  border-top-color: #da123b; }
.theme-warning .nav-tabs-custom.tab-warning > .nav-tabs > li a.active {
  border-top-color: #2536ad; }
.theme-warning .nav-tabs .nav-link.active {
  border-bottom-color: #ffa800;
  background-color: #ffa800;
  color: #ffffff; }
  .theme-warning .nav-tabs .nav-link.active:hover, .theme-warning .nav-tabs .nav-link.active:focus {
    border-bottom-color: #ffa800;
    background-color: #ffa800;
    color: #ffffff; }
.theme-warning .nav-tabs .nav-item.open .nav-link {
  border-bottom-color: #ffa800;
  background-color: #ffa800; }
  .theme-warning .nav-tabs .nav-item.open .nav-link:hover, .theme-warning .nav-tabs .nav-item.open .nav-link:focus {
    border-bottom-color: #ffa800;
    background-color: #ffa800; }

/*---todo---*/
.theme-warning .todo-list .primary {
  border-left-color: #ffa800; }
.theme-warning .todo-list .info {
  border-left-color: #ffa800; }
.theme-warning .todo-list .success {
  border-left-color: #1dbfc1; }
.theme-warning .todo-list .danger {
  border-left-color: #ee3158; }
.theme-warning .todo-list .warning {
  border-left-color: #3246D3; }

/*---timeline---*/
.theme-warning .timeline .timeline-item > .timeline-event.timeline-event-primary {
  background-color: #ffa800;
  border: 1px solid #ffa800;
  color: #ffffff; }
  .theme-warning .timeline .timeline-item > .timeline-event.timeline-event-primary:before, .theme-warning .timeline .timeline-item > .timeline-event.timeline-event-primary:after {
    border-left-color: #ffa800;
    border-right-color: #ffa800; }
  .theme-warning .timeline .timeline-item > .timeline-event.timeline-event-primary * {
    color: inherit; }
.theme-warning .timeline .timeline-item > .timeline-event.timeline-event-info {
  background-color: #00D0FF;
  border: 1px solid #00D0FF;
  color: #ffffff; }
  .theme-warning .timeline .timeline-item > .timeline-event.timeline-event-info:before, .theme-warning .timeline .timeline-item > .timeline-event.timeline-event-info:after {
    border-left-color: #00D0FF;
    border-right-color: #00D0FF; }
  .theme-warning .timeline .timeline-item > .timeline-event.timeline-event-info * {
    color: inherit; }
.theme-warning .timeline .timeline-item > .timeline-event.timeline-event-success {
  background-color: #1dbfc1;
  border: 1px solid #1dbfc1;
  color: #ffffff; }
  .theme-warning .timeline .timeline-item > .timeline-event.timeline-event-success:before, .theme-warning .timeline .timeline-item > .timeline-event.timeline-event-success:after {
    border-left-color: #1dbfc1;
    border-right-color: #1dbfc1; }
  .theme-warning .timeline .timeline-item > .timeline-event.timeline-event-success * {
    color: inherit; }
.theme-warning .timeline .timeline-item > .timeline-event.timeline-event-danger {
  background-color: #ee3158;
  border: 1px solid #ee3158;
  color: #ffffff; }
  .theme-warning .timeline .timeline-item > .timeline-event.timeline-event-danger:before, .theme-warning .timeline .timeline-item > .timeline-event.timeline-event-danger:after {
    border-left-color: #ee3158;
    border-right-color: #ee3158; }
  .theme-warning .timeline .timeline-item > .timeline-event.timeline-event-danger * {
    color: inherit; }
.theme-warning .timeline .timeline-item > .timeline-event.timeline-event-warning {
  background-color: #3246D3;
  border: 1px solid #3246D3;
  color: #ffffff; }
  .theme-warning .timeline .timeline-item > .timeline-event.timeline-event-warning:before, .theme-warning .timeline .timeline-item > .timeline-event.timeline-event-warning:after {
    border-left-color: #3246D3;
    border-right-color: #3246D3; }
  .theme-warning .timeline .timeline-item > .timeline-event.timeline-event-warning * {
    color: inherit; }
.theme-warning .timeline .timeline-item > .timeline-point.timeline-point-primary {
  color: #ffa800;
  background-color: #ffffff; }
.theme-warning .timeline .timeline-item > .timeline-point.timeline-point-info {
  color: #00D0FF;
  background-color: #ffffff; }
.theme-warning .timeline .timeline-item > .timeline-point.timeline-point-success {
  color: #1dbfc1;
  background-color: #ffffff; }
.theme-warning .timeline .timeline-item > .timeline-point.timeline-point-danger {
  color: #ee3158;
  background-color: #ffffff; }
.theme-warning .timeline .timeline-item > .timeline-point.timeline-point-warning {
  color: #3246D3;
  background-color: #ffffff; }
.theme-warning .timeline .timeline-label .label-primary {
  background-color: #ffa800; }
.theme-warning .timeline .timeline-label .label-info {
  background-color: #00D0FF; }
.theme-warning .timeline .timeline-label .label-success {
  background-color: #1dbfc1; }
.theme-warning .timeline .timeline-label .label-danger {
  background-color: #ee3158; }
.theme-warning .timeline .timeline-label .label-warning {
  background-color: #3246D3; }
.theme-warning .timeline__year, .theme-warning .timeline5:before, .theme-warning .timeline__box:before, .theme-warning .timeline__date {
  background-color: #ffa800; }
.theme-warning .timeline__post {
  border-left: 3px solid #ffa800; }

/*---daterangepicker---*/
.theme-warning .daterangepicker td.active {
  background-color: #ffa800; }
  .theme-warning .daterangepicker td.active:hover {
    background-color: #ffa800; }
.theme-warning .daterangepicker .input-mini.active {
  border: 1px solid #ffa800; }
.theme-warning .ranges li:hover, .theme-warning .ranges li:active, .theme-warning .ranges li.active {
  border: 1px solid #ffa800;
  background-color: #ffa800; }

/*---control-sidebar---*/
.theme-warning .control-sidebar .nav-tabs.control-sidebar-tabs > li > a:hover, .theme-warning .control-sidebar .nav-tabs.control-sidebar-tabs > li > a:active, .theme-warning .control-sidebar .nav-tabs.control-sidebar-tabs > li > a:focus {
  border-color: #ffa800;
  color: #ffa800; }
.theme-warning .control-sidebar .nav-tabs.control-sidebar-tabs > li > a.active {
  border-color: #ffa800;
  color: #ffa800; }
  .theme-warning .control-sidebar .nav-tabs.control-sidebar-tabs > li > a.active:hover, .theme-warning .control-sidebar .nav-tabs.control-sidebar-tabs > li > a.active:active, .theme-warning .control-sidebar .nav-tabs.control-sidebar-tabs > li > a.active:focus {
    border-color: #ffa800;
    color: #ffa800; }
.theme-warning .control-sidebar .rpanel-title .btn:hover {
  color: #ffa800; }

/*---nav---*/
.theme-warning .nav > li > a:hover, .theme-warning .nav > li > a:active, .theme-warning .nav > li > a:focus {
  color: #ffa800; }
.theme-warning .nav-pills > li > a.active {
  border-top-color: #ffa800;
  background-color: #ffa800 !important;
  color: #ffffff; }
  .theme-warning .nav-pills > li > a.active:hover, .theme-warning .nav-pills > li > a.active:focus {
    border-top-color: #ffa800;
    background-color: #ffa800 !important;
    color: #ffffff; }
.theme-warning .mailbox-nav .nav-pills > li > a:hover, .theme-warning .mailbox-nav .nav-pills > li > a:focus {
  border-color: #ffa800; }
.theme-warning .mailbox-nav .nav-pills > li > a.active {
  border-color: #ffa800; }
  .theme-warning .mailbox-nav .nav-pills > li > a.active:hover, .theme-warning .mailbox-nav .nav-pills > li > a.active:focus {
    border-color: #ffa800; }
.theme-warning .nav-tabs-custom > .nav-tabs > li a.active {
  border-top-color: #ffa800; }
.theme-warning .profile-tab li a.nav-link.active {
  border-bottom: 2px solid #ffa800; }
.theme-warning .customtab li a.nav-link.active {
  border-bottom: 2px solid #ffa800; }

/*---form-element---*/
.theme-warning .form-element .input-group .input-group-addon {
  background-image: linear-gradient(45deg, #ffa800, #00D0FF), linear-gradient(#3b6dc1, #3b6dc1); }
.theme-warning .form-element .form-control {
  background-image: linear-gradient(45deg, #ffa800, #00D0FF), linear-gradient(#3b6dc1, #3b6dc1); }
  .theme-warning .form-element .form-control:focus {
    background-image: linear-gradient(45deg, #ffa800, #00D0FF), linear-gradient(#3b6dc1, #3b6dc1); }
.theme-warning .form-control:focus {
  border-color: #ffa800; }
.theme-warning [type=checkbox]:checked.chk-col-primary + label:before {
  border-right: 2px solid #ffa800;
  border-bottom: 2px solid #ffa800; }
.theme-warning [type=checkbox]:checked.chk-col-info + label:before {
  border-right: 2px solid #00D0FF;
  border-bottom: 2px solid #00D0FF; }
.theme-warning [type=checkbox]:checked.chk-col-success + label:before {
  border-right: 2px solid #1dbfc1;
  border-bottom: 2px solid #1dbfc1; }
.theme-warning [type=checkbox]:checked.chk-col-danger + label:before {
  border-right: 2px solid #ee3158;
  border-bottom: 2px solid #ee3158; }
.theme-warning [type=checkbox]:checked.chk-col-warning + label:before {
  border-right: 2px solid #3246D3;
  border-bottom: 2px solid #3246D3; }
.theme-warning [type=checkbox].filled-in:checked.chk-col-primary + label:after {
  border: 2px solid #ffa800;
  background-color: #ffa800; }
.theme-warning [type=checkbox].filled-in:checked.chk-col-info + label:after {
  border: 2px solid #00D0FF;
  background-color: #00D0FF; }
.theme-warning [type=checkbox].filled-in:checked.chk-col-success + label:after {
  border: 2px solid #1dbfc1;
  background-color: #1dbfc1; }
.theme-warning [type=checkbox].filled-in:checked.chk-col-danger + label:after {
  border: 2px solid #ee3158;
  background-color: #ee3158; }
.theme-warning [type=checkbox].filled-in:checked.chk-col-warning + label:after {
  border: 2px solid #3246D3;
  background-color: #3246D3; }
.theme-warning [type=radio].radio-col-primary:checked + label:after {
  background-color: #ffa800;
  border-color: #ffa800;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-warning [type=radio].with-gap.radio-col-primary:checked + label:before {
  border: 2px solid #ffa800;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-warning [type=radio].with-gap.radio-col-primary:checked + label:after {
  background-color: #ffa800;
  border: 2px solid #ffa800;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-warning [type=radio].radio-col-info:checked + label:after {
  background-color: #00D0FF;
  border-color: #00D0FF;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-warning [type=radio].with-gap.radio-col-info:checked + label:before {
  border: 2px solid #00D0FF;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-warning [type=radio].with-gap.radio-col-info:checked + label:after {
  background-color: #00D0FF;
  border: 2px solid #00D0FF;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-warning [type=radio].radio-col-success:checked + label:after {
  background-color: #1dbfc1;
  border-color: #1dbfc1;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-warning [type=radio].with-gap.radio-col-success:checked + label:before {
  border: 2px solid #1dbfc1;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-warning [type=radio].with-gap.radio-col-success:checked + label:after {
  background-color: #1dbfc1;
  border: 2px solid #1dbfc1;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-warning [type=radio].radio-col-danger:checked + label:after {
  background-color: #ee3158;
  border-color: #ee3158;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-warning [type=radio].with-gap.radio-col-danger:checked + label:before {
  border: 2px solid #ee3158;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-warning [type=radio].with-gap.radio-col-danger:checked + label:after {
  background-color: #ee3158;
  border: 2px solid #ee3158;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-warning [type=radio].radio-col-warning:checked + label:after {
  background-color: #3246D3;
  border-color: #3246D3;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-warning [type=radio].with-gap.radio-col-warning:checked + label:before {
  border: 2px solid #3246D3;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-warning [type=radio].with-gap.radio-col-warning:checked + label:after {
  background-color: #3246D3;
  border: 2px solid #3246D3;
  -webkit-animation: ripple .2s linear forwards;
  animation: ripple .2s linear forwards; }
.theme-warning [type=checkbox]:checked + label:before {
  border-right: 2px solid #ffa800;
  border-bottom: 2px solid #ffa800; }
.theme-warning [type=checkbox].filled-in:checked + label:after {
  border: 2px solid #ffa800;
  background-color: #ffa800; }
.theme-warning [type=radio].with-gap:checked + label:before, .theme-warning [type=radio].with-gap:checked + label:after {
  border: 2px solid #ffa800; }
.theme-warning [type=radio].with-gap:checked + label:after {
  background-color: #ffa800;
  z-index: 0; }
.theme-warning [type=radio]:checked + label:after {
  border: 2px solid #ffa800;
  background-color: #ffa800;
  z-index: 0; }
.theme-warning [type=checkbox].filled-in.tabbed:checked:focus + label:after {
  border-color: #ffa800;
  background-color: #ffa800; }

/*---Calender---*/
.theme-warning .fx-element-overlay .fx-card-item .fx-card-content a:hover {
  color: #ffa800; }
.theme-warning .fx-element-overlay .fx-card-item .fx-overlay-1 .fx-info > li a:hover {
  background: #ffa800;
  border-color: #ffa800; }
.theme-warning .fc-event, .theme-warning .calendar-event {
  background: #ffa800; }

/*---Tabs---*/
.theme-warning .tabs-vertical li .nav-link:hover, .theme-warning .tabs-vertical li .nav-link:active, .theme-warning .tabs-vertical li .nav-link:focus, .theme-warning .tabs-vertical li .nav-link.active {
  background-color: #ffa800;
  color: #ffffff; }
.theme-warning .customvtab .tabs-vertical li .nav-link:hover, .theme-warning .customvtab .tabs-vertical li .nav-link:active, .theme-warning .customvtab .tabs-vertical li .nav-link:focus, .theme-warning .customvtab .tabs-vertical li .nav-link.active {
  border-right: 2px solid #ffa800;
  color: #ffa800; }
.theme-warning .customtab2 li a.nav-link:hover, .theme-warning .customtab2 li a.nav-link:active, .theme-warning .customtab2 li a.nav-link.active {
  background-color: #ffa800; }

/*---Notification---*/
.theme-warning .jq-icon-primary {
  background-color: #ffa800;
  color: #ffffff;
  border-color: #ffa800; }
.theme-warning .jq-icon-info {
  background-color: #00D0FF;
  color: #ffffff;
  border-color: #00D0FF; }
.theme-warning .jq-icon-success {
  background-color: #1dbfc1;
  color: #ffffff;
  border-color: #ffa800; }
.theme-warning .jq-icon-error {
  background-color: #ee3158;
  color: #ffffff;
  border-color: #ee3158; }
.theme-warning .jq-icon-danger {
  background-color: #ee3158;
  color: #ffffff;
  border-color: #ee3158; }
.theme-warning .jq-icon-warning {
  background-color: #3246D3;
  color: #ffffff;
  border-color: #3246D3; }

/*---avatar---*/
.theme-warning .avatar.status-primary::after {
  background-color: #ffa800; }
.theme-warning .avatar.status-info::after {
  background-color: #00D0FF; }
.theme-warning .avatar.status-success::after {
  background-color: #1dbfc1; }
.theme-warning .avatar.status-danger::after {
  background-color: #ee3158; }
.theme-warning .avatar.status-warning::after {
  background-color: #3246D3; }
.theme-warning .avatar[class*='status-']::after {
  background-color: #ffa800; }
.theme-warning .avatar-add:hover {
  background-color: #cc8600;
  border-color: #cc8600; }

/*---media---*/
.theme-warning .media-chat.media-chat-reverse .media-body p {
  background-color: #ffa800; }
.theme-warning .media-right-out a:hover {
  color: #cc8600; }

/*---control---*/
.theme-warning .control input:checked:focus ~ .control_indicator {
  background-color: #ffa800; }
.theme-warning .control input:checked ~ .control_indicator {
  background-color: #ffa800; }
.theme-warning .control:hover input:not([disabled]):checked ~ .control_indicator {
  background-color: #ffa800; }

/*---flex---*/
.theme-warning .flex-column > li > a.nav-link.active {
  border-left-color: #ffa800; }
  .theme-warning .flex-column > li > a.nav-link.active:hover {
    border-left-color: #ffa800; }

/*---pagination---*/
.theme-warning .pagination li a.current {
  border: 1px solid #ffa800;
  background-color: #ffa800; }
  .theme-warning .pagination li a.current:hover {
    border: 1px solid #ffa800;
    background-color: #ffa800; }
.theme-warning .pagination li a:hover {
  border: 1px solid #cc8600;
  background-color: #cc8600 !important; }
.theme-warning .dataTables_wrapper .dataTables_paginate .paginate_button.current {
  border: 1px solid #ffa800;
  background-color: #ffa800; }
  .theme-warning .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    border: 1px solid #ffa800;
    background-color: #ffa800; }
.theme-warning .paging_simple_numbers .pagination .paginate_button.active a {
  background-color: #ffa800; }
.theme-warning .paging_simple_numbers .pagination .paginate_button:hover a {
  background-color: #ffa800; }
.theme-warning .footable .pagination li a:hover, .theme-warning .footable .pagination li a:active, .theme-warning .footable .pagination li a.active {
  background-color: #ffa800; }

/*---dataTables---*/
.theme-warning .dt-buttons .dt-button {
  background-color: #ffa800; }

/*---select2---*/
.theme-warning .select2-container--default.select2-container--open {
  border-color: #ffa800; }
.theme-warning .select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: #ffa800; }
.theme-warning .select2-container--default .select2-search--dropdown .select2-search__field {
  border-color: #ffa800 !important; }
.theme-warning .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #ffa800 !important; }
.theme-warning .select2-container--default .select2-selection--multiple:focus {
  border-color: #ffa800 !important; }
.theme-warning .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #ffa800;
  border-color: #ffa800; }

/*---Other---*/
.theme-warning .myadmin-dd .dd-list .dd-list .dd-handle:hover {
  color: #cc8600; }
.theme-warning .myadmin-dd-empty .dd-list .dd3-handle:hover {
  color: #cc8600; }
.theme-warning .myadmin-dd-empty .dd-list .dd3-content:hover {
  color: #cc8600; }
.theme-warning [data-overlay-primary]::before {
  background: #cc8600; }

/*---wizard---*/
.theme-warning .wizard-content .wizard > .steps > ul > li.current {
  border: 2px solid #ffa800;
  background-color: #ffa800; }
.theme-warning .wizard-content .wizard > .steps > ul > li.done {
  border-color: #cc8600;
  background-color: #cc8600; }
.theme-warning .wizard-content .wizard > .actions > ul > li > a {
  background-color: #ffa800; }
.theme-warning .wizard-content .wizard.wizard-circle > .steps > ul > li:after {
  background-color: #ffa800; }
.theme-warning .wizard-content .wizard.wizard-circle > .steps > ul > li:before {
  background-color: #ffa800; }
.theme-warning .wizard-content .wizard.wizard-notification > .steps > ul > li:after {
  background-color: #ffa800; }
.theme-warning .wizard-content .wizard.wizard-notification > .steps > ul > li:before {
  background-color: #ffa800; }
.theme-warning .wizard-content .wizard.wizard-notification > .steps > ul > li.current .step {
  border: 2px solid #ffa800;
  color: #ffa800; }
  .theme-warning .wizard-content .wizard.wizard-notification > .steps > ul > li.current .step:after {
    border-top-color: #ffa800; }
.theme-warning .wizard-content .wizard.wizard-notification > .steps > ul > li.done .step:after {
  border-top-color: #ffa800; }

@media (max-width: 767px) {
  .theme-warning .wizard-content .wizard > .steps > ul > li:last-child:after {
    background-color: #ffa800; } }
@media (max-width: 575px) {
  .theme-warning .wizard-content .wizard > .steps > ul > li.current:after {
    background-color: #ffa800; } }
/*---slider---*/
.theme-warning #primary .slider-selection {
  background-color: #ffa800; }
.theme-warning #info .slider-selection {
  background-color: #00D0FF; }
.theme-warning #success .slider-selection {
  background-color: #1dbfc1; }
.theme-warning #danger .slider-selection {
  background-color: #ee3158; }
.theme-warning #warning .slider-selection {
  background-color: #3246D3; }

/*---horizontal-timeline---*/
.theme-warning .cd-horizontal-timeline .events a.selected::after {
  background: #ffa800;
  border-color: #ffa800; }
.theme-warning .cd-horizontal-timeline .events a.older-event::after {
  border-color: #ffa800; }
.theme-warning .cd-horizontal-timeline .filling-line {
  background: #ffa800; }
.theme-warning .cd-horizontal-timeline a {
  color: #ffa800; }
  .theme-warning .cd-horizontal-timeline a:hover, .theme-warning .cd-horizontal-timeline a:focus {
    color: #ffa800; }
.theme-warning .cd-timeline-navigation a:hover, .theme-warning .cd-timeline-navigation a:focus {
  border-color: #ffa800; }

/*# sourceMappingURL=color_theme.css.map */
