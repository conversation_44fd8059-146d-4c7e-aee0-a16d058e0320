/*
Template Name: Rhythm - Responsive Admin Template 
Author: Multipurpose Themes
File: scss
*/
@import url("https://fonts.googleapis.com/css?family=IBM+Plex+Sans:100,100i,200,200i,300,300i,400,400i,500,500i,600,600i,700,700i|Rubik:300,300i,400,400i,500,500i,700,700i,900,900i&display=swap");
/*Social Media Colors*/
/*Theme Colors*/
/*Lite color*/
/*Theme Colors For Dark*/
.rtl {
  text-align: right !important;
  direction: rtl; }

/*******************
Padding property 
*******************/
.rtl .ps-0 {
  padding-right: 0px !important;
  padding-left: unset !important; }

.rtl .ps-5 {
  padding-right: 5px !important;
  padding-left: unset !important; }

.rtl .ps-10 {
  padding-right: 10px !important;
  padding-left: unset !important; }

.rtl .ps-15 {
  padding-right: 15px !important;
  padding-left: unset !important; }

.rtl .ps-20 {
  padding-right: 20px !important;
  padding-left: unset !important; }

.rtl .ps-25 {
  padding-right: 25px !important;
  padding-left: unset !important; }

.rtl .ps-30 {
  padding-right: 30px !important;
  padding-left: unset !important; }

.rtl .ps-35 {
  padding-right: 35px !important;
  padding-left: unset !important; }

.rtl .ps-40 {
  padding-right: 40px !important;
  padding-left: unset !important; }

.rtl .ps-45 {
  padding-right: 45px !important;
  padding-left: unset !important; }

.rtl .ps-50 {
  padding-right: 50px !important;
  padding-left: unset !important; }

.rtl .ps-55 {
  padding-right: 55px !important;
  padding-left: unset !important; }

.rtl .ps-60 {
  padding-right: 60px !important;
  padding-left: unset !important; }

.rtl .ps-65 {
  padding-right: 65px !important;
  padding-left: unset !important; }

.rtl .ps-70 {
  padding-right: 70px !important;
  padding-left: unset !important; }

.rtl .ps-75 {
  padding-right: 75px !important;
  padding-left: unset !important; }

.rtl .ps-80 {
  padding-right: 80px !important;
  padding-left: unset !important; }

.rtl .ps-85 {
  padding-right: 85px !important;
  padding-left: unset !important; }

.rtl .ps-90 {
  padding-right: 90px !important;
  padding-left: unset !important; }

.rtl .ps-95 {
  padding-right: 95px !important;
  padding-left: unset !important; }

.rtl .ps-100 {
  padding-right: 100px !important;
  padding-left: unset !important; }

.rtl .ps-105 {
  padding-right: 105px !important;
  padding-left: unset !important; }

.rtl .ps-110 {
  padding-right: 110px !important;
  padding-left: unset !important; }

.rtl .ps-115 {
  padding-right: 115px !important;
  padding-left: unset !important; }

.rtl .ps-120 {
  padding-right: 120px !important;
  padding-left: unset !important; }

.rtl .ps-125 {
  padding-right: 125px !important;
  padding-left: unset !important; }

.rtl .ps-130 {
  padding-right: 130px !important;
  padding-left: unset !important; }

.rtl .ps-135 {
  padding-right: 135px !important;
  padding-left: unset !important; }

.rtl .ps-140 {
  padding-right: 140px !important;
  padding-left: unset !important; }

.rtl .ps-145 {
  padding-right: 145px !important;
  padding-left: unset !important; }

.rtl .ps-150 {
  padding-right: 150px !important;
  padding-left: unset !important; }

.rtl .ps-155 {
  padding-right: 155px !important;
  padding-left: unset !important; }

.rtl .ps-160 {
  padding-right: 160px !important;
  padding-left: unset !important; }

.rtl .ps-165 {
  padding-right: 165px !important;
  padding-left: unset !important; }

.rtl .ps-170 {
  padding-right: 170px !important;
  padding-left: unset !important; }

.rtl .ps-175 {
  padding-right: 175px !important;
  padding-left: unset !important; }

.rtl .ps-180 {
  padding-right: 180px !important;
  padding-left: unset !important; }

.rtl .ps-185 {
  padding-right: 185px !important;
  padding-left: unset !important; }

.rtl .ps-190 {
  padding-right: 190px !important;
  padding-left: unset !important; }

.rtl .ps-195 {
  padding-right: 195px !important;
  padding-left: unset !important; }

.rtl .ps-200 {
  padding-right: 200px !important;
  padding-left: unset !important; }

.rtl .pe-0 {
  padding-left: 0px !important;
  padding-right: unset !important; }

.rtl .pe-5 {
  padding-left: 5px !important;
  padding-right: unset !important; }

.rtl .pe-10 {
  padding-left: 10px !important;
  padding-right: unset !important; }

.rtl .pe-15 {
  padding-left: 15px !important;
  padding-right: unset !important; }

.rtl .pe-20 {
  padding-left: 20px !important;
  padding-right: unset !important; }

.rtl .pe-25 {
  padding-left: 25px !important;
  padding-right: unset !important; }

.rtl .pe-30 {
  padding-left: 30px !important;
  padding-right: unset !important; }

.rtl .pe-35 {
  padding-left: 35px !important;
  padding-right: unset !important; }

.rtl .pe-40 {
  padding-left: 40px !important;
  padding-right: unset !important; }

.rtl .pe-45 {
  padding-left: 45px !important;
  padding-right: unset !important; }

.rtl .pe-50 {
  padding-left: 50px !important;
  padding-right: unset !important; }

.rtl .pe-55 {
  padding-left: 55px !important;
  padding-right: unset !important; }

.rtl .pe-60 {
  padding-left: 60px !important;
  padding-right: unset !important; }

.rtl .pe-65 {
  padding-left: 65px !important;
  padding-right: unset !important; }

.rtl .pe-70 {
  padding-left: 70px !important;
  padding-right: unset !important; }

.rtl .pe-75 {
  padding-left: 75px !important;
  padding-right: unset !important; }

.rtl .pe-80 {
  padding-left: 80px !important;
  padding-right: unset !important; }

.rtl .pe-85 {
  padding-left: 85px !important;
  padding-right: unset !important; }

.rtl .pe-90 {
  padding-left: 90px !important;
  padding-right: unset !important; }

.rtl .pe-95 {
  padding-left: 95px !important;
  padding-right: unset !important; }

.rtl .pe-100 {
  padding-left: 100px !important;
  padding-right: unset !important; }

.rtl .pe-105 {
  padding-left: 105px !important;
  padding-right: unset !important; }

.rtl .pe-110 {
  padding-left: 110px !important;
  padding-right: unset !important; }

.rtl .pe-115 {
  padding-left: 115px !important;
  padding-right: unset !important; }

.rtl .pe-120 {
  padding-left: 120px !important;
  padding-right: unset !important; }

.rtl .pe-125 {
  padding-left: 125px !important;
  padding-right: unset !important; }

.rtl .pe-130 {
  padding-left: 130px !important;
  padding-right: unset !important; }

.rtl .pe-135 {
  padding-left: 135px !important;
  padding-right: unset !important; }

.rtl .pe-140 {
  padding-left: 140px !important;
  padding-right: unset !important; }

.rtl .pe-145 {
  padding-left: 145px !important;
  padding-right: unset !important; }

.rtl .pe-150 {
  padding-left: 150px !important;
  padding-right: unset !important; }

.rtl .pe-155 {
  padding-left: 155px !important;
  padding-right: unset !important; }

.rtl .pe-160 {
  padding-left: 160px !important;
  padding-right: unset !important; }

.rtl .pe-165 {
  padding-left: 165px !important;
  padding-right: unset !important; }

.rtl .pe-170 {
  padding-left: 170px !important;
  padding-right: unset !important; }

.rtl .pe-175 {
  padding-left: 175px !important;
  padding-right: unset !important; }

.rtl .pe-180 {
  padding-left: 180px !important;
  padding-right: unset !important; }

.rtl .pe-185 {
  padding-left: 185px !important;
  padding-right: unset !important; }

.rtl .pe-190 {
  padding-left: 190px !important;
  padding-right: unset !important; }

.rtl .pe-195 {
  padding-left: 195px !important;
  padding-right: unset !important; }

.rtl .pe-200 {
  padding-left: 200px !important;
  padding-right: unset !important; }

@media (max-width: 575px) {
  .rtl .ps-xs-0 {
    padding-right: 0px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-5 {
    padding-right: 5px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-10 {
    padding-right: 10px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-15 {
    padding-right: 15px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-20 {
    padding-right: 20px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-25 {
    padding-right: 25px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-30 {
    padding-right: 30px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-35 {
    padding-right: 35px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-40 {
    padding-right: 40px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-45 {
    padding-right: 45px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-50 {
    padding-right: 50px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-55 {
    padding-right: 55px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-60 {
    padding-right: 60px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-65 {
    padding-right: 65px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-70 {
    padding-right: 70px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-75 {
    padding-right: 75px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-80 {
    padding-right: 80px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-85 {
    padding-right: 85px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-90 {
    padding-right: 90px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-95 {
    padding-right: 95px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-100 {
    padding-right: 100px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-105 {
    padding-right: 105px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-110 {
    padding-right: 110px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-115 {
    padding-right: 115px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-120 {
    padding-right: 120px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-125 {
    padding-right: 125px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-130 {
    padding-right: 130px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-135 {
    padding-right: 135px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-140 {
    padding-right: 140px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-145 {
    padding-right: 145px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-150 {
    padding-right: 150px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-155 {
    padding-right: 155px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-160 {
    padding-right: 160px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-165 {
    padding-right: 165px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-170 {
    padding-right: 170px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-175 {
    padding-right: 175px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-180 {
    padding-right: 180px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-185 {
    padding-right: 185px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-190 {
    padding-right: 190px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-195 {
    padding-right: 195px !important;
    padding-left: unset !important; }

  .rtl .ps-xs-200 {
    padding-right: 200px !important;
    padding-left: unset !important; }

  .rtl .pe-xs-0 {
    padding-left: 0px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-5 {
    padding-left: 5px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-10 {
    padding-left: 10px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-15 {
    padding-left: 15px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-20 {
    padding-left: 20px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-25 {
    padding-left: 25px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-30 {
    padding-left: 30px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-35 {
    padding-left: 35px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-40 {
    padding-left: 40px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-45 {
    padding-left: 45px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-50 {
    padding-left: 50px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-55 {
    padding-left: 55px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-60 {
    padding-left: 60px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-65 {
    padding-left: 65px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-70 {
    padding-left: 70px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-75 {
    padding-left: 75px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-80 {
    padding-left: 80px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-85 {
    padding-left: 85px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-90 {
    padding-left: 90px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-95 {
    padding-left: 95px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-100 {
    padding-left: 100px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-105 {
    padding-left: 105px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-110 {
    padding-left: 110px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-115 {
    padding-left: 115px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-120 {
    padding-left: 120px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-125 {
    padding-left: 125px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-130 {
    padding-left: 130px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-135 {
    padding-left: 135px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-140 {
    padding-left: 140px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-145 {
    padding-left: 145px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-150 {
    padding-left: 150px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-155 {
    padding-left: 155px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-160 {
    padding-left: 160px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-165 {
    padding-left: 165px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-170 {
    padding-left: 170px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-175 {
    padding-left: 175px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-180 {
    padding-left: 180px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-185 {
    padding-left: 185px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-190 {
    padding-left: 190px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-195 {
    padding-left: 195px !important;
    padding-right: unset !important; }

  .rtl .pe-xs-200 {
    padding-left: 200px !important;
    padding-right: unset !important; } }
@media (min-width: 576px) {
  .rtl .ps-sm-0 {
    padding-right: 0px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-5 {
    padding-right: 5px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-10 {
    padding-right: 10px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-15 {
    padding-right: 15px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-20 {
    padding-right: 20px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-25 {
    padding-right: 25px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-30 {
    padding-right: 30px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-35 {
    padding-right: 35px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-40 {
    padding-right: 40px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-45 {
    padding-right: 45px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-50 {
    padding-right: 50px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-55 {
    padding-right: 55px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-60 {
    padding-right: 60px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-65 {
    padding-right: 65px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-70 {
    padding-right: 70px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-75 {
    padding-right: 75px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-80 {
    padding-right: 80px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-85 {
    padding-right: 85px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-90 {
    padding-right: 90px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-95 {
    padding-right: 95px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-100 {
    padding-right: 100px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-105 {
    padding-right: 105px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-110 {
    padding-right: 110px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-115 {
    padding-right: 115px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-120 {
    padding-right: 120px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-125 {
    padding-right: 125px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-130 {
    padding-right: 130px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-135 {
    padding-right: 135px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-140 {
    padding-right: 140px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-145 {
    padding-right: 145px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-150 {
    padding-right: 150px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-155 {
    padding-right: 155px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-160 {
    padding-right: 160px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-165 {
    padding-right: 165px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-170 {
    padding-right: 170px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-175 {
    padding-right: 175px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-180 {
    padding-right: 180px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-185 {
    padding-right: 185px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-190 {
    padding-right: 190px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-195 {
    padding-right: 195px !important;
    padding-left: unset !important; }

  .rtl .ps-sm-200 {
    padding-right: 200px !important;
    padding-left: unset !important; }

  .rtl .pe-sm-0 {
    padding-left: 0px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-5 {
    padding-left: 5px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-10 {
    padding-left: 10px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-15 {
    padding-left: 15px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-20 {
    padding-left: 20px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-25 {
    padding-left: 25px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-30 {
    padding-left: 30px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-35 {
    padding-left: 35px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-40 {
    padding-left: 40px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-45 {
    padding-left: 45px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-50 {
    padding-left: 50px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-55 {
    padding-left: 55px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-60 {
    padding-left: 60px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-65 {
    padding-left: 65px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-70 {
    padding-left: 70px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-75 {
    padding-left: 75px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-80 {
    padding-left: 80px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-85 {
    padding-left: 85px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-90 {
    padding-left: 90px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-95 {
    padding-left: 95px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-100 {
    padding-left: 100px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-105 {
    padding-left: 105px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-110 {
    padding-left: 110px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-115 {
    padding-left: 115px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-120 {
    padding-left: 120px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-125 {
    padding-left: 125px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-130 {
    padding-left: 130px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-135 {
    padding-left: 135px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-140 {
    padding-left: 140px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-145 {
    padding-left: 145px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-150 {
    padding-left: 150px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-155 {
    padding-left: 155px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-160 {
    padding-left: 160px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-165 {
    padding-left: 165px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-170 {
    padding-left: 170px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-175 {
    padding-left: 175px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-180 {
    padding-left: 180px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-185 {
    padding-left: 185px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-190 {
    padding-left: 190px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-195 {
    padding-left: 195px !important;
    padding-right: unset !important; }

  .rtl .pe-sm-200 {
    padding-left: 200px !important;
    padding-right: unset !important; } }
@media (min-width: 768px) {
  .rtl .ps-md-0 {
    padding-right: 0px !important;
    padding-left: unset !important; }

  .rtl .ps-md-5 {
    padding-right: 5px !important;
    padding-left: unset !important; }

  .rtl .ps-md-10 {
    padding-right: 10px !important;
    padding-left: unset !important; }

  .rtl .ps-md-15 {
    padding-right: 15px !important;
    padding-left: unset !important; }

  .rtl .ps-md-20 {
    padding-right: 20px !important;
    padding-left: unset !important; }

  .rtl .ps-md-25 {
    padding-right: 25px !important;
    padding-left: unset !important; }

  .rtl .ps-md-30 {
    padding-right: 30px !important;
    padding-left: unset !important; }

  .rtl .ps-md-35 {
    padding-right: 35px !important;
    padding-left: unset !important; }

  .rtl .ps-md-40 {
    padding-right: 40px !important;
    padding-left: unset !important; }

  .rtl .ps-md-45 {
    padding-right: 45px !important;
    padding-left: unset !important; }

  .rtl .ps-md-50 {
    padding-right: 50px !important;
    padding-left: unset !important; }

  .rtl .ps-md-55 {
    padding-right: 55px !important;
    padding-left: unset !important; }

  .rtl .ps-md-60 {
    padding-right: 60px !important;
    padding-left: unset !important; }

  .rtl .ps-md-65 {
    padding-right: 65px !important;
    padding-left: unset !important; }

  .rtl .ps-md-70 {
    padding-right: 70px !important;
    padding-left: unset !important; }

  .rtl .ps-md-75 {
    padding-right: 75px !important;
    padding-left: unset !important; }

  .rtl .ps-md-80 {
    padding-right: 80px !important;
    padding-left: unset !important; }

  .rtl .ps-md-85 {
    padding-right: 85px !important;
    padding-left: unset !important; }

  .rtl .ps-md-90 {
    padding-right: 90px !important;
    padding-left: unset !important; }

  .rtl .ps-md-95 {
    padding-right: 95px !important;
    padding-left: unset !important; }

  .rtl .ps-md-100 {
    padding-right: 100px !important;
    padding-left: unset !important; }

  .rtl .ps-md-105 {
    padding-right: 105px !important;
    padding-left: unset !important; }

  .rtl .ps-md-110 {
    padding-right: 110px !important;
    padding-left: unset !important; }

  .rtl .ps-md-115 {
    padding-right: 115px !important;
    padding-left: unset !important; }

  .rtl .ps-md-120 {
    padding-right: 120px !important;
    padding-left: unset !important; }

  .rtl .ps-md-125 {
    padding-right: 125px !important;
    padding-left: unset !important; }

  .rtl .ps-md-130 {
    padding-right: 130px !important;
    padding-left: unset !important; }

  .rtl .ps-md-135 {
    padding-right: 135px !important;
    padding-left: unset !important; }

  .rtl .ps-md-140 {
    padding-right: 140px !important;
    padding-left: unset !important; }

  .rtl .ps-md-145 {
    padding-right: 145px !important;
    padding-left: unset !important; }

  .rtl .ps-md-150 {
    padding-right: 150px !important;
    padding-left: unset !important; }

  .rtl .ps-md-155 {
    padding-right: 155px !important;
    padding-left: unset !important; }

  .rtl .ps-md-160 {
    padding-right: 160px !important;
    padding-left: unset !important; }

  .rtl .ps-md-165 {
    padding-right: 165px !important;
    padding-left: unset !important; }

  .rtl .ps-md-170 {
    padding-right: 170px !important;
    padding-left: unset !important; }

  .rtl .ps-md-175 {
    padding-right: 175px !important;
    padding-left: unset !important; }

  .rtl .ps-md-180 {
    padding-right: 180px !important;
    padding-left: unset !important; }

  .rtl .ps-md-185 {
    padding-right: 185px !important;
    padding-left: unset !important; }

  .rtl .ps-md-190 {
    padding-right: 190px !important;
    padding-left: unset !important; }

  .rtl .ps-md-195 {
    padding-right: 195px !important;
    padding-left: unset !important; }

  .rtl .ps-md-200 {
    padding-right: 200px !important;
    padding-left: unset !important; }

  .rtl .pe-md-0 {
    padding-left: 0px !important;
    padding-right: unset !important; }

  .rtl .pe-md-5 {
    padding-left: 5px !important;
    padding-right: unset !important; }

  .rtl .pe-md-10 {
    padding-left: 10px !important;
    padding-right: unset !important; }

  .rtl .pe-md-15 {
    padding-left: 15px !important;
    padding-right: unset !important; }

  .rtl .pe-md-20 {
    padding-left: 20px !important;
    padding-right: unset !important; }

  .rtl .pe-md-25 {
    padding-left: 25px !important;
    padding-right: unset !important; }

  .rtl .pe-md-30 {
    padding-left: 30px !important;
    padding-right: unset !important; }

  .rtl .pe-md-35 {
    padding-left: 35px !important;
    padding-right: unset !important; }

  .rtl .pe-md-40 {
    padding-left: 40px !important;
    padding-right: unset !important; }

  .rtl .pe-md-45 {
    padding-left: 45px !important;
    padding-right: unset !important; }

  .rtl .pe-md-50 {
    padding-left: 50px !important;
    padding-right: unset !important; }

  .rtl .pe-md-55 {
    padding-left: 55px !important;
    padding-right: unset !important; }

  .rtl .pe-md-60 {
    padding-left: 60px !important;
    padding-right: unset !important; }

  .rtl .pe-md-65 {
    padding-left: 65px !important;
    padding-right: unset !important; }

  .rtl .pe-md-70 {
    padding-left: 70px !important;
    padding-right: unset !important; }

  .rtl .pe-md-75 {
    padding-left: 75px !important;
    padding-right: unset !important; }

  .rtl .pe-md-80 {
    padding-left: 80px !important;
    padding-right: unset !important; }

  .rtl .pe-md-85 {
    padding-left: 85px !important;
    padding-right: unset !important; }

  .rtl .pe-md-90 {
    padding-left: 90px !important;
    padding-right: unset !important; }

  .rtl .pe-md-95 {
    padding-left: 95px !important;
    padding-right: unset !important; }

  .rtl .pe-md-100 {
    padding-left: 100px !important;
    padding-right: unset !important; }

  .rtl .pe-md-105 {
    padding-left: 105px !important;
    padding-right: unset !important; }

  .rtl .pe-md-110 {
    padding-left: 110px !important;
    padding-right: unset !important; }

  .rtl .pe-md-115 {
    padding-left: 115px !important;
    padding-right: unset !important; }

  .rtl .pe-md-120 {
    padding-left: 120px !important;
    padding-right: unset !important; }

  .rtl .pe-md-125 {
    padding-left: 125px !important;
    padding-right: unset !important; }

  .rtl .pe-md-130 {
    padding-left: 130px !important;
    padding-right: unset !important; }

  .rtl .pe-md-135 {
    padding-left: 135px !important;
    padding-right: unset !important; }

  .rtl .pe-md-140 {
    padding-left: 140px !important;
    padding-right: unset !important; }

  .rtl .pe-md-145 {
    padding-left: 145px !important;
    padding-right: unset !important; }

  .rtl .pe-md-150 {
    padding-left: 150px !important;
    padding-right: unset !important; }

  .rtl .pe-md-155 {
    padding-left: 155px !important;
    padding-right: unset !important; }

  .rtl .pe-md-160 {
    padding-left: 160px !important;
    padding-right: unset !important; }

  .rtl .pe-md-165 {
    padding-left: 165px !important;
    padding-right: unset !important; }

  .rtl .pe-md-170 {
    padding-left: 170px !important;
    padding-right: unset !important; }

  .rtl .pe-md-175 {
    padding-left: 175px !important;
    padding-right: unset !important; }

  .rtl .pe-md-180 {
    padding-left: 180px !important;
    padding-right: unset !important; }

  .rtl .pe-md-185 {
    padding-left: 185px !important;
    padding-right: unset !important; }

  .rtl .pe-md-190 {
    padding-left: 190px !important;
    padding-right: unset !important; }

  .rtl .pe-md-195 {
    padding-left: 195px !important;
    padding-right: unset !important; }

  .rtl .pe-md-200 {
    padding-left: 200px !important;
    padding-right: unset !important; } }
@media (min-width: 992px) {
  .rtl .ps-lg-0 {
    padding-right: 0px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-5 {
    padding-right: 5px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-10 {
    padding-right: 10px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-15 {
    padding-right: 15px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-20 {
    padding-right: 20px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-25 {
    padding-right: 25px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-30 {
    padding-right: 30px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-35 {
    padding-right: 35px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-40 {
    padding-right: 40px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-45 {
    padding-right: 45px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-50 {
    padding-right: 50px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-55 {
    padding-right: 55px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-60 {
    padding-right: 60px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-65 {
    padding-right: 65px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-70 {
    padding-right: 70px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-75 {
    padding-right: 75px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-80 {
    padding-right: 80px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-85 {
    padding-right: 85px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-90 {
    padding-right: 90px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-95 {
    padding-right: 95px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-100 {
    padding-right: 100px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-105 {
    padding-right: 105px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-110 {
    padding-right: 110px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-115 {
    padding-right: 115px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-120 {
    padding-right: 120px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-125 {
    padding-right: 125px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-130 {
    padding-right: 130px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-135 {
    padding-right: 135px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-140 {
    padding-right: 140px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-145 {
    padding-right: 145px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-150 {
    padding-right: 150px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-155 {
    padding-right: 155px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-160 {
    padding-right: 160px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-165 {
    padding-right: 165px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-170 {
    padding-right: 170px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-175 {
    padding-right: 175px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-180 {
    padding-right: 180px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-185 {
    padding-right: 185px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-190 {
    padding-right: 190px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-195 {
    padding-right: 195px !important;
    padding-left: unset !important; }

  .rtl .ps-lg-200 {
    padding-right: 200px !important;
    padding-left: unset !important; }

  .rtl .pe-lg-0 {
    padding-left: 0px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-5 {
    padding-left: 5px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-10 {
    padding-left: 10px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-15 {
    padding-left: 15px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-20 {
    padding-left: 20px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-25 {
    padding-left: 25px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-30 {
    padding-left: 30px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-35 {
    padding-left: 35px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-40 {
    padding-left: 40px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-45 {
    padding-left: 45px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-50 {
    padding-left: 50px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-55 {
    padding-left: 55px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-60 {
    padding-left: 60px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-65 {
    padding-left: 65px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-70 {
    padding-left: 70px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-75 {
    padding-left: 75px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-80 {
    padding-left: 80px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-85 {
    padding-left: 85px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-90 {
    padding-left: 90px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-95 {
    padding-left: 95px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-100 {
    padding-left: 100px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-105 {
    padding-left: 105px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-110 {
    padding-left: 110px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-115 {
    padding-left: 115px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-120 {
    padding-left: 120px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-125 {
    padding-left: 125px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-130 {
    padding-left: 130px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-135 {
    padding-left: 135px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-140 {
    padding-left: 140px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-145 {
    padding-left: 145px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-150 {
    padding-left: 150px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-155 {
    padding-left: 155px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-160 {
    padding-left: 160px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-165 {
    padding-left: 165px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-170 {
    padding-left: 170px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-175 {
    padding-left: 175px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-180 {
    padding-left: 180px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-185 {
    padding-left: 185px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-190 {
    padding-left: 190px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-195 {
    padding-left: 195px !important;
    padding-right: unset !important; }

  .rtl .pe-lg-200 {
    padding-left: 200px !important;
    padding-right: unset !important; } }
@media (min-width: 1200px) {
  .rtl .ps-xl-0 {
    padding-right: 0px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-5 {
    padding-right: 5px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-10 {
    padding-right: 10px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-15 {
    padding-right: 15px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-20 {
    padding-right: 20px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-25 {
    padding-right: 25px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-30 {
    padding-right: 30px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-35 {
    padding-right: 35px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-40 {
    padding-right: 40px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-45 {
    padding-right: 45px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-50 {
    padding-right: 50px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-55 {
    padding-right: 55px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-60 {
    padding-right: 60px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-65 {
    padding-right: 65px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-70 {
    padding-right: 70px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-75 {
    padding-right: 75px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-80 {
    padding-right: 80px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-85 {
    padding-right: 85px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-90 {
    padding-right: 90px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-95 {
    padding-right: 95px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-100 {
    padding-right: 100px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-105 {
    padding-right: 105px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-110 {
    padding-right: 110px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-115 {
    padding-right: 115px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-120 {
    padding-right: 120px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-125 {
    padding-right: 125px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-130 {
    padding-right: 130px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-135 {
    padding-right: 135px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-140 {
    padding-right: 140px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-145 {
    padding-right: 145px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-150 {
    padding-right: 150px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-155 {
    padding-right: 155px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-160 {
    padding-right: 160px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-165 {
    padding-right: 165px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-170 {
    padding-right: 170px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-175 {
    padding-right: 175px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-180 {
    padding-right: 180px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-185 {
    padding-right: 185px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-190 {
    padding-right: 190px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-195 {
    padding-right: 195px !important;
    padding-left: unset !important; }

  .rtl .ps-xl-200 {
    padding-right: 200px !important;
    padding-left: unset !important; }

  .rtl .pe-xl-0 {
    padding-left: 0px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-5 {
    padding-left: 5px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-10 {
    padding-left: 10px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-15 {
    padding-left: 15px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-20 {
    padding-left: 20px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-25 {
    padding-left: 25px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-30 {
    padding-left: 30px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-35 {
    padding-left: 35px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-40 {
    padding-left: 40px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-45 {
    padding-left: 45px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-50 {
    padding-left: 50px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-55 {
    padding-left: 55px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-60 {
    padding-left: 60px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-65 {
    padding-left: 65px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-70 {
    padding-left: 70px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-75 {
    padding-left: 75px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-80 {
    padding-left: 80px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-85 {
    padding-left: 85px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-90 {
    padding-left: 90px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-95 {
    padding-left: 95px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-100 {
    padding-left: 100px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-105 {
    padding-left: 105px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-110 {
    padding-left: 110px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-115 {
    padding-left: 115px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-120 {
    padding-left: 120px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-125 {
    padding-left: 125px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-130 {
    padding-left: 130px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-135 {
    padding-left: 135px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-140 {
    padding-left: 140px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-145 {
    padding-left: 145px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-150 {
    padding-left: 150px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-155 {
    padding-left: 155px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-160 {
    padding-left: 160px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-165 {
    padding-left: 165px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-170 {
    padding-left: 170px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-175 {
    padding-left: 175px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-180 {
    padding-left: 180px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-185 {
    padding-left: 185px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-190 {
    padding-left: 190px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-195 {
    padding-left: 195px !important;
    padding-right: unset !important; }

  .rtl .pe-xl-200 {
    padding-left: 200px !important;
    padding-right: unset !important; } }
/*******************
Margin property 
*******************/
.rtl .ms-0 {
  margin-right: 0 !important;
  margin-left: inherit !important; }
.rtl .ms-1 {
  margin-right: 0.25rem !important;
  margin-left: inherit !important; }
.rtl .ms-2 {
  margin-right: 0.5rem !important;
  margin-left: inherit !important; }
.rtl .ms-3 {
  margin-right: 1rem !important;
  margin-left: inherit !important; }
.rtl .ms-4 {
  margin-right: 1.5rem !important;
  margin-left: inherit !important; }
.rtl .me-0 {
  margin-left: 0 !important;
  margin-right: inherit !important; }
.rtl .me-1 {
  margin-left: 0.25rem !important;
  margin-right: inherit !important; }
.rtl .me-2 {
  margin-left: 0.5rem !important;
  margin-right: inherit !important; }
.rtl .me-3 {
  margin-left: 1rem !important;
  margin-right: inherit !important; }
.rtl .me-4 {
  margin-left: 1.5rem !important;
  margin-right: inherit !important; }

.rtl .ms-0 {
  margin-right: 0px !important;
  margin-left: unset !important; }

.rtl .ms-5 {
  margin-right: 5px !important;
  margin-left: unset !important; }

.rtl .ms-10 {
  margin-right: 10px !important;
  margin-left: unset !important; }

.rtl .ms-15 {
  margin-right: 15px !important;
  margin-left: unset !important; }

.rtl .ms-20 {
  margin-right: 20px !important;
  margin-left: unset !important; }

.rtl .ms-25 {
  margin-right: 25px !important;
  margin-left: unset !important; }

.rtl .ms-30 {
  margin-right: 30px !important;
  margin-left: unset !important; }

.rtl .ms-35 {
  margin-right: 35px !important;
  margin-left: unset !important; }

.rtl .ms-40 {
  margin-right: 40px !important;
  margin-left: unset !important; }

.rtl .ms-45 {
  margin-right: 45px !important;
  margin-left: unset !important; }

.rtl .ms-50 {
  margin-right: 50px !important;
  margin-left: unset !important; }

.rtl .ms-55 {
  margin-right: 55px !important;
  margin-left: unset !important; }

.rtl .ms-60 {
  margin-right: 60px !important;
  margin-left: unset !important; }

.rtl .ms-65 {
  margin-right: 65px !important;
  margin-left: unset !important; }

.rtl .ms-70 {
  margin-right: 70px !important;
  margin-left: unset !important; }

.rtl .ms-75 {
  margin-right: 75px !important;
  margin-left: unset !important; }

.rtl .ms-80 {
  margin-right: 80px !important;
  margin-left: unset !important; }

.rtl .ms-85 {
  margin-right: 85px !important;
  margin-left: unset !important; }

.rtl .ms-90 {
  margin-right: 90px !important;
  margin-left: unset !important; }

.rtl .ms-95 {
  margin-right: 95px !important;
  margin-left: unset !important; }

.rtl .ms-100 {
  margin-right: 100px !important;
  margin-left: unset !important; }

.rtl .ms-105 {
  margin-right: 105px !important;
  margin-left: unset !important; }

.rtl .ms-110 {
  margin-right: 110px !important;
  margin-left: unset !important; }

.rtl .ms-115 {
  margin-right: 115px !important;
  margin-left: unset !important; }

.rtl .ms-120 {
  margin-right: 120px !important;
  margin-left: unset !important; }

.rtl .ms-125 {
  margin-right: 125px !important;
  margin-left: unset !important; }

.rtl .ms-130 {
  margin-right: 130px !important;
  margin-left: unset !important; }

.rtl .ms-135 {
  margin-right: 135px !important;
  margin-left: unset !important; }

.rtl .ms-140 {
  margin-right: 140px !important;
  margin-left: unset !important; }

.rtl .ms-145 {
  margin-right: 145px !important;
  margin-left: unset !important; }

.rtl .ms-150 {
  margin-right: 150px !important;
  margin-left: unset !important; }

.rtl .ms-155 {
  margin-right: 155px !important;
  margin-left: unset !important; }

.rtl .ms-160 {
  margin-right: 160px !important;
  margin-left: unset !important; }

.rtl .ms-165 {
  margin-right: 165px !important;
  margin-left: unset !important; }

.rtl .ms-170 {
  margin-right: 170px !important;
  margin-left: unset !important; }

.rtl .ms-175 {
  margin-right: 175px !important;
  margin-left: unset !important; }

.rtl .ms-180 {
  margin-right: 180px !important;
  margin-left: unset !important; }

.rtl .ms-185 {
  margin-right: 185px !important;
  margin-left: unset !important; }

.rtl .ms-190 {
  margin-right: 190px !important;
  margin-left: unset !important; }

.rtl .ms-195 {
  margin-right: 195px !important;
  margin-left: unset !important; }

.rtl .ms-200 {
  margin-right: 200px !important;
  margin-left: unset !important; }

.rtl .me-0 {
  margin-left: 0px !important;
  margin-right: unset !important; }

.rtl .me-5 {
  margin-left: 5px !important;
  margin-right: unset !important; }

.rtl .me-10 {
  margin-left: 10px !important;
  margin-right: unset !important; }

.rtl .me-15 {
  margin-left: 15px !important;
  margin-right: unset !important; }

.rtl .me-20 {
  margin-left: 20px !important;
  margin-right: unset !important; }

.rtl .me-25 {
  margin-left: 25px !important;
  margin-right: unset !important; }

.rtl .me-30 {
  margin-left: 30px !important;
  margin-right: unset !important; }

.rtl .me-35 {
  margin-left: 35px !important;
  margin-right: unset !important; }

.rtl .me-40 {
  margin-left: 40px !important;
  margin-right: unset !important; }

.rtl .me-45 {
  margin-left: 45px !important;
  margin-right: unset !important; }

.rtl .me-50 {
  margin-left: 50px !important;
  margin-right: unset !important; }

.rtl .me-55 {
  margin-left: 55px !important;
  margin-right: unset !important; }

.rtl .me-60 {
  margin-left: 60px !important;
  margin-right: unset !important; }

.rtl .me-65 {
  margin-left: 65px !important;
  margin-right: unset !important; }

.rtl .me-70 {
  margin-left: 70px !important;
  margin-right: unset !important; }

.rtl .me-75 {
  margin-left: 75px !important;
  margin-right: unset !important; }

.rtl .me-80 {
  margin-left: 80px !important;
  margin-right: unset !important; }

.rtl .me-85 {
  margin-left: 85px !important;
  margin-right: unset !important; }

.rtl .me-90 {
  margin-left: 90px !important;
  margin-right: unset !important; }

.rtl .me-95 {
  margin-left: 95px !important;
  margin-right: unset !important; }

.rtl .me-100 {
  margin-left: 100px !important;
  margin-right: unset !important; }

.rtl .me-105 {
  margin-left: 105px !important;
  margin-right: unset !important; }

.rtl .me-110 {
  margin-left: 110px !important;
  margin-right: unset !important; }

.rtl .me-115 {
  margin-left: 115px !important;
  margin-right: unset !important; }

.rtl .me-120 {
  margin-left: 120px !important;
  margin-right: unset !important; }

.rtl .me-125 {
  margin-left: 125px !important;
  margin-right: unset !important; }

.rtl .me-130 {
  margin-left: 130px !important;
  margin-right: unset !important; }

.rtl .me-135 {
  margin-left: 135px !important;
  margin-right: unset !important; }

.rtl .me-140 {
  margin-left: 140px !important;
  margin-right: unset !important; }

.rtl .me-145 {
  margin-left: 145px !important;
  margin-right: unset !important; }

.rtl .me-150 {
  margin-left: 150px !important;
  margin-right: unset !important; }

.rtl .me-155 {
  margin-left: 155px !important;
  margin-right: unset !important; }

.rtl .me-160 {
  margin-left: 160px !important;
  margin-right: unset !important; }

.rtl .me-165 {
  margin-left: 165px !important;
  margin-right: unset !important; }

.rtl .me-170 {
  margin-left: 170px !important;
  margin-right: unset !important; }

.rtl .me-175 {
  margin-left: 175px !important;
  margin-right: unset !important; }

.rtl .me-180 {
  margin-left: 180px !important;
  margin-right: unset !important; }

.rtl .me-185 {
  margin-left: 185px !important;
  margin-right: unset !important; }

.rtl .me-190 {
  margin-left: 190px !important;
  margin-right: unset !important; }

.rtl .me-195 {
  margin-left: 195px !important;
  margin-right: unset !important; }

.rtl .me-200 {
  margin-left: 200px !important;
  margin-right: unset !important; }

@media (max-width: 575px) {
  .rtl .ms-xs-0 {
    margin-right: 0px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-5 {
    margin-right: 5px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-10 {
    margin-right: 10px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-15 {
    margin-right: 15px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-20 {
    margin-right: 20px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-25 {
    margin-right: 25px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-30 {
    margin-right: 30px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-35 {
    margin-right: 35px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-40 {
    margin-right: 40px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-45 {
    margin-right: 45px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-50 {
    margin-right: 50px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-55 {
    margin-right: 55px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-60 {
    margin-right: 60px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-65 {
    margin-right: 65px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-70 {
    margin-right: 70px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-75 {
    margin-right: 75px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-80 {
    margin-right: 80px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-85 {
    margin-right: 85px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-90 {
    margin-right: 90px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-95 {
    margin-right: 95px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-100 {
    margin-right: 100px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-105 {
    margin-right: 105px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-110 {
    margin-right: 110px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-115 {
    margin-right: 115px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-120 {
    margin-right: 120px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-125 {
    margin-right: 125px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-130 {
    margin-right: 130px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-135 {
    margin-right: 135px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-140 {
    margin-right: 140px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-145 {
    margin-right: 145px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-150 {
    margin-right: 150px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-155 {
    margin-right: 155px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-160 {
    margin-right: 160px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-165 {
    margin-right: 165px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-170 {
    margin-right: 170px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-175 {
    margin-right: 175px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-180 {
    margin-right: 180px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-185 {
    margin-right: 185px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-190 {
    margin-right: 190px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-195 {
    margin-right: 195px !important;
    margin-left: unset !important; }

  .rtl .ms-xs-200 {
    margin-right: 200px !important;
    margin-left: unset !important; }

  .rtl .me-xs-0 {
    margin-left: 0px !important;
    margin-right: unset !important; }

  .rtl .me-xs-5 {
    margin-left: 5px !important;
    margin-right: unset !important; }

  .rtl .me-xs-10 {
    margin-left: 10px !important;
    margin-right: unset !important; }

  .rtl .me-xs-15 {
    margin-left: 15px !important;
    margin-right: unset !important; }

  .rtl .me-xs-20 {
    margin-left: 20px !important;
    margin-right: unset !important; }

  .rtl .me-xs-25 {
    margin-left: 25px !important;
    margin-right: unset !important; }

  .rtl .me-xs-30 {
    margin-left: 30px !important;
    margin-right: unset !important; }

  .rtl .me-xs-35 {
    margin-left: 35px !important;
    margin-right: unset !important; }

  .rtl .me-xs-40 {
    margin-left: 40px !important;
    margin-right: unset !important; }

  .rtl .me-xs-45 {
    margin-left: 45px !important;
    margin-right: unset !important; }

  .rtl .me-xs-50 {
    margin-left: 50px !important;
    margin-right: unset !important; }

  .rtl .me-xs-55 {
    margin-left: 55px !important;
    margin-right: unset !important; }

  .rtl .me-xs-60 {
    margin-left: 60px !important;
    margin-right: unset !important; }

  .rtl .me-xs-65 {
    margin-left: 65px !important;
    margin-right: unset !important; }

  .rtl .me-xs-70 {
    margin-left: 70px !important;
    margin-right: unset !important; }

  .rtl .me-xs-75 {
    margin-left: 75px !important;
    margin-right: unset !important; }

  .rtl .me-xs-80 {
    margin-left: 80px !important;
    margin-right: unset !important; }

  .rtl .me-xs-85 {
    margin-left: 85px !important;
    margin-right: unset !important; }

  .rtl .me-xs-90 {
    margin-left: 90px !important;
    margin-right: unset !important; }

  .rtl .me-xs-95 {
    margin-left: 95px !important;
    margin-right: unset !important; }

  .rtl .me-xs-100 {
    margin-left: 100px !important;
    margin-right: unset !important; }

  .rtl .me-xs-105 {
    margin-left: 105px !important;
    margin-right: unset !important; }

  .rtl .me-xs-110 {
    margin-left: 110px !important;
    margin-right: unset !important; }

  .rtl .me-xs-115 {
    margin-left: 115px !important;
    margin-right: unset !important; }

  .rtl .me-xs-120 {
    margin-left: 120px !important;
    margin-right: unset !important; }

  .rtl .me-xs-125 {
    margin-left: 125px !important;
    margin-right: unset !important; }

  .rtl .me-xs-130 {
    margin-left: 130px !important;
    margin-right: unset !important; }

  .rtl .me-xs-135 {
    margin-left: 135px !important;
    margin-right: unset !important; }

  .rtl .me-xs-140 {
    margin-left: 140px !important;
    margin-right: unset !important; }

  .rtl .me-xs-145 {
    margin-left: 145px !important;
    margin-right: unset !important; }

  .rtl .me-xs-150 {
    margin-left: 150px !important;
    margin-right: unset !important; }

  .rtl .me-xs-155 {
    margin-left: 155px !important;
    margin-right: unset !important; }

  .rtl .me-xs-160 {
    margin-left: 160px !important;
    margin-right: unset !important; }

  .rtl .me-xs-165 {
    margin-left: 165px !important;
    margin-right: unset !important; }

  .rtl .me-xs-170 {
    margin-left: 170px !important;
    margin-right: unset !important; }

  .rtl .me-xs-175 {
    margin-left: 175px !important;
    margin-right: unset !important; }

  .rtl .me-xs-180 {
    margin-left: 180px !important;
    margin-right: unset !important; }

  .rtl .me-xs-185 {
    margin-left: 185px !important;
    margin-right: unset !important; }

  .rtl .me-xs-190 {
    margin-left: 190px !important;
    margin-right: unset !important; }

  .rtl .me-xs-195 {
    margin-left: 195px !important;
    margin-right: unset !important; }

  .rtl .me-xs-200 {
    margin-left: 200px !important;
    margin-right: unset !important; } }
@media (min-width: 576px) {
  .rtl .ms-sm-0 {
    margin-right: 0px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-5 {
    margin-right: 5px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-10 {
    margin-right: 10px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-15 {
    margin-right: 15px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-20 {
    margin-right: 20px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-25 {
    margin-right: 25px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-30 {
    margin-right: 30px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-35 {
    margin-right: 35px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-40 {
    margin-right: 40px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-45 {
    margin-right: 45px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-50 {
    margin-right: 50px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-55 {
    margin-right: 55px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-60 {
    margin-right: 60px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-65 {
    margin-right: 65px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-70 {
    margin-right: 70px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-75 {
    margin-right: 75px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-80 {
    margin-right: 80px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-85 {
    margin-right: 85px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-90 {
    margin-right: 90px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-95 {
    margin-right: 95px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-100 {
    margin-right: 100px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-105 {
    margin-right: 105px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-110 {
    margin-right: 110px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-115 {
    margin-right: 115px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-120 {
    margin-right: 120px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-125 {
    margin-right: 125px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-130 {
    margin-right: 130px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-135 {
    margin-right: 135px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-140 {
    margin-right: 140px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-145 {
    margin-right: 145px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-150 {
    margin-right: 150px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-155 {
    margin-right: 155px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-160 {
    margin-right: 160px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-165 {
    margin-right: 165px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-170 {
    margin-right: 170px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-175 {
    margin-right: 175px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-180 {
    margin-right: 180px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-185 {
    margin-right: 185px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-190 {
    margin-right: 190px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-195 {
    margin-right: 195px !important;
    margin-left: unset !important; }

  .rtl .ms-sm-200 {
    margin-right: 200px !important;
    margin-left: unset !important; }

  .rtl .me-sm-0 {
    margin-left: 0px !important;
    margin-right: unset !important; }

  .rtl .me-sm-5 {
    margin-left: 5px !important;
    margin-right: unset !important; }

  .rtl .me-sm-10 {
    margin-left: 10px !important;
    margin-right: unset !important; }

  .rtl .me-sm-15 {
    margin-left: 15px !important;
    margin-right: unset !important; }

  .rtl .me-sm-20 {
    margin-left: 20px !important;
    margin-right: unset !important; }

  .rtl .me-sm-25 {
    margin-left: 25px !important;
    margin-right: unset !important; }

  .rtl .me-sm-30 {
    margin-left: 30px !important;
    margin-right: unset !important; }

  .rtl .me-sm-35 {
    margin-left: 35px !important;
    margin-right: unset !important; }

  .rtl .me-sm-40 {
    margin-left: 40px !important;
    margin-right: unset !important; }

  .rtl .me-sm-45 {
    margin-left: 45px !important;
    margin-right: unset !important; }

  .rtl .me-sm-50 {
    margin-left: 50px !important;
    margin-right: unset !important; }

  .rtl .me-sm-55 {
    margin-left: 55px !important;
    margin-right: unset !important; }

  .rtl .me-sm-60 {
    margin-left: 60px !important;
    margin-right: unset !important; }

  .rtl .me-sm-65 {
    margin-left: 65px !important;
    margin-right: unset !important; }

  .rtl .me-sm-70 {
    margin-left: 70px !important;
    margin-right: unset !important; }

  .rtl .me-sm-75 {
    margin-left: 75px !important;
    margin-right: unset !important; }

  .rtl .me-sm-80 {
    margin-left: 80px !important;
    margin-right: unset !important; }

  .rtl .me-sm-85 {
    margin-left: 85px !important;
    margin-right: unset !important; }

  .rtl .me-sm-90 {
    margin-left: 90px !important;
    margin-right: unset !important; }

  .rtl .me-sm-95 {
    margin-left: 95px !important;
    margin-right: unset !important; }

  .rtl .me-sm-100 {
    margin-left: 100px !important;
    margin-right: unset !important; }

  .rtl .me-sm-105 {
    margin-left: 105px !important;
    margin-right: unset !important; }

  .rtl .me-sm-110 {
    margin-left: 110px !important;
    margin-right: unset !important; }

  .rtl .me-sm-115 {
    margin-left: 115px !important;
    margin-right: unset !important; }

  .rtl .me-sm-120 {
    margin-left: 120px !important;
    margin-right: unset !important; }

  .rtl .me-sm-125 {
    margin-left: 125px !important;
    margin-right: unset !important; }

  .rtl .me-sm-130 {
    margin-left: 130px !important;
    margin-right: unset !important; }

  .rtl .me-sm-135 {
    margin-left: 135px !important;
    margin-right: unset !important; }

  .rtl .me-sm-140 {
    margin-left: 140px !important;
    margin-right: unset !important; }

  .rtl .me-sm-145 {
    margin-left: 145px !important;
    margin-right: unset !important; }

  .rtl .me-sm-150 {
    margin-left: 150px !important;
    margin-right: unset !important; }

  .rtl .me-sm-155 {
    margin-left: 155px !important;
    margin-right: unset !important; }

  .rtl .me-sm-160 {
    margin-left: 160px !important;
    margin-right: unset !important; }

  .rtl .me-sm-165 {
    margin-left: 165px !important;
    margin-right: unset !important; }

  .rtl .me-sm-170 {
    margin-left: 170px !important;
    margin-right: unset !important; }

  .rtl .me-sm-175 {
    margin-left: 175px !important;
    margin-right: unset !important; }

  .rtl .me-sm-180 {
    margin-left: 180px !important;
    margin-right: unset !important; }

  .rtl .me-sm-185 {
    margin-left: 185px !important;
    margin-right: unset !important; }

  .rtl .me-sm-190 {
    margin-left: 190px !important;
    margin-right: unset !important; }

  .rtl .me-sm-195 {
    margin-left: 195px !important;
    margin-right: unset !important; }

  .rtl .me-sm-200 {
    margin-left: 200px !important;
    margin-right: unset !important; } }
@media (min-width: 768px) {
  .rtl .ms-md-0 {
    margin-right: 0px !important;
    margin-left: unset !important; }

  .rtl .ms-md-5 {
    margin-right: 5px !important;
    margin-left: unset !important; }

  .rtl .ms-md-10 {
    margin-right: 10px !important;
    margin-left: unset !important; }

  .rtl .ms-md-15 {
    margin-right: 15px !important;
    margin-left: unset !important; }

  .rtl .ms-md-20 {
    margin-right: 20px !important;
    margin-left: unset !important; }

  .rtl .ms-md-25 {
    margin-right: 25px !important;
    margin-left: unset !important; }

  .rtl .ms-md-30 {
    margin-right: 30px !important;
    margin-left: unset !important; }

  .rtl .ms-md-35 {
    margin-right: 35px !important;
    margin-left: unset !important; }

  .rtl .ms-md-40 {
    margin-right: 40px !important;
    margin-left: unset !important; }

  .rtl .ms-md-45 {
    margin-right: 45px !important;
    margin-left: unset !important; }

  .rtl .ms-md-50 {
    margin-right: 50px !important;
    margin-left: unset !important; }

  .rtl .ms-md-55 {
    margin-right: 55px !important;
    margin-left: unset !important; }

  .rtl .ms-md-60 {
    margin-right: 60px !important;
    margin-left: unset !important; }

  .rtl .ms-md-65 {
    margin-right: 65px !important;
    margin-left: unset !important; }

  .rtl .ms-md-70 {
    margin-right: 70px !important;
    margin-left: unset !important; }

  .rtl .ms-md-75 {
    margin-right: 75px !important;
    margin-left: unset !important; }

  .rtl .ms-md-80 {
    margin-right: 80px !important;
    margin-left: unset !important; }

  .rtl .ms-md-85 {
    margin-right: 85px !important;
    margin-left: unset !important; }

  .rtl .ms-md-90 {
    margin-right: 90px !important;
    margin-left: unset !important; }

  .rtl .ms-md-95 {
    margin-right: 95px !important;
    margin-left: unset !important; }

  .rtl .ms-md-100 {
    margin-right: 100px !important;
    margin-left: unset !important; }

  .rtl .ms-md-105 {
    margin-right: 105px !important;
    margin-left: unset !important; }

  .rtl .ms-md-110 {
    margin-right: 110px !important;
    margin-left: unset !important; }

  .rtl .ms-md-115 {
    margin-right: 115px !important;
    margin-left: unset !important; }

  .rtl .ms-md-120 {
    margin-right: 120px !important;
    margin-left: unset !important; }

  .rtl .ms-md-125 {
    margin-right: 125px !important;
    margin-left: unset !important; }

  .rtl .ms-md-130 {
    margin-right: 130px !important;
    margin-left: unset !important; }

  .rtl .ms-md-135 {
    margin-right: 135px !important;
    margin-left: unset !important; }

  .rtl .ms-md-140 {
    margin-right: 140px !important;
    margin-left: unset !important; }

  .rtl .ms-md-145 {
    margin-right: 145px !important;
    margin-left: unset !important; }

  .rtl .ms-md-150 {
    margin-right: 150px !important;
    margin-left: unset !important; }

  .rtl .ms-md-155 {
    margin-right: 155px !important;
    margin-left: unset !important; }

  .rtl .ms-md-160 {
    margin-right: 160px !important;
    margin-left: unset !important; }

  .rtl .ms-md-165 {
    margin-right: 165px !important;
    margin-left: unset !important; }

  .rtl .ms-md-170 {
    margin-right: 170px !important;
    margin-left: unset !important; }

  .rtl .ms-md-175 {
    margin-right: 175px !important;
    margin-left: unset !important; }

  .rtl .ms-md-180 {
    margin-right: 180px !important;
    margin-left: unset !important; }

  .rtl .ms-md-185 {
    margin-right: 185px !important;
    margin-left: unset !important; }

  .rtl .ms-md-190 {
    margin-right: 190px !important;
    margin-left: unset !important; }

  .rtl .ms-md-195 {
    margin-right: 195px !important;
    margin-left: unset !important; }

  .rtl .ms-md-200 {
    margin-right: 200px !important;
    margin-left: unset !important; }

  .rtl .me-md-0 {
    margin-left: 0px !important;
    margin-right: unset !important; }

  .rtl .me-md-5 {
    margin-left: 5px !important;
    margin-right: unset !important; }

  .rtl .me-md-10 {
    margin-left: 10px !important;
    margin-right: unset !important; }

  .rtl .me-md-15 {
    margin-left: 15px !important;
    margin-right: unset !important; }

  .rtl .me-md-20 {
    margin-left: 20px !important;
    margin-right: unset !important; }

  .rtl .me-md-25 {
    margin-left: 25px !important;
    margin-right: unset !important; }

  .rtl .me-md-30 {
    margin-left: 30px !important;
    margin-right: unset !important; }

  .rtl .me-md-35 {
    margin-left: 35px !important;
    margin-right: unset !important; }

  .rtl .me-md-40 {
    margin-left: 40px !important;
    margin-right: unset !important; }

  .rtl .me-md-45 {
    margin-left: 45px !important;
    margin-right: unset !important; }

  .rtl .me-md-50 {
    margin-left: 50px !important;
    margin-right: unset !important; }

  .rtl .me-md-55 {
    margin-left: 55px !important;
    margin-right: unset !important; }

  .rtl .me-md-60 {
    margin-left: 60px !important;
    margin-right: unset !important; }

  .rtl .me-md-65 {
    margin-left: 65px !important;
    margin-right: unset !important; }

  .rtl .me-md-70 {
    margin-left: 70px !important;
    margin-right: unset !important; }

  .rtl .me-md-75 {
    margin-left: 75px !important;
    margin-right: unset !important; }

  .rtl .me-md-80 {
    margin-left: 80px !important;
    margin-right: unset !important; }

  .rtl .me-md-85 {
    margin-left: 85px !important;
    margin-right: unset !important; }

  .rtl .me-md-90 {
    margin-left: 90px !important;
    margin-right: unset !important; }

  .rtl .me-md-95 {
    margin-left: 95px !important;
    margin-right: unset !important; }

  .rtl .me-md-100 {
    margin-left: 100px !important;
    margin-right: unset !important; } }
@media (min-width: 992px) {
  .rtl .ms-lg-0 {
    margin-right: 0px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-5 {
    margin-right: 5px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-10 {
    margin-right: 10px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-15 {
    margin-right: 15px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-20 {
    margin-right: 20px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-25 {
    margin-right: 25px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-30 {
    margin-right: 30px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-35 {
    margin-right: 35px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-40 {
    margin-right: 40px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-45 {
    margin-right: 45px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-50 {
    margin-right: 50px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-55 {
    margin-right: 55px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-60 {
    margin-right: 60px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-65 {
    margin-right: 65px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-70 {
    margin-right: 70px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-75 {
    margin-right: 75px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-80 {
    margin-right: 80px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-85 {
    margin-right: 85px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-90 {
    margin-right: 90px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-95 {
    margin-right: 95px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-100 {
    margin-right: 100px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-105 {
    margin-right: 105px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-110 {
    margin-right: 110px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-115 {
    margin-right: 115px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-120 {
    margin-right: 120px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-125 {
    margin-right: 125px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-130 {
    margin-right: 130px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-135 {
    margin-right: 135px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-140 {
    margin-right: 140px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-145 {
    margin-right: 145px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-150 {
    margin-right: 150px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-155 {
    margin-right: 155px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-160 {
    margin-right: 160px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-165 {
    margin-right: 165px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-170 {
    margin-right: 170px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-175 {
    margin-right: 175px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-180 {
    margin-right: 180px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-185 {
    margin-right: 185px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-190 {
    margin-right: 190px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-195 {
    margin-right: 195px !important;
    margin-left: unset !important; }

  .rtl .ms-lg-200 {
    margin-right: 200px !important;
    margin-left: unset !important; }

  .rtl .me-lg-0 {
    margin-left: 0px !important;
    margin-right: unset !important; }

  .rtl .me-lg-5 {
    margin-left: 5px !important;
    margin-right: unset !important; }

  .rtl .me-lg-10 {
    margin-left: 10px !important;
    margin-right: unset !important; }

  .rtl .me-lg-15 {
    margin-left: 15px !important;
    margin-right: unset !important; }

  .rtl .me-lg-20 {
    margin-left: 20px !important;
    margin-right: unset !important; }

  .rtl .me-lg-25 {
    margin-left: 25px !important;
    margin-right: unset !important; }

  .rtl .me-lg-30 {
    margin-left: 30px !important;
    margin-right: unset !important; }

  .rtl .me-lg-35 {
    margin-left: 35px !important;
    margin-right: unset !important; }

  .rtl .me-lg-40 {
    margin-left: 40px !important;
    margin-right: unset !important; }

  .rtl .me-lg-45 {
    margin-left: 45px !important;
    margin-right: unset !important; }

  .rtl .me-lg-50 {
    margin-left: 50px !important;
    margin-right: unset !important; }

  .rtl .me-lg-55 {
    margin-left: 55px !important;
    margin-right: unset !important; }

  .rtl .me-lg-60 {
    margin-left: 60px !important;
    margin-right: unset !important; }

  .rtl .me-lg-65 {
    margin-left: 65px !important;
    margin-right: unset !important; }

  .rtl .me-lg-70 {
    margin-left: 70px !important;
    margin-right: unset !important; }

  .rtl .me-lg-75 {
    margin-left: 75px !important;
    margin-right: unset !important; }

  .rtl .me-lg-80 {
    margin-left: 80px !important;
    margin-right: unset !important; }

  .rtl .me-lg-85 {
    margin-left: 85px !important;
    margin-right: unset !important; }

  .rtl .me-lg-90 {
    margin-left: 90px !important;
    margin-right: unset !important; }

  .rtl .me-lg-95 {
    margin-left: 95px !important;
    margin-right: unset !important; }

  .rtl .me-lg-100 {
    margin-left: 100px !important;
    margin-right: unset !important; }

  .rtl .me-lg-105 {
    margin-left: 105px !important;
    margin-right: unset !important; }

  .rtl .me-lg-110 {
    margin-left: 110px !important;
    margin-right: unset !important; }

  .rtl .me-lg-115 {
    margin-left: 115px !important;
    margin-right: unset !important; }

  .rtl .me-lg-120 {
    margin-left: 120px !important;
    margin-right: unset !important; }

  .rtl .me-lg-125 {
    margin-left: 125px !important;
    margin-right: unset !important; }

  .rtl .me-lg-130 {
    margin-left: 130px !important;
    margin-right: unset !important; }

  .rtl .me-lg-135 {
    margin-left: 135px !important;
    margin-right: unset !important; }

  .rtl .me-lg-140 {
    margin-left: 140px !important;
    margin-right: unset !important; }

  .rtl .me-lg-145 {
    margin-left: 145px !important;
    margin-right: unset !important; }

  .rtl .me-lg-150 {
    margin-left: 150px !important;
    margin-right: unset !important; }

  .rtl .me-lg-155 {
    margin-left: 155px !important;
    margin-right: unset !important; }

  .rtl .me-lg-160 {
    margin-left: 160px !important;
    margin-right: unset !important; }

  .rtl .me-lg-165 {
    margin-left: 165px !important;
    margin-right: unset !important; }

  .rtl .me-lg-170 {
    margin-left: 170px !important;
    margin-right: unset !important; }

  .rtl .me-lg-175 {
    margin-left: 175px !important;
    margin-right: unset !important; }

  .rtl .me-lg-180 {
    margin-left: 180px !important;
    margin-right: unset !important; }

  .rtl .me-lg-185 {
    margin-left: 185px !important;
    margin-right: unset !important; }

  .rtl .me-lg-190 {
    margin-left: 190px !important;
    margin-right: unset !important; }

  .rtl .me-lg-195 {
    margin-left: 195px !important;
    margin-right: unset !important; }

  .rtl .me-lg-200 {
    margin-left: 200px !important;
    margin-right: unset !important; } }
@media (min-width: 1200px) {
  .rtl .ms-xl-0 {
    margin-right: 0px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-5 {
    margin-right: 5px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-10 {
    margin-right: 10px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-15 {
    margin-right: 15px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-20 {
    margin-right: 20px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-25 {
    margin-right: 25px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-30 {
    margin-right: 30px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-35 {
    margin-right: 35px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-40 {
    margin-right: 40px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-45 {
    margin-right: 45px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-50 {
    margin-right: 50px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-55 {
    margin-right: 55px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-60 {
    margin-right: 60px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-65 {
    margin-right: 65px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-70 {
    margin-right: 70px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-75 {
    margin-right: 75px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-80 {
    margin-right: 80px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-85 {
    margin-right: 85px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-90 {
    margin-right: 90px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-95 {
    margin-right: 95px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-100 {
    margin-right: 100px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-105 {
    margin-right: 105px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-110 {
    margin-right: 110px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-115 {
    margin-right: 115px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-120 {
    margin-right: 120px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-125 {
    margin-right: 125px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-130 {
    margin-right: 130px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-135 {
    margin-right: 135px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-140 {
    margin-right: 140px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-145 {
    margin-right: 145px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-150 {
    margin-right: 150px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-155 {
    margin-right: 155px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-160 {
    margin-right: 160px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-165 {
    margin-right: 165px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-170 {
    margin-right: 170px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-175 {
    margin-right: 175px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-180 {
    margin-right: 180px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-185 {
    margin-right: 185px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-190 {
    margin-right: 190px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-195 {
    margin-right: 195px !important;
    margin-left: unset !important; }

  .rtl .ms-xl-200 {
    margin-right: 200px !important;
    margin-left: unset !important; }

  .rtl .me-xl-0 {
    margin-left: 0px !important;
    margin-right: unset !important; }

  .rtl .me-xl-5 {
    margin-left: 5px !important;
    margin-right: unset !important; }

  .rtl .me-xl-10 {
    margin-left: 10px !important;
    margin-right: unset !important; }

  .rtl .me-xl-15 {
    margin-left: 15px !important;
    margin-right: unset !important; }

  .rtl .me-xl-20 {
    margin-left: 20px !important;
    margin-right: unset !important; }

  .rtl .me-xl-25 {
    margin-left: 25px !important;
    margin-right: unset !important; }

  .rtl .me-xl-30 {
    margin-left: 30px !important;
    margin-right: unset !important; }

  .rtl .me-xl-35 {
    margin-left: 35px !important;
    margin-right: unset !important; }

  .rtl .me-xl-40 {
    margin-left: 40px !important;
    margin-right: unset !important; }

  .rtl .me-xl-45 {
    margin-left: 45px !important;
    margin-right: unset !important; }

  .rtl .me-xl-50 {
    margin-left: 50px !important;
    margin-right: unset !important; }

  .rtl .me-xl-55 {
    margin-left: 55px !important;
    margin-right: unset !important; }

  .rtl .me-xl-60 {
    margin-left: 60px !important;
    margin-right: unset !important; }

  .rtl .me-xl-65 {
    margin-left: 65px !important;
    margin-right: unset !important; }

  .rtl .me-xl-70 {
    margin-left: 70px !important;
    margin-right: unset !important; }

  .rtl .me-xl-75 {
    margin-left: 75px !important;
    margin-right: unset !important; }

  .rtl .me-xl-80 {
    margin-left: 80px !important;
    margin-right: unset !important; }

  .rtl .me-xl-85 {
    margin-left: 85px !important;
    margin-right: unset !important; }

  .rtl .me-xl-90 {
    margin-left: 90px !important;
    margin-right: unset !important; }

  .rtl .me-xl-95 {
    margin-left: 95px !important;
    margin-right: unset !important; }

  .rtl .me-xl-100 {
    margin-left: 100px !important;
    margin-right: unset !important; }

  .rtl .me-xl-105 {
    margin-left: 105px !important;
    margin-right: unset !important; }

  .rtl .me-xl-110 {
    margin-left: 110px !important;
    margin-right: unset !important; }

  .rtl .me-xl-115 {
    margin-left: 115px !important;
    margin-right: unset !important; }

  .rtl .me-xl-120 {
    margin-left: 120px !important;
    margin-right: unset !important; }

  .rtl .me-xl-125 {
    margin-left: 125px !important;
    margin-right: unset !important; }

  .rtl .me-xl-130 {
    margin-left: 130px !important;
    margin-right: unset !important; }

  .rtl .me-xl-135 {
    margin-left: 135px !important;
    margin-right: unset !important; }

  .rtl .me-xl-140 {
    margin-left: 140px !important;
    margin-right: unset !important; }

  .rtl .me-xl-145 {
    margin-left: 145px !important;
    margin-right: unset !important; }

  .rtl .me-xl-150 {
    margin-left: 150px !important;
    margin-right: unset !important; }

  .rtl .me-xl-155 {
    margin-left: 155px !important;
    margin-right: unset !important; }

  .rtl .me-xl-160 {
    margin-left: 160px !important;
    margin-right: unset !important; }

  .rtl .me-xl-165 {
    margin-left: 165px !important;
    margin-right: unset !important; }

  .rtl .me-xl-170 {
    margin-left: 170px !important;
    margin-right: unset !important; }

  .rtl .me-xl-175 {
    margin-left: 175px !important;
    margin-right: unset !important; }

  .rtl .me-xl-180 {
    margin-left: 180px !important;
    margin-right: unset !important; }

  .rtl .me-xl-185 {
    margin-left: 185px !important;
    margin-right: unset !important; }

  .rtl .me-xl-190 {
    margin-left: 190px !important;
    margin-right: unset !important; }

  .rtl .me-xl-195 {
    margin-left: 195px !important;
    margin-right: unset !important; }

  .rtl .me-xl-200 {
    margin-left: 200px !important;
    margin-right: unset !important; } }
.rtl .offset-1 {
  margin-right: 8.333333%;
  margin-left: 0; }
.rtl .offset-2 {
  margin-right: 16.666667%;
  margin-left: 0; }
.rtl .offset-3 {
  margin-right: 25%;
  margin-left: 0; }
.rtl .offset-4 {
  margin-right: 33.333333%;
  margin-left: 0; }
.rtl .offset-5 {
  margin-right: 41.666667%;
  margin-left: 0; }
.rtl .offset-6 {
  margin-right: 50%;
  margin-left: 0; }
.rtl .offset-7 {
  margin-right: 58.333333%;
  margin-left: 0; }
.rtl .offset-8 {
  margin-right: 66.666667%;
  margin-left: 0; }
.rtl .offset-9 {
  margin-right: 75%;
  margin-left: 0; }
.rtl .offset-10 {
  margin-right: 83.333333%;
  margin-left: 0; }
.rtl .offset-11 {
  margin-right: 91.666667%;
  margin-left: 0; }

@media (min-width: 576px) {
  .rtl .offset-sm-0 {
    margin-right: 0;
    margin-left: 0; }
  .rtl .offset-sm-1 {
    margin-right: 8.333333%;
    margin-left: 0; }
  .rtl .offset-sm-2 {
    margin-right: 16.666667%;
    margin-left: 0; }
  .rtl .offset-sm-3 {
    margin-right: 25%;
    margin-left: 0; }
  .rtl .offset-sm-4 {
    margin-right: 33.333333%;
    margin-left: 0; }
  .rtl .offset-sm-5 {
    margin-right: 41.666667%;
    margin-left: 0; }
  .rtl .offset-sm-6 {
    margin-right: 50%;
    margin-left: 0; }
  .rtl .offset-sm-7 {
    margin-right: 58.333333%;
    margin-left: 0; }
  .rtl .offset-sm-8 {
    margin-right: 66.666667%;
    margin-left: 0; }
  .rtl .offset-sm-9 {
    margin-right: 75%;
    margin-left: 0; }
  .rtl .offset-sm-10 {
    margin-right: 83.333333%;
    margin-left: 0; }
  .rtl .offset-sm-11 {
    margin-right: 91.666667%;
    margin-left: 0; } }
@media (min-width: 768px) {
  .rtl .offset-md-0 {
    margin-right: 0;
    margin-left: 0; }
  .rtl .offset-md-1 {
    margin-right: 8.333333%;
    margin-left: 0; }
  .rtl .offset-md-2 {
    margin-right: 16.666667%;
    margin-left: 0; }
  .rtl .offset-md-3 {
    margin-right: 25%;
    margin-left: 0; }
  .rtl .offset-md-4 {
    margin-right: 33.333333%;
    margin-left: 0; }
  .rtl .offset-md-5 {
    margin-right: 41.666667%;
    margin-left: 0; }
  .rtl .offset-md-6 {
    margin-right: 50%;
    margin-left: 0; }
  .rtl .offset-md-7 {
    margin-right: 58.333333%;
    margin-left: 0; }
  .rtl .offset-md-8 {
    margin-right: 66.666667%;
    margin-left: 0; }
  .rtl .offset-md-9 {
    margin-right: 75%;
    margin-left: 0; }
  .rtl .offset-md-10 {
    margin-right: 83.333333%;
    margin-left: 0; }
  .rtl .offset-md-11 {
    margin-right: 91.666667%;
    margin-left: 0; } }
@media (min-width: 992px) {
  .rtl .offset-lg-0 {
    margin-right: 0;
    margin-left: 0; }
  .rtl .offset-lg-1 {
    margin-right: 8.333333%;
    margin-left: 0; }
  .rtl .offset-lg-2 {
    margin-right: 16.666667%;
    margin-left: 0; }
  .rtl .offset-lg-3 {
    margin-right: 25%;
    margin-left: 0; }
  .rtl .offset-lg-4 {
    margin-right: 33.333333%;
    margin-left: 0; }
  .rtl .offset-lg-5 {
    margin-right: 41.666667%;
    margin-left: 0; }
  .rtl .offset-lg-6 {
    margin-right: 50%;
    margin-left: 0; }
  .rtl .offset-lg-7 {
    margin-right: 58.333333%;
    margin-left: 0; }
  .rtl .offset-lg-8 {
    margin-right: 66.666667%;
    margin-left: 0; }
  .rtl .offset-lg-9 {
    margin-right: 75%;
    margin-left: 0; }
  .rtl .offset-lg-10 {
    margin-right: 83.333333%;
    margin-left: 0; }
  .rtl .offset-lg-11 {
    margin-right: 91.666667%;
    margin-left: 0; } }
@media (min-width: 1200px) {
  .rtl .offset-xl-0 {
    margin-right: 0;
    margin-left: 0; }
  .rtl .offset-xl-1 {
    margin-right: 8.333333%;
    margin-left: 0; }
  .rtl .offset-xl-2 {
    margin-right: 16.666667%;
    margin-left: 0; }
  .rtl .offset-xl-3 {
    margin-right: 25%;
    margin-left: 0; }
  .rtl .offset-xl-4 {
    margin-right: 33.333333%;
    margin-left: 0; }
  .rtl .offset-xl-5 {
    margin-right: 41.666667%;
    margin-left: 0; }
  .rtl .offset-xl-6 {
    margin-right: 50%;
    margin-left: 0; }
  .rtl .offset-xl-7 {
    margin-right: 58.333333%;
    margin-left: 0; }
  .rtl .offset-xl-8 {
    margin-right: 66.666667%;
    margin-left: 0; }
  .rtl .offset-xl-9 {
    margin-right: 75%;
    margin-left: 0; }
  .rtl .offset-xl-10 {
    margin-right: 83.333333%;
    margin-left: 0; }
  .rtl .offset-xl-11 {
    margin-right: 91.666667%;
    margin-left: 0; } }
.rtl .me-auto {
  margin-left: auto !important;
  margin-right: inherit !important; }
.rtl .ms-auto {
  margin-right: auto !important;
  margin-left: inherit !important; }

.rtl .text-start {
  text-align: right !important; }
.rtl .text-end {
  text-align: left !important; }
@media (min-width: 576px) {
  .rtl .text-sm-start {
    text-align: right !important; }
  .rtl .text-sm-end {
    text-align: left !important; } }
@media (min-width: 768px) {
  .rtl .text-md-start {
    text-align: right !important; }
  .rtl .text-md-end {
    text-align: left !important; } }
@media (min-width: 992px) {
  .rtl .text-lg-start {
    text-align: right !important; }
  .rtl .text-lg-end {
    text-align: left !important; } }
@media (min-width: 1200px) {
  .rtl .text-xl-start {
    text-align: right !important; }
  .rtl .text-xl-end {
    text-align: left !important; } }
@media (min-width: 1440px) {
  .rtl .text-xxl-start {
    text-align: right !important; }
  .rtl .text-xxl-end {
    text-align: left !important; } }
@media (min-width: 1599px) {
  .rtl .text-xxxl-start {
    text-align: right !important; }
  .rtl .text-xxxl-end {
    text-align: left !important; } }

@media (max-width: 575px) {
  .rtl .xs-r-0 {
    left: 0rem!important; }
  .rtl .xs-r-10 {
    left: 0.7142857143rem!important; }
  .rtl .xs-r-12 {
    left: 0.8571428571rem!important; }
  .rtl .xs-r-14 {
    left: 1rem!important; }
  .rtl .xs-r-16 {
    left: 1.1428571429rem!important; }
  .rtl .xs-r-18 {
    left: 1.2857142857rem!important; }
  .rtl .xs-r-20 {
    left: 1.4285714286rem!important; }
  .rtl .xs-r-22 {
    left: 1.5714285714rem!important; }
  .rtl .xs-r-24 {
    left: 1.7142857143rem!important; }
  .rtl .xs-r-26 {
    left: 1.8571428571rem!important; }
  .rtl .xs-r-30 {
    left: 2.1428571429rem!important; }
  .rtl .xs-r-32 {
    left: 2.2857142857rem!important; }
  .rtl .xs-r-36 {
    left: 2.5714285714rem!important; }
  .rtl .xs-r-38 {
    left: 2.7142857143rem!important; }
  .rtl .xs-r-40 {
    left: 2.8571428571rem!important; }
  .rtl .xs-r-42 {
    left: 3rem!important; }
  .rtl .xs-r-46 {
    left: 3.2857142857rem!important; }
  .rtl .xs-r-48 {
    left: 3.4285714286rem!important; }
  .rtl .xs-r-50 {
    left: 3.5714285714rem!important; }
  .rtl .xs-r-60 {
    left: 4.2857142857rem!important; }
  .rtl .xs-r-70 {
    left: 5rem!important; }
  .rtl .xs-r-80 {
    left: 5.7142857143rem!important; }
  .rtl .xs-r-90 {
    left: 6.4285714286rem!important; }
  .rtl .xs-r-100 {
    left: 7.1428571429rem!important; }
  .rtl .xs-r-110 {
    left: 7.8571428571rem!important; }
  .rtl .xs-r-120 {
    left: 8.5714285714rem!important; }
  .rtl .xs-r-130 {
    left: 9.2857142857rem!important; }
  .rtl .xs-r-140 {
    left: 10rem!important; }
  .rtl .xs-r-150 {
    left: 10.7142857143rem!important; }
  .rtl .xs-r-0 {
    right: auto!important; }
  .rtl .xs-r-10 {
    right: auto!important; }
  .rtl .xs-r-12 {
    right: auto!important; }
  .rtl .xs-r-14 {
    right: auto!important; }
  .rtl .xs-r-16 {
    right: auto!important; }
  .rtl .xs-r-18 {
    right: auto!important; }
  .rtl .xs-r-20 {
    right: auto!important; }
  .rtl .xs-r-22 {
    right: auto!important; }
  .rtl .xs-r-24 {
    right: auto!important; }
  .rtl .xs-r-26 {
    right: auto!important; }
  .rtl .xs-r-30 {
    right: auto!important; }
  .rtl .xs-r-32 {
    right: auto!important; }
  .rtl .xs-r-36 {
    right: auto!important; }
  .rtl .xs-r-38 {
    right: auto!important; }
  .rtl .xs-r-40 {
    right: auto!important; }
  .rtl .xs-r-42 {
    right: auto!important; }
  .rtl .xs-r-46 {
    right: auto!important; }
  .rtl .xs-r-48 {
    right: auto!important; }
  .rtl .xs-r-50 {
    right: auto!important; }
  .rtl .xs-r-60 {
    right: auto!important; }
  .rtl .xs-r-70 {
    right: auto!important; }
  .rtl .xs-r-80 {
    right: auto!important; }
  .rtl .xs-r-90 {
    right: auto!important; }
  .rtl .xs-r-100 {
    right: auto!important; }
  .rtl .xs-r-110 {
    right: auto!important; }
  .rtl .xs-r-120 {
    right: auto!important; }
  .rtl .xs-r-130 {
    right: auto!important; }
  .rtl .xs-r-140 {
    right: auto!important; }
  .rtl .xs-r-150 {
    right: auto!important; }
  .rtl .xs-l-0 {
    right: 0rem!important; }
  .rtl .xs-l-10 {
    right: 0.7142857143rem!important; }
  .rtl .xs-l-12 {
    right: 0.8571428571rem!important; }
  .rtl .xs-l-14 {
    right: 1rem!important; }
  .rtl .xs-l-16 {
    right: 1.1428571429rem!important; }
  .rtl .xs-l-18 {
    right: 1.2857142857rem!important; }
  .rtl .xs-l-20 {
    right: 1.4285714286rem!important; }
  .rtl .xs-l-22 {
    right: 1.5714285714rem!important; }
  .rtl .xs-l-24 {
    right: 1.7142857143rem!important; }
  .rtl .xs-l-26 {
    right: 1.8571428571rem!important; }
  .rtl .xs-l-30 {
    right: 2.1428571429rem!important; }
  .rtl .xs-l-32 {
    right: 2.2857142857rem!important; }
  .rtl .xs-l-36 {
    right: 2.5714285714rem!important; }
  .rtl .xs-l-38 {
    right: 2.7142857143rem!important; }
  .rtl .xs-l-40 {
    right: 2.8571428571rem!important; }
  .rtl .xs-l-42 {
    right: 3rem!important; }
  .rtl .xs-l-46 {
    right: 3.2857142857rem!important; }
  .rtl .xs-l-48 {
    right: 3.4285714286rem!important; }
  .rtl .xs-l-50 {
    right: 3.5714285714rem!important; }
  .rtl .xs-l-60 {
    right: 4.2857142857rem!important; }
  .rtl .xs-l-70 {
    right: 5rem!important; }
  .rtl .xs-l-80 {
    right: 5.7142857143rem!important; }
  .rtl .xs-l-90 {
    right: 6.4285714286rem!important; }
  .rtl .xs-l-100 {
    right: 7.1428571429rem!important; }
  .rtl .xs-l-110 {
    right: 7.8571428571rem!important; }
  .rtl .xs-l-120 {
    right: 8.5714285714rem!important; }
  .rtl .xs-l-130 {
    right: 9.2857142857rem!important; }
  .rtl .xs-l-140 {
    right: 10rem!important; }
  .rtl .xs-l-150 {
    right: 10.7142857143rem!important; }
  .rtl .xs-l-0 {
    left: auto!important; }
  .rtl .xs-l-10 {
    left: auto!important; }
  .rtl .xs-l-12 {
    left: auto!important; }
  .rtl .xs-l-14 {
    left: auto!important; }
  .rtl .xs-l-16 {
    left: auto!important; }
  .rtl .xs-l-18 {
    left: auto!important; }
  .rtl .xs-l-20 {
    left: auto!important; }
  .rtl .xs-l-22 {
    left: auto!important; }
  .rtl .xs-l-24 {
    left: auto!important; }
  .rtl .xs-l-26 {
    left: auto!important; }
  .rtl .xs-l-30 {
    left: auto!important; }
  .rtl .xs-l-32 {
    left: auto!important; }
  .rtl .xs-l-36 {
    left: auto!important; }
  .rtl .xs-l-38 {
    left: auto!important; }
  .rtl .xs-l-40 {
    left: auto!important; }
  .rtl .xs-l-42 {
    left: auto!important; }
  .rtl .xs-l-46 {
    left: auto!important; }
  .rtl .xs-l-48 {
    left: auto!important; }
  .rtl .xs-l-50 {
    left: auto!important; }
  .rtl .xs-l-60 {
    left: auto!important; }
  .rtl .xs-l-70 {
    left: auto!important; }
  .rtl .xs-l-80 {
    left: auto!important; }
  .rtl .xs-l-90 {
    left: auto!important; }
  .rtl .xs-l-100 {
    left: auto!important; }
  .rtl .xs-l-110 {
    left: auto!important; }
  .rtl .xs-l-120 {
    left: auto!important; }
  .rtl .xs-l-130 {
    left: auto!important; }
  .rtl .xs-l-140 {
    left: auto!important; }
  .rtl .xs-l-150 {
    left: auto!important; } }
@media (min-width: 576px) {
  .rtl .sm-r-0 {
    left: 0rem!important; }
  .rtl .sm-r-10 {
    left: 0.7142857143rem!important; }
  .rtl .sm-r-12 {
    left: 0.8571428571rem!important; }
  .rtl .sm-r-14 {
    left: 1rem!important; }
  .rtl .sm-r-16 {
    left: 1.1428571429rem!important; }
  .rtl .sm-r-18 {
    left: 1.2857142857rem!important; }
  .rtl .sm-r-20 {
    left: 1.4285714286rem!important; }
  .rtl .sm-r-22 {
    left: 1.5714285714rem!important; }
  .rtl .sm-r-24 {
    left: 1.7142857143rem!important; }
  .rtl .sm-r-26 {
    left: 1.8571428571rem!important; }
  .rtl .sm-r-30 {
    left: 2.1428571429rem!important; }
  .rtl .sm-r-32 {
    left: 2.2857142857rem!important; }
  .rtl .sm-r-36 {
    left: 2.5714285714rem!important; }
  .rtl .sm-r-38 {
    left: 2.7142857143rem!important; }
  .rtl .sm-r-40 {
    left: 2.8571428571rem!important; }
  .rtl .sm-r-42 {
    left: 3rem!important; }
  .rtl .sm-r-46 {
    left: 3.2857142857rem!important; }
  .rtl .sm-r-48 {
    left: 3.4285714286rem!important; }
  .rtl .sm-r-50 {
    left: 3.5714285714rem!important; }
  .rtl .sm-r-60 {
    left: 4.2857142857rem!important; }
  .rtl .sm-r-70 {
    left: 5rem!important; }
  .rtl .sm-r-80 {
    left: 5.7142857143rem!important; }
  .rtl .sm-r-90 {
    left: 6.4285714286rem!important; }
  .rtl .sm-r-100 {
    left: 7.1428571429rem!important; }
  .rtl .sm-r-110 {
    left: 7.8571428571rem!important; }
  .rtl .sm-r-120 {
    left: 8.5714285714rem!important; }
  .rtl .sm-r-130 {
    left: 9.2857142857rem!important; }
  .rtl .sm-r-140 {
    left: 10rem!important; }
  .rtl .sm-r-150 {
    left: 10.7142857143rem!important; }
  .rtl .sm-r-0 {
    right: auto!important; }
  .rtl .sm-r-10 {
    right: auto!important; }
  .rtl .sm-r-12 {
    right: auto!important; }
  .rtl .sm-r-14 {
    right: auto!important; }
  .rtl .sm-r-16 {
    right: auto!important; }
  .rtl .sm-r-18 {
    right: auto!important; }
  .rtl .sm-r-20 {
    right: auto!important; }
  .rtl .sm-r-22 {
    right: auto!important; }
  .rtl .sm-r-24 {
    right: auto!important; }
  .rtl .sm-r-26 {
    right: auto!important; }
  .rtl .sm-r-30 {
    right: auto!important; }
  .rtl .sm-r-32 {
    right: auto!important; }
  .rtl .sm-r-36 {
    right: auto!important; }
  .rtl .sm-r-38 {
    right: auto!important; }
  .rtl .sm-r-40 {
    right: auto!important; }
  .rtl .sm-r-42 {
    right: auto!important; }
  .rtl .sm-r-46 {
    right: auto!important; }
  .rtl .sm-r-48 {
    right: auto!important; }
  .rtl .sm-r-50 {
    right: auto!important; }
  .rtl .sm-r-60 {
    right: auto!important; }
  .rtl .sm-r-70 {
    right: auto!important; }
  .rtl .sm-r-80 {
    right: auto!important; }
  .rtl .sm-r-90 {
    right: auto!important; }
  .rtl .sm-r-100 {
    right: auto!important; }
  .rtl .sm-r-110 {
    right: auto!important; }
  .rtl .sm-r-120 {
    right: auto!important; }
  .rtl .sm-r-130 {
    right: auto!important; }
  .rtl .sm-r-140 {
    right: auto!important; }
  .rtl .sm-r-150 {
    right: auto!important; }
  .rtl .sm-l-0 {
    right: 0rem!important; }
  .rtl .sm-l-10 {
    right: 0.7142857143rem!important; }
  .rtl .sm-l-12 {
    right: 0.8571428571rem!important; }
  .rtl .sm-l-14 {
    right: 1rem!important; }
  .rtl .sm-l-16 {
    right: 1.1428571429rem!important; }
  .rtl .sm-l-18 {
    right: 1.2857142857rem!important; }
  .rtl .sm-l-20 {
    right: 1.4285714286rem!important; }
  .rtl .sm-l-22 {
    right: 1.5714285714rem!important; }
  .rtl .sm-l-24 {
    right: 1.7142857143rem!important; }
  .rtl .sm-l-26 {
    right: 1.8571428571rem!important; }
  .rtl .sm-l-30 {
    right: 2.1428571429rem!important; }
  .rtl .sm-l-32 {
    right: 2.2857142857rem!important; }
  .rtl .sm-l-36 {
    right: 2.5714285714rem!important; }
  .rtl .sm-l-38 {
    right: 2.7142857143rem!important; }
  .rtl .sm-l-40 {
    right: 2.8571428571rem!important; }
  .rtl .sm-l-42 {
    right: 3rem!important; }
  .rtl .sm-l-46 {
    right: 3.2857142857rem!important; }
  .rtl .sm-l-48 {
    right: 3.4285714286rem!important; }
  .rtl .sm-l-50 {
    right: 3.5714285714rem!important; }
  .rtl .sm-l-60 {
    right: 4.2857142857rem!important; }
  .rtl .sm-l-70 {
    right: 5rem!important; }
  .rtl .sm-l-80 {
    right: 5.7142857143rem!important; }
  .rtl .sm-l-90 {
    right: 6.4285714286rem!important; }
  .rtl .sm-l-100 {
    right: 7.1428571429rem!important; }
  .rtl .sm-l-110 {
    right: 7.8571428571rem!important; }
  .rtl .sm-l-120 {
    right: 8.5714285714rem!important; }
  .rtl .sm-l-130 {
    right: 9.2857142857rem!important; }
  .rtl .sm-l-140 {
    right: 10rem!important; }
  .rtl .sm-l-150 {
    right: 10.7142857143rem!important; }
  .rtl .sm-l-0 {
    left: auto!important; }
  .rtl .sm-l-10 {
    left: auto!important; }
  .rtl .sm-l-12 {
    left: auto!important; }
  .rtl .sm-l-14 {
    left: auto!important; }
  .rtl .sm-l-16 {
    left: auto!important; }
  .rtl .sm-l-18 {
    left: auto!important; }
  .rtl .sm-l-20 {
    left: auto!important; }
  .rtl .sm-l-22 {
    left: auto!important; }
  .rtl .sm-l-24 {
    left: auto!important; }
  .rtl .sm-l-26 {
    left: auto!important; }
  .rtl .sm-l-30 {
    left: auto!important; }
  .rtl .sm-l-32 {
    left: auto!important; }
  .rtl .sm-l-36 {
    left: auto!important; }
  .rtl .sm-l-38 {
    left: auto!important; }
  .rtl .sm-l-40 {
    left: auto!important; }
  .rtl .sm-l-42 {
    left: auto!important; }
  .rtl .sm-l-46 {
    left: auto!important; }
  .rtl .sm-l-48 {
    left: auto!important; }
  .rtl .sm-l-50 {
    left: auto!important; }
  .rtl .sm-l-60 {
    left: auto!important; }
  .rtl .sm-l-70 {
    left: auto!important; }
  .rtl .sm-l-80 {
    left: auto!important; }
  .rtl .sm-l-90 {
    left: auto!important; }
  .rtl .sm-l-100 {
    left: auto!important; }
  .rtl .sm-l-110 {
    left: auto!important; }
  .rtl .sm-l-120 {
    left: auto!important; }
  .rtl .sm-l-130 {
    left: auto!important; }
  .rtl .sm-l-140 {
    left: auto!important; }
  .rtl .sm-l-150 {
    left: auto!important; } }
@media (max-width: 767px) {
  .rtl .sm-max-r-0 {
    left: 0rem!important; }
  .rtl .sm-max-r-10 {
    left: 0.7142857143rem!important; }
  .rtl .sm-max-r-12 {
    left: 0.8571428571rem!important; }
  .rtl .sm-max-r-14 {
    left: 1rem!important; }
  .rtl .sm-max-r-16 {
    left: 1.1428571429rem!important; }
  .rtl .sm-max-r-18 {
    left: 1.2857142857rem!important; }
  .rtl .sm-max-r-20 {
    left: 1.4285714286rem!important; }
  .rtl .sm-max-r-22 {
    left: 1.5714285714rem!important; }
  .rtl .sm-max-r-24 {
    left: 1.7142857143rem!important; }
  .rtl .sm-max-r-26 {
    left: 1.8571428571rem!important; }
  .rtl .sm-max-r-30 {
    left: 2.1428571429rem!important; }
  .rtl .sm-max-r-32 {
    left: 2.2857142857rem!important; }
  .rtl .sm-max-r-36 {
    left: 2.5714285714rem!important; }
  .rtl .sm-max-r-38 {
    left: 2.7142857143rem!important; }
  .rtl .sm-max-r-40 {
    left: 2.8571428571rem!important; }
  .rtl .sm-max-r-42 {
    left: 3rem!important; }
  .rtl .sm-max-r-46 {
    left: 3.2857142857rem!important; }
  .rtl .sm-max-r-48 {
    left: 3.4285714286rem!important; }
  .rtl .sm-max-r-50 {
    left: 3.5714285714rem!important; }
  .rtl .sm-max-r-60 {
    left: 4.2857142857rem!important; }
  .rtl .sm-max-r-70 {
    left: 5rem!important; }
  .rtl .sm-max-r-80 {
    left: 5.7142857143rem!important; }
  .rtl .sm-max-r-90 {
    left: 6.4285714286rem!important; }
  .rtl .sm-max-r-100 {
    left: 7.1428571429rem!important; }
  .rtl .sm-max-r-110 {
    left: 7.8571428571rem!important; }
  .rtl .sm-max-r-120 {
    left: 8.5714285714rem!important; }
  .rtl .sm-max-r-130 {
    left: 9.2857142857rem!important; }
  .rtl .sm-max-r-140 {
    left: 10rem!important; }
  .rtl .sm-max-r-150 {
    left: 10.7142857143rem!important; }
  .rtl .sm-max-r-0 {
    right: auto!important; }
  .rtl .sm-max-r-10 {
    right: auto!important; }
  .rtl .sm-max-r-12 {
    right: auto!important; }
  .rtl .sm-max-r-14 {
    right: auto!important; }
  .rtl .sm-max-r-16 {
    right: auto!important; }
  .rtl .sm-max-r-18 {
    right: auto!important; }
  .rtl .sm-max-r-20 {
    right: auto!important; }
  .rtl .sm-max-r-22 {
    right: auto!important; }
  .rtl .sm-max-r-24 {
    right: auto!important; }
  .rtl .sm-max-r-26 {
    right: auto!important; }
  .rtl .sm-max-r-30 {
    right: auto!important; }
  .rtl .sm-max-r-32 {
    right: auto!important; }
  .rtl .sm-max-r-36 {
    right: auto!important; }
  .rtl .sm-max-r-38 {
    right: auto!important; }
  .rtl .sm-max-r-40 {
    right: auto!important; }
  .rtl .sm-max-r-42 {
    right: auto!important; }
  .rtl .sm-max-r-46 {
    right: auto!important; }
  .rtl .sm-max-r-48 {
    right: auto!important; }
  .rtl .sm-max-r-50 {
    right: auto!important; }
  .rtl .sm-max-r-60 {
    right: auto!important; }
  .rtl .sm-max-r-70 {
    right: auto!important; }
  .rtl .sm-max-r-80 {
    right: auto!important; }
  .rtl .sm-max-r-90 {
    right: auto!important; }
  .rtl .sm-max-r-100 {
    right: auto!important; }
  .rtl .sm-max-r-110 {
    right: auto!important; }
  .rtl .sm-max-r-120 {
    right: auto!important; }
  .rtl .sm-max-r-130 {
    right: auto!important; }
  .rtl .sm-max-r-140 {
    right: auto!important; }
  .rtl .sm-max-r-150 {
    right: auto!important; }
  .rtl .sm-max-l-0 {
    right: 0rem!important; }
  .rtl .sm-max-l-10 {
    right: 0.7142857143rem!important; }
  .rtl .sm-max-l-12 {
    right: 0.8571428571rem!important; }
  .rtl .sm-max-l-14 {
    right: 1rem!important; }
  .rtl .sm-max-l-16 {
    right: 1.1428571429rem!important; }
  .rtl .sm-max-l-18 {
    right: 1.2857142857rem!important; }
  .rtl .sm-max-l-20 {
    right: 1.4285714286rem!important; }
  .rtl .sm-max-l-22 {
    right: 1.5714285714rem!important; }
  .rtl .sm-max-l-24 {
    right: 1.7142857143rem!important; }
  .rtl .sm-max-l-26 {
    right: 1.8571428571rem!important; }
  .rtl .sm-max-l-30 {
    right: 2.1428571429rem!important; }
  .rtl .sm-max-l-32 {
    right: 2.2857142857rem!important; }
  .rtl .sm-max-l-36 {
    right: 2.5714285714rem!important; }
  .rtl .sm-max-l-38 {
    right: 2.7142857143rem!important; }
  .rtl .sm-max-l-40 {
    right: 2.8571428571rem!important; }
  .rtl .sm-max-l-42 {
    right: 3rem!important; }
  .rtl .sm-max-l-46 {
    right: 3.2857142857rem!important; }
  .rtl .sm-max-l-48 {
    right: 3.4285714286rem!important; }
  .rtl .sm-max-l-50 {
    right: 3.5714285714rem!important; }
  .rtl .sm-max-l-60 {
    right: 4.2857142857rem!important; }
  .rtl .sm-max-l-70 {
    right: 5rem!important; }
  .rtl .sm-max-l-80 {
    right: 5.7142857143rem!important; }
  .rtl .sm-max-l-90 {
    right: 6.4285714286rem!important; }
  .rtl .sm-max-l-100 {
    right: 7.1428571429rem!important; }
  .rtl .sm-max-l-110 {
    right: 7.8571428571rem!important; }
  .rtl .sm-max-l-120 {
    right: 8.5714285714rem!important; }
  .rtl .sm-max-l-130 {
    right: 9.2857142857rem!important; }
  .rtl .sm-max-l-140 {
    right: 10rem!important; }
  .rtl .sm-max-l-150 {
    right: 10.7142857143rem!important; }
  .rtl .sm-max-l-0 {
    left: auto!important; }
  .rtl .sm-max-l-10 {
    left: auto!important; }
  .rtl .sm-max-l-12 {
    left: auto!important; }
  .rtl .sm-max-l-14 {
    left: auto!important; }
  .rtl .sm-max-l-16 {
    left: auto!important; }
  .rtl .sm-max-l-18 {
    left: auto!important; }
  .rtl .sm-max-l-20 {
    left: auto!important; }
  .rtl .sm-max-l-22 {
    left: auto!important; }
  .rtl .sm-max-l-24 {
    left: auto!important; }
  .rtl .sm-max-l-26 {
    left: auto!important; }
  .rtl .sm-max-l-30 {
    left: auto!important; }
  .rtl .sm-max-l-32 {
    left: auto!important; }
  .rtl .sm-max-l-36 {
    left: auto!important; }
  .rtl .sm-max-l-38 {
    left: auto!important; }
  .rtl .sm-max-l-40 {
    left: auto!important; }
  .rtl .sm-max-l-42 {
    left: auto!important; }
  .rtl .sm-max-l-46 {
    left: auto!important; }
  .rtl .sm-max-l-48 {
    left: auto!important; }
  .rtl .sm-max-l-50 {
    left: auto!important; }
  .rtl .sm-max-l-60 {
    left: auto!important; }
  .rtl .sm-max-l-70 {
    left: auto!important; }
  .rtl .sm-max-l-80 {
    left: auto!important; }
  .rtl .sm-max-l-90 {
    left: auto!important; }
  .rtl .sm-max-l-100 {
    left: auto!important; }
  .rtl .sm-max-l-110 {
    left: auto!important; }
  .rtl .sm-max-l-120 {
    left: auto!important; }
  .rtl .sm-max-l-130 {
    left: auto!important; }
  .rtl .sm-max-l-140 {
    left: auto!important; }
  .rtl .sm-max-l-150 {
    left: auto!important; } }
@media (min-width: 768px) {
  .rtl .md-r-0 {
    left: 0rem!important; }
  .rtl .md-r-10 {
    left: 0.7142857143rem!important; }
  .rtl .md-r-12 {
    left: 0.8571428571rem!important; }
  .rtl .md-r-14 {
    left: 1rem!important; }
  .rtl .md-r-16 {
    left: 1.1428571429rem!important; }
  .rtl .md-r-18 {
    left: 1.2857142857rem!important; }
  .rtl .md-r-20 {
    left: 1.4285714286rem!important; }
  .rtl .md-r-22 {
    left: 1.5714285714rem!important; }
  .rtl .md-r-24 {
    left: 1.7142857143rem!important; }
  .rtl .md-r-26 {
    left: 1.8571428571rem!important; }
  .rtl .md-r-30 {
    left: 2.1428571429rem!important; }
  .rtl .md-r-32 {
    left: 2.2857142857rem!important; }
  .rtl .md-r-36 {
    left: 2.5714285714rem!important; }
  .rtl .md-r-38 {
    left: 2.7142857143rem!important; }
  .rtl .md-r-40 {
    left: 2.8571428571rem!important; }
  .rtl .md-r-42 {
    left: 3rem!important; }
  .rtl .md-r-46 {
    left: 3.2857142857rem!important; }
  .rtl .md-r-48 {
    left: 3.4285714286rem!important; }
  .rtl .md-r-50 {
    left: 3.5714285714rem!important; }
  .rtl .md-r-60 {
    left: 4.2857142857rem!important; }
  .rtl .md-r-70 {
    left: 5rem!important; }
  .rtl .md-r-80 {
    left: 5.7142857143rem!important; }
  .rtl .md-r-90 {
    left: 6.4285714286rem!important; }
  .rtl .md-r-100 {
    left: 7.1428571429rem!important; }
  .rtl .md-r-110 {
    left: 7.8571428571rem!important; }
  .rtl .md-r-120 {
    left: 8.5714285714rem!important; }
  .rtl .md-r-130 {
    left: 9.2857142857rem!important; }
  .rtl .md-r-140 {
    left: 10rem!important; }
  .rtl .md-r-150 {
    left: 10.7142857143rem!important; }
  .rtl .md-r-0 {
    right: auto!important; }
  .rtl .md-r-10 {
    right: auto!important; }
  .rtl .md-r-12 {
    right: auto!important; }
  .rtl .md-r-14 {
    right: auto!important; }
  .rtl .md-r-16 {
    right: auto!important; }
  .rtl .md-r-18 {
    right: auto!important; }
  .rtl .md-r-20 {
    right: auto!important; }
  .rtl .md-r-22 {
    right: auto!important; }
  .rtl .md-r-24 {
    right: auto!important; }
  .rtl .md-r-26 {
    right: auto!important; }
  .rtl .md-r-30 {
    right: auto!important; }
  .rtl .md-r-32 {
    right: auto!important; }
  .rtl .md-r-36 {
    right: auto!important; }
  .rtl .md-r-38 {
    right: auto!important; }
  .rtl .md-r-40 {
    right: auto!important; }
  .rtl .md-r-42 {
    right: auto!important; }
  .rtl .md-r-46 {
    right: auto!important; }
  .rtl .md-r-48 {
    right: auto!important; }
  .rtl .md-r-50 {
    right: auto!important; }
  .rtl .md-r-60 {
    right: auto!important; }
  .rtl .md-r-70 {
    right: auto!important; }
  .rtl .md-r-80 {
    right: auto!important; }
  .rtl .md-r-90 {
    right: auto!important; }
  .rtl .md-r-100 {
    right: auto!important; }
  .rtl .md-r-110 {
    right: auto!important; }
  .rtl .md-r-120 {
    right: auto!important; }
  .rtl .md-r-130 {
    right: auto!important; }
  .rtl .md-r-140 {
    right: auto!important; }
  .rtl .md-r-150 {
    right: auto!important; }
  .rtl .md-l-0 {
    right: 0rem!important; }
  .rtl .md-l-10 {
    right: 0.7142857143rem!important; }
  .rtl .md-l-12 {
    right: 0.8571428571rem!important; }
  .rtl .md-l-14 {
    right: 1rem!important; }
  .rtl .md-l-16 {
    right: 1.1428571429rem!important; }
  .rtl .md-l-18 {
    right: 1.2857142857rem!important; }
  .rtl .md-l-20 {
    right: 1.4285714286rem!important; }
  .rtl .md-l-22 {
    right: 1.5714285714rem!important; }
  .rtl .md-l-24 {
    right: 1.7142857143rem!important; }
  .rtl .md-l-26 {
    right: 1.8571428571rem!important; }
  .rtl .md-l-30 {
    right: 2.1428571429rem!important; }
  .rtl .md-l-32 {
    right: 2.2857142857rem!important; }
  .rtl .md-l-36 {
    right: 2.5714285714rem!important; }
  .rtl .md-l-38 {
    right: 2.7142857143rem!important; }
  .rtl .md-l-40 {
    right: 2.8571428571rem!important; }
  .rtl .md-l-42 {
    right: 3rem!important; }
  .rtl .md-l-46 {
    right: 3.2857142857rem!important; }
  .rtl .md-l-48 {
    right: 3.4285714286rem!important; }
  .rtl .md-l-50 {
    right: 3.5714285714rem!important; }
  .rtl .md-l-60 {
    right: 4.2857142857rem!important; }
  .rtl .md-l-70 {
    right: 5rem!important; }
  .rtl .md-l-80 {
    right: 5.7142857143rem!important; }
  .rtl .md-l-90 {
    right: 6.4285714286rem!important; }
  .rtl .md-l-100 {
    right: 7.1428571429rem!important; }
  .rtl .md-l-110 {
    right: 7.8571428571rem!important; }
  .rtl .md-l-120 {
    right: 8.5714285714rem!important; }
  .rtl .md-l-130 {
    right: 9.2857142857rem!important; }
  .rtl .md-l-140 {
    right: 10rem!important; }
  .rtl .md-l-150 {
    right: 10.7142857143rem!important; }
  .rtl .md-l-0 {
    left: auto!important; }
  .rtl .md-l-10 {
    left: auto!important; }
  .rtl .md-l-12 {
    left: auto!important; }
  .rtl .md-l-14 {
    left: auto!important; }
  .rtl .md-l-16 {
    left: auto!important; }
  .rtl .md-l-18 {
    left: auto!important; }
  .rtl .md-l-20 {
    left: auto!important; }
  .rtl .md-l-22 {
    left: auto!important; }
  .rtl .md-l-24 {
    left: auto!important; }
  .rtl .md-l-26 {
    left: auto!important; }
  .rtl .md-l-30 {
    left: auto!important; }
  .rtl .md-l-32 {
    left: auto!important; }
  .rtl .md-l-36 {
    left: auto!important; }
  .rtl .md-l-38 {
    left: auto!important; }
  .rtl .md-l-40 {
    left: auto!important; }
  .rtl .md-l-42 {
    left: auto!important; }
  .rtl .md-l-46 {
    left: auto!important; }
  .rtl .md-l-48 {
    left: auto!important; }
  .rtl .md-l-50 {
    left: auto!important; }
  .rtl .md-l-60 {
    left: auto!important; }
  .rtl .md-l-70 {
    left: auto!important; }
  .rtl .md-l-80 {
    left: auto!important; }
  .rtl .md-l-90 {
    left: auto!important; }
  .rtl .md-l-100 {
    left: auto!important; }
  .rtl .md-l-110 {
    left: auto!important; }
  .rtl .md-l-120 {
    left: auto!important; }
  .rtl .md-l-130 {
    left: auto!important; }
  .rtl .md-l-140 {
    left: auto!important; }
  .rtl .md-l-150 {
    left: auto!important; } }
@media (max-width: 991px) {
  .rtl .md-max-r-0 {
    left: 0rem!important; }
  .rtl .md-max-r-10 {
    left: 0.7142857143rem!important; }
  .rtl .md-max-r-12 {
    left: 0.8571428571rem!important; }
  .rtl .md-max-r-14 {
    left: 1rem!important; }
  .rtl .md-max-r-16 {
    left: 1.1428571429rem!important; }
  .rtl .md-max-r-18 {
    left: 1.2857142857rem!important; }
  .rtl .md-max-r-20 {
    left: 1.4285714286rem!important; }
  .rtl .md-max-r-22 {
    left: 1.5714285714rem!important; }
  .rtl .md-max-r-24 {
    left: 1.7142857143rem!important; }
  .rtl .md-max-r-26 {
    left: 1.8571428571rem!important; }
  .rtl .md-max-r-30 {
    left: 2.1428571429rem!important; }
  .rtl .md-max-r-32 {
    left: 2.2857142857rem!important; }
  .rtl .md-max-r-36 {
    left: 2.5714285714rem!important; }
  .rtl .md-max-r-38 {
    left: 2.7142857143rem!important; }
  .rtl .md-max-r-40 {
    left: 2.8571428571rem!important; }
  .rtl .md-max-r-42 {
    left: 3rem!important; }
  .rtl .md-max-r-46 {
    left: 3.2857142857rem!important; }
  .rtl .md-max-r-48 {
    left: 3.4285714286rem!important; }
  .rtl .md-max-r-50 {
    left: 3.5714285714rem!important; }
  .rtl .md-max-r-60 {
    left: 4.2857142857rem!important; }
  .rtl .md-max-r-70 {
    left: 5rem!important; }
  .rtl .md-max-r-80 {
    left: 5.7142857143rem!important; }
  .rtl .md-max-r-90 {
    left: 6.4285714286rem!important; }
  .rtl .md-max-r-100 {
    left: 7.1428571429rem!important; }
  .rtl .md-max-r-110 {
    left: 7.8571428571rem!important; }
  .rtl .md-max-r-120 {
    left: 8.5714285714rem!important; }
  .rtl .md-max-r-130 {
    left: 9.2857142857rem!important; }
  .rtl .md-max-r-140 {
    left: 10rem!important; }
  .rtl .md-max-r-150 {
    left: 10.7142857143rem!important; }
  .rtl .md-max-r-0 {
    right: auto!important; }
  .rtl .md-max-r-10 {
    right: auto!important; }
  .rtl .md-max-r-12 {
    right: auto!important; }
  .rtl .md-max-r-14 {
    right: auto!important; }
  .rtl .md-max-r-16 {
    right: auto!important; }
  .rtl .md-max-r-18 {
    right: auto!important; }
  .rtl .md-max-r-20 {
    right: auto!important; }
  .rtl .md-max-r-22 {
    right: auto!important; }
  .rtl .md-max-r-24 {
    right: auto!important; }
  .rtl .md-max-r-26 {
    right: auto!important; }
  .rtl .md-max-r-30 {
    right: auto!important; }
  .rtl .md-max-r-32 {
    right: auto!important; }
  .rtl .md-max-r-36 {
    right: auto!important; }
  .rtl .md-max-r-38 {
    right: auto!important; }
  .rtl .md-max-r-40 {
    right: auto!important; }
  .rtl .md-max-r-42 {
    right: auto!important; }
  .rtl .md-max-r-46 {
    right: auto!important; }
  .rtl .md-max-r-48 {
    right: auto!important; }
  .rtl .md-max-r-50 {
    right: auto!important; }
  .rtl .md-max-r-60 {
    right: auto!important; }
  .rtl .md-max-r-70 {
    right: auto!important; }
  .rtl .md-max-r-80 {
    right: auto!important; }
  .rtl .md-max-r-90 {
    right: auto!important; }
  .rtl .md-max-r-100 {
    right: auto!important; }
  .rtl .md-max-r-110 {
    right: auto!important; }
  .rtl .md-max-r-120 {
    right: auto!important; }
  .rtl .md-max-r-130 {
    right: auto!important; }
  .rtl .md-max-r-140 {
    right: auto!important; }
  .rtl .md-max-r-150 {
    right: auto!important; }
  .rtl .md-max-l-0 {
    right: 0rem!important; }
  .rtl .md-max-l-10 {
    right: 0.7142857143rem!important; }
  .rtl .md-max-l-12 {
    right: 0.8571428571rem!important; }
  .rtl .md-max-l-14 {
    right: 1rem!important; }
  .rtl .md-max-l-16 {
    right: 1.1428571429rem!important; }
  .rtl .md-max-l-18 {
    right: 1.2857142857rem!important; }
  .rtl .md-max-l-20 {
    right: 1.4285714286rem!important; }
  .rtl .md-max-l-22 {
    right: 1.5714285714rem!important; }
  .rtl .md-max-l-24 {
    right: 1.7142857143rem!important; }
  .rtl .md-max-l-26 {
    right: 1.8571428571rem!important; }
  .rtl .md-max-l-30 {
    right: 2.1428571429rem!important; }
  .rtl .md-max-l-32 {
    right: 2.2857142857rem!important; }
  .rtl .md-max-l-36 {
    right: 2.5714285714rem!important; }
  .rtl .md-max-l-38 {
    right: 2.7142857143rem!important; }
  .rtl .md-max-l-40 {
    right: 2.8571428571rem!important; }
  .rtl .md-max-l-42 {
    right: 3rem!important; }
  .rtl .md-max-l-46 {
    right: 3.2857142857rem!important; }
  .rtl .md-max-l-48 {
    right: 3.4285714286rem!important; }
  .rtl .md-max-l-50 {
    right: 3.5714285714rem!important; }
  .rtl .md-max-l-60 {
    right: 4.2857142857rem!important; }
  .rtl .md-max-l-70 {
    right: 5rem!important; }
  .rtl .md-max-l-80 {
    right: 5.7142857143rem!important; }
  .rtl .md-max-l-90 {
    right: 6.4285714286rem!important; }
  .rtl .md-max-l-100 {
    right: 7.1428571429rem!important; }
  .rtl .md-max-l-110 {
    right: 7.8571428571rem!important; }
  .rtl .md-max-l-120 {
    right: 8.5714285714rem!important; }
  .rtl .md-max-l-130 {
    right: 9.2857142857rem!important; }
  .rtl .md-max-l-140 {
    right: 10rem!important; }
  .rtl .md-max-l-150 {
    right: 10.7142857143rem!important; }
  .rtl .md-max-l-0 {
    left: auto!important; }
  .rtl .md-max-l-10 {
    left: auto!important; }
  .rtl .md-max-l-12 {
    left: auto!important; }
  .rtl .md-max-l-14 {
    left: auto!important; }
  .rtl .md-max-l-16 {
    left: auto!important; }
  .rtl .md-max-l-18 {
    left: auto!important; }
  .rtl .md-max-l-20 {
    left: auto!important; }
  .rtl .md-max-l-22 {
    left: auto!important; }
  .rtl .md-max-l-24 {
    left: auto!important; }
  .rtl .md-max-l-26 {
    left: auto!important; }
  .rtl .md-max-l-30 {
    left: auto!important; }
  .rtl .md-max-l-32 {
    left: auto!important; }
  .rtl .md-max-l-36 {
    left: auto!important; }
  .rtl .md-max-l-38 {
    left: auto!important; }
  .rtl .md-max-l-40 {
    left: auto!important; }
  .rtl .md-max-l-42 {
    left: auto!important; }
  .rtl .md-max-l-46 {
    left: auto!important; }
  .rtl .md-max-l-48 {
    left: auto!important; }
  .rtl .md-max-l-50 {
    left: auto!important; }
  .rtl .md-max-l-60 {
    left: auto!important; }
  .rtl .md-max-l-70 {
    left: auto!important; }
  .rtl .md-max-l-80 {
    left: auto!important; }
  .rtl .md-max-l-90 {
    left: auto!important; }
  .rtl .md-max-l-100 {
    left: auto!important; }
  .rtl .md-max-l-110 {
    left: auto!important; }
  .rtl .md-max-l-120 {
    left: auto!important; }
  .rtl .md-max-l-130 {
    left: auto!important; }
  .rtl .md-max-l-140 {
    left: auto!important; }
  .rtl .md-max-l-150 {
    left: auto!important; } }
@media (min-width: 992px) {
  .rtl .lg-r-0 {
    left: 0rem!important; }
  .rtl .lg-r-10 {
    left: 0.7142857143rem!important; }
  .rtl .lg-r-12 {
    left: 0.8571428571rem!important; }
  .rtl .lg-r-14 {
    left: 1rem!important; }
  .rtl .lg-r-16 {
    left: 1.1428571429rem!important; }
  .rtl .lg-r-18 {
    left: 1.2857142857rem!important; }
  .rtl .lg-r-20 {
    left: 1.4285714286rem!important; }
  .rtl .lg-r-22 {
    left: 1.5714285714rem!important; }
  .rtl .lg-r-24 {
    left: 1.7142857143rem!important; }
  .rtl .lg-r-26 {
    left: 1.8571428571rem!important; }
  .rtl .lg-r-30 {
    left: 2.1428571429rem!important; }
  .rtl .lg-r-32 {
    left: 2.2857142857rem!important; }
  .rtl .lg-r-36 {
    left: 2.5714285714rem!important; }
  .rtl .lg-r-38 {
    left: 2.7142857143rem!important; }
  .rtl .lg-r-40 {
    left: 2.8571428571rem!important; }
  .rtl .lg-r-42 {
    left: 3rem!important; }
  .rtl .lg-r-46 {
    left: 3.2857142857rem!important; }
  .rtl .lg-r-48 {
    left: 3.4285714286rem!important; }
  .rtl .lg-r-50 {
    left: 3.5714285714rem!important; }
  .rtl .lg-r-60 {
    left: 4.2857142857rem!important; }
  .rtl .lg-r-70 {
    left: 5rem!important; }
  .rtl .lg-r-80 {
    left: 5.7142857143rem!important; }
  .rtl .lg-r-90 {
    left: 6.4285714286rem!important; }
  .rtl .lg-r-100 {
    left: 7.1428571429rem!important; }
  .rtl .lg-r-110 {
    left: 7.8571428571rem!important; }
  .rtl .lg-r-120 {
    left: 8.5714285714rem!important; }
  .rtl .lg-r-130 {
    left: 9.2857142857rem!important; }
  .rtl .lg-r-140 {
    left: 10rem!important; }
  .rtl .lg-r-150 {
    left: 10.7142857143rem!important; }
  .rtl .lg-r-0 {
    right: auto!important; }
  .rtl .lg-r-10 {
    right: auto!important; }
  .rtl .lg-r-12 {
    right: auto!important; }
  .rtl .lg-r-14 {
    right: auto!important; }
  .rtl .lg-r-16 {
    right: auto!important; }
  .rtl .lg-r-18 {
    right: auto!important; }
  .rtl .lg-r-20 {
    right: auto!important; }
  .rtl .lg-r-22 {
    right: auto!important; }
  .rtl .lg-r-24 {
    right: auto!important; }
  .rtl .lg-r-26 {
    right: auto!important; }
  .rtl .lg-r-30 {
    right: auto!important; }
  .rtl .lg-r-32 {
    right: auto!important; }
  .rtl .lg-r-36 {
    right: auto!important; }
  .rtl .lg-r-38 {
    right: auto!important; }
  .rtl .lg-r-40 {
    right: auto!important; }
  .rtl .lg-r-42 {
    right: auto!important; }
  .rtl .lg-r-46 {
    right: auto!important; }
  .rtl .lg-r-48 {
    right: auto!important; }
  .rtl .lg-r-50 {
    right: auto!important; }
  .rtl .lg-r-60 {
    right: auto!important; }
  .rtl .lg-r-70 {
    right: auto!important; }
  .rtl .lg-r-80 {
    right: auto!important; }
  .rtl .lg-r-90 {
    right: auto!important; }
  .rtl .lg-r-100 {
    right: auto!important; }
  .rtl .lg-r-110 {
    right: auto!important; }
  .rtl .lg-r-120 {
    right: auto!important; }
  .rtl .lg-r-130 {
    right: auto!important; }
  .rtl .lg-r-140 {
    right: auto!important; }
  .rtl .lg-r-150 {
    right: auto!important; }
  .rtl .lg-l-0 {
    right: 0rem!important; }
  .rtl .lg-l-10 {
    right: 0.7142857143rem!important; }
  .rtl .lg-l-12 {
    right: 0.8571428571rem!important; }
  .rtl .lg-l-14 {
    right: 1rem!important; }
  .rtl .lg-l-16 {
    right: 1.1428571429rem!important; }
  .rtl .lg-l-18 {
    right: 1.2857142857rem!important; }
  .rtl .lg-l-20 {
    right: 1.4285714286rem!important; }
  .rtl .lg-l-22 {
    right: 1.5714285714rem!important; }
  .rtl .lg-l-24 {
    right: 1.7142857143rem!important; }
  .rtl .lg-l-26 {
    right: 1.8571428571rem!important; }
  .rtl .lg-l-30 {
    right: 2.1428571429rem!important; }
  .rtl .lg-l-32 {
    right: 2.2857142857rem!important; }
  .rtl .lg-l-36 {
    right: 2.5714285714rem!important; }
  .rtl .lg-l-38 {
    right: 2.7142857143rem!important; }
  .rtl .lg-l-40 {
    right: 2.8571428571rem!important; }
  .rtl .lg-l-42 {
    right: 3rem!important; }
  .rtl .lg-l-46 {
    right: 3.2857142857rem!important; }
  .rtl .lg-l-48 {
    right: 3.4285714286rem!important; }
  .rtl .lg-l-50 {
    right: 3.5714285714rem!important; }
  .rtl .lg-l-60 {
    right: 4.2857142857rem!important; }
  .rtl .lg-l-70 {
    right: 5rem!important; }
  .rtl .lg-l-80 {
    right: 5.7142857143rem!important; }
  .rtl .lg-l-90 {
    right: 6.4285714286rem!important; }
  .rtl .lg-l-100 {
    right: 7.1428571429rem!important; }
  .rtl .lg-l-110 {
    right: 7.8571428571rem!important; }
  .rtl .lg-l-120 {
    right: 8.5714285714rem!important; }
  .rtl .lg-l-130 {
    right: 9.2857142857rem!important; }
  .rtl .lg-l-140 {
    right: 10rem!important; }
  .rtl .lg-l-150 {
    right: 10.7142857143rem!important; }
  .rtl .lg-l-0 {
    left: auto!important; }
  .rtl .lg-l-10 {
    left: auto!important; }
  .rtl .lg-l-12 {
    left: auto!important; }
  .rtl .lg-l-14 {
    left: auto!important; }
  .rtl .lg-l-16 {
    left: auto!important; }
  .rtl .lg-l-18 {
    left: auto!important; }
  .rtl .lg-l-20 {
    left: auto!important; }
  .rtl .lg-l-22 {
    left: auto!important; }
  .rtl .lg-l-24 {
    left: auto!important; }
  .rtl .lg-l-26 {
    left: auto!important; }
  .rtl .lg-l-30 {
    left: auto!important; }
  .rtl .lg-l-32 {
    left: auto!important; }
  .rtl .lg-l-36 {
    left: auto!important; }
  .rtl .lg-l-38 {
    left: auto!important; }
  .rtl .lg-l-40 {
    left: auto!important; }
  .rtl .lg-l-42 {
    left: auto!important; }
  .rtl .lg-l-46 {
    left: auto!important; }
  .rtl .lg-l-48 {
    left: auto!important; }
  .rtl .lg-l-50 {
    left: auto!important; }
  .rtl .lg-l-60 {
    left: auto!important; }
  .rtl .lg-l-70 {
    left: auto!important; }
  .rtl .lg-l-80 {
    left: auto!important; }
  .rtl .lg-l-90 {
    left: auto!important; }
  .rtl .lg-l-100 {
    left: auto!important; }
  .rtl .lg-l-110 {
    left: auto!important; }
  .rtl .lg-l-120 {
    left: auto!important; }
  .rtl .lg-l-130 {
    left: auto!important; }
  .rtl .lg-l-140 {
    left: auto!important; }
  .rtl .lg-l-150 {
    left: auto!important; } }
@media (max-width: 1024px) {
  .rtl .tl-r-0 {
    left: 0rem!important; }
  .rtl .tl-r-10 {
    left: 0.7142857143rem!important; }
  .rtl .tl-r-12 {
    left: 0.8571428571rem!important; }
  .rtl .tl-r-14 {
    left: 1rem!important; }
  .rtl .tl-r-16 {
    left: 1.1428571429rem!important; }
  .rtl .tl-r-18 {
    left: 1.2857142857rem!important; }
  .rtl .tl-r-20 {
    left: 1.4285714286rem!important; }
  .rtl .tl-r-22 {
    left: 1.5714285714rem!important; }
  .rtl .tl-r-24 {
    left: 1.7142857143rem!important; }
  .rtl .tl-r-26 {
    left: 1.8571428571rem!important; }
  .rtl .tl-r-30 {
    left: 2.1428571429rem!important; }
  .rtl .tl-r-32 {
    left: 2.2857142857rem!important; }
  .rtl .tl-r-36 {
    left: 2.5714285714rem!important; }
  .rtl .tl-r-38 {
    left: 2.7142857143rem!important; }
  .rtl .tl-r-40 {
    left: 2.8571428571rem!important; }
  .rtl .tl-r-42 {
    left: 3rem!important; }
  .rtl .tl-r-46 {
    left: 3.2857142857rem!important; }
  .rtl .tl-r-48 {
    left: 3.4285714286rem!important; }
  .rtl .tl-r-50 {
    left: 3.5714285714rem!important; }
  .rtl .tl-r-60 {
    left: 4.2857142857rem!important; }
  .rtl .tl-r-70 {
    left: 5rem!important; }
  .rtl .tl-r-80 {
    left: 5.7142857143rem!important; }
  .rtl .tl-r-90 {
    left: 6.4285714286rem!important; }
  .rtl .tl-r-100 {
    left: 7.1428571429rem!important; }
  .rtl .tl-r-110 {
    left: 7.8571428571rem!important; }
  .rtl .tl-r-120 {
    left: 8.5714285714rem!important; }
  .rtl .tl-r-130 {
    left: 9.2857142857rem!important; }
  .rtl .tl-r-140 {
    left: 10rem!important; }
  .rtl .tl-r-150 {
    left: 10.7142857143rem!important; }
  .rtl .tl-r-0 {
    right: auto!important; }
  .rtl .tl-r-10 {
    right: auto!important; }
  .rtl .tl-r-12 {
    right: auto!important; }
  .rtl .tl-r-14 {
    right: auto!important; }
  .rtl .tl-r-16 {
    right: auto!important; }
  .rtl .tl-r-18 {
    right: auto!important; }
  .rtl .tl-r-20 {
    right: auto!important; }
  .rtl .tl-r-22 {
    right: auto!important; }
  .rtl .tl-r-24 {
    right: auto!important; }
  .rtl .tl-r-26 {
    right: auto!important; }
  .rtl .tl-r-30 {
    right: auto!important; }
  .rtl .tl-r-32 {
    right: auto!important; }
  .rtl .tl-r-36 {
    right: auto!important; }
  .rtl .tl-r-38 {
    right: auto!important; }
  .rtl .tl-r-40 {
    right: auto!important; }
  .rtl .tl-r-42 {
    right: auto!important; }
  .rtl .tl-r-46 {
    right: auto!important; }
  .rtl .tl-r-48 {
    right: auto!important; }
  .rtl .tl-r-50 {
    right: auto!important; }
  .rtl .tl-r-60 {
    right: auto!important; }
  .rtl .tl-r-70 {
    right: auto!important; }
  .rtl .tl-r-80 {
    right: auto!important; }
  .rtl .tl-r-90 {
    right: auto!important; }
  .rtl .tl-r-100 {
    right: auto!important; }
  .rtl .tl-r-110 {
    right: auto!important; }
  .rtl .tl-r-120 {
    right: auto!important; }
  .rtl .tl-r-130 {
    right: auto!important; }
  .rtl .tl-r-140 {
    right: auto!important; }
  .rtl .tl-r-150 {
    right: auto!important; }
  .rtl .tl-l-0 {
    right: 0rem!important; }
  .rtl .tl-l-10 {
    right: 0.7142857143rem!important; }
  .rtl .tl-l-12 {
    right: 0.8571428571rem!important; }
  .rtl .tl-l-14 {
    right: 1rem!important; }
  .rtl .tl-l-16 {
    right: 1.1428571429rem!important; }
  .rtl .tl-l-18 {
    right: 1.2857142857rem!important; }
  .rtl .tl-l-20 {
    right: 1.4285714286rem!important; }
  .rtl .tl-l-22 {
    right: 1.5714285714rem!important; }
  .rtl .tl-l-24 {
    right: 1.7142857143rem!important; }
  .rtl .tl-l-26 {
    right: 1.8571428571rem!important; }
  .rtl .tl-l-30 {
    right: 2.1428571429rem!important; }
  .rtl .tl-l-32 {
    right: 2.2857142857rem!important; }
  .rtl .tl-l-36 {
    right: 2.5714285714rem!important; }
  .rtl .tl-l-38 {
    right: 2.7142857143rem!important; }
  .rtl .tl-l-40 {
    right: 2.8571428571rem!important; }
  .rtl .tl-l-42 {
    right: 3rem!important; }
  .rtl .tl-l-46 {
    right: 3.2857142857rem!important; }
  .rtl .tl-l-48 {
    right: 3.4285714286rem!important; }
  .rtl .tl-l-50 {
    right: 3.5714285714rem!important; }
  .rtl .tl-l-60 {
    right: 4.2857142857rem!important; }
  .rtl .tl-l-70 {
    right: 5rem!important; }
  .rtl .tl-l-80 {
    right: 5.7142857143rem!important; }
  .rtl .tl-l-90 {
    right: 6.4285714286rem!important; }
  .rtl .tl-l-100 {
    right: 7.1428571429rem!important; }
  .rtl .tl-l-110 {
    right: 7.8571428571rem!important; }
  .rtl .tl-l-120 {
    right: 8.5714285714rem!important; }
  .rtl .tl-l-130 {
    right: 9.2857142857rem!important; }
  .rtl .tl-l-140 {
    right: 10rem!important; }
  .rtl .tl-l-150 {
    right: 10.7142857143rem!important; }
  .rtl .tl-l-0 {
    left: auto!important; }
  .rtl .tl-l-10 {
    left: auto!important; }
  .rtl .tl-l-12 {
    left: auto!important; }
  .rtl .tl-l-14 {
    left: auto!important; }
  .rtl .tl-l-16 {
    left: auto!important; }
  .rtl .tl-l-18 {
    left: auto!important; }
  .rtl .tl-l-20 {
    left: auto!important; }
  .rtl .tl-l-22 {
    left: auto!important; }
  .rtl .tl-l-24 {
    left: auto!important; }
  .rtl .tl-l-26 {
    left: auto!important; }
  .rtl .tl-l-30 {
    left: auto!important; }
  .rtl .tl-l-32 {
    left: auto!important; }
  .rtl .tl-l-36 {
    left: auto!important; }
  .rtl .tl-l-38 {
    left: auto!important; }
  .rtl .tl-l-40 {
    left: auto!important; }
  .rtl .tl-l-42 {
    left: auto!important; }
  .rtl .tl-l-46 {
    left: auto!important; }
  .rtl .tl-l-48 {
    left: auto!important; }
  .rtl .tl-l-50 {
    left: auto!important; }
  .rtl .tl-l-60 {
    left: auto!important; }
  .rtl .tl-l-70 {
    left: auto!important; }
  .rtl .tl-l-80 {
    left: auto!important; }
  .rtl .tl-l-90 {
    left: auto!important; }
  .rtl .tl-l-100 {
    left: auto!important; }
  .rtl .tl-l-110 {
    left: auto!important; }
  .rtl .tl-l-120 {
    left: auto!important; }
  .rtl .tl-l-130 {
    left: auto!important; }
  .rtl .tl-l-140 {
    left: auto!important; }
  .rtl .tl-l-150 {
    left: auto!important; } }
@media (max-width: 991px) {
  .rtl .lg-max-r-0 {
    left: 0rem!important; }
  .rtl .lg-max-r-10 {
    left: 0.7142857143rem!important; }
  .rtl .lg-max-r-12 {
    left: 0.8571428571rem!important; }
  .rtl .lg-max-r-14 {
    left: 1rem!important; }
  .rtl .lg-max-r-16 {
    left: 1.1428571429rem!important; }
  .rtl .lg-max-r-18 {
    left: 1.2857142857rem!important; }
  .rtl .lg-max-r-20 {
    left: 1.4285714286rem!important; }
  .rtl .lg-max-r-22 {
    left: 1.5714285714rem!important; }
  .rtl .lg-max-r-24 {
    left: 1.7142857143rem!important; }
  .rtl .lg-max-r-26 {
    left: 1.8571428571rem!important; }
  .rtl .lg-max-r-30 {
    left: 2.1428571429rem!important; }
  .rtl .lg-max-r-32 {
    left: 2.2857142857rem!important; }
  .rtl .lg-max-r-36 {
    left: 2.5714285714rem!important; }
  .rtl .lg-max-r-38 {
    left: 2.7142857143rem!important; }
  .rtl .lg-max-r-40 {
    left: 2.8571428571rem!important; }
  .rtl .lg-max-r-42 {
    left: 3rem!important; }
  .rtl .lg-max-r-46 {
    left: 3.2857142857rem!important; }
  .rtl .lg-max-r-48 {
    left: 3.4285714286rem!important; }
  .rtl .lg-max-r-50 {
    left: 3.5714285714rem!important; }
  .rtl .lg-max-r-60 {
    left: 4.2857142857rem!important; }
  .rtl .lg-max-r-70 {
    left: 5rem!important; }
  .rtl .lg-max-r-80 {
    left: 5.7142857143rem!important; }
  .rtl .lg-max-r-90 {
    left: 6.4285714286rem!important; }
  .rtl .lg-max-r-100 {
    left: 7.1428571429rem!important; }
  .rtl .lg-max-r-110 {
    left: 7.8571428571rem!important; }
  .rtl .lg-max-r-120 {
    left: 8.5714285714rem!important; }
  .rtl .lg-max-r-130 {
    left: 9.2857142857rem!important; }
  .rtl .lg-max-r-140 {
    left: 10rem!important; }
  .rtl .lg-max-r-150 {
    left: 10.7142857143rem!important; }
  .rtl .lg-max-r-0 {
    right: auto!important; }
  .rtl .lg-max-r-10 {
    right: auto!important; }
  .rtl .lg-max-r-12 {
    right: auto!important; }
  .rtl .lg-max-r-14 {
    right: auto!important; }
  .rtl .lg-max-r-16 {
    right: auto!important; }
  .rtl .lg-max-r-18 {
    right: auto!important; }
  .rtl .lg-max-r-20 {
    right: auto!important; }
  .rtl .lg-max-r-22 {
    right: auto!important; }
  .rtl .lg-max-r-24 {
    right: auto!important; }
  .rtl .lg-max-r-26 {
    right: auto!important; }
  .rtl .lg-max-r-30 {
    right: auto!important; }
  .rtl .lg-max-r-32 {
    right: auto!important; }
  .rtl .lg-max-r-36 {
    right: auto!important; }
  .rtl .lg-max-r-38 {
    right: auto!important; }
  .rtl .lg-max-r-40 {
    right: auto!important; }
  .rtl .lg-max-r-42 {
    right: auto!important; }
  .rtl .lg-max-r-46 {
    right: auto!important; }
  .rtl .lg-max-r-48 {
    right: auto!important; }
  .rtl .lg-max-r-50 {
    right: auto!important; }
  .rtl .lg-max-r-60 {
    right: auto!important; }
  .rtl .lg-max-r-70 {
    right: auto!important; }
  .rtl .lg-max-r-80 {
    right: auto!important; }
  .rtl .lg-max-r-90 {
    right: auto!important; }
  .rtl .lg-max-r-100 {
    right: auto!important; }
  .rtl .lg-max-r-110 {
    right: auto!important; }
  .rtl .lg-max-r-120 {
    right: auto!important; }
  .rtl .lg-max-r-130 {
    right: auto!important; }
  .rtl .lg-max-r-140 {
    right: auto!important; }
  .rtl .lg-max-r-150 {
    right: auto!important; }
  .rtl .lg-max-l-0 {
    right: 0rem!important; }
  .rtl .lg-max-l-10 {
    right: 0.7142857143rem!important; }
  .rtl .lg-max-l-12 {
    right: 0.8571428571rem!important; }
  .rtl .lg-max-l-14 {
    right: 1rem!important; }
  .rtl .lg-max-l-16 {
    right: 1.1428571429rem!important; }
  .rtl .lg-max-l-18 {
    right: 1.2857142857rem!important; }
  .rtl .lg-max-l-20 {
    right: 1.4285714286rem!important; }
  .rtl .lg-max-l-22 {
    right: 1.5714285714rem!important; }
  .rtl .lg-max-l-24 {
    right: 1.7142857143rem!important; }
  .rtl .lg-max-l-26 {
    right: 1.8571428571rem!important; }
  .rtl .lg-max-l-30 {
    right: 2.1428571429rem!important; }
  .rtl .lg-max-l-32 {
    right: 2.2857142857rem!important; }
  .rtl .lg-max-l-36 {
    right: 2.5714285714rem!important; }
  .rtl .lg-max-l-38 {
    right: 2.7142857143rem!important; }
  .rtl .lg-max-l-40 {
    right: 2.8571428571rem!important; }
  .rtl .lg-max-l-42 {
    right: 3rem!important; }
  .rtl .lg-max-l-46 {
    right: 3.2857142857rem!important; }
  .rtl .lg-max-l-48 {
    right: 3.4285714286rem!important; }
  .rtl .lg-max-l-50 {
    right: 3.5714285714rem!important; }
  .rtl .lg-max-l-60 {
    right: 4.2857142857rem!important; }
  .rtl .lg-max-l-70 {
    right: 5rem!important; }
  .rtl .lg-max-l-80 {
    right: 5.7142857143rem!important; }
  .rtl .lg-max-l-90 {
    right: 6.4285714286rem!important; }
  .rtl .lg-max-l-100 {
    right: 7.1428571429rem!important; }
  .rtl .lg-max-l-110 {
    right: 7.8571428571rem!important; }
  .rtl .lg-max-l-120 {
    right: 8.5714285714rem!important; }
  .rtl .lg-max-l-130 {
    right: 9.2857142857rem!important; }
  .rtl .lg-max-l-140 {
    right: 10rem!important; }
  .rtl .lg-max-l-150 {
    right: 10.7142857143rem!important; }
  .rtl .lg-max-l-0 {
    left: auto!important; }
  .rtl .lg-max-l-10 {
    left: auto!important; }
  .rtl .lg-max-l-12 {
    left: auto!important; }
  .rtl .lg-max-l-14 {
    left: auto!important; }
  .rtl .lg-max-l-16 {
    left: auto!important; }
  .rtl .lg-max-l-18 {
    left: auto!important; }
  .rtl .lg-max-l-20 {
    left: auto!important; }
  .rtl .lg-max-l-22 {
    left: auto!important; }
  .rtl .lg-max-l-24 {
    left: auto!important; }
  .rtl .lg-max-l-26 {
    left: auto!important; }
  .rtl .lg-max-l-30 {
    left: auto!important; }
  .rtl .lg-max-l-32 {
    left: auto!important; }
  .rtl .lg-max-l-36 {
    left: auto!important; }
  .rtl .lg-max-l-38 {
    left: auto!important; }
  .rtl .lg-max-l-40 {
    left: auto!important; }
  .rtl .lg-max-l-42 {
    left: auto!important; }
  .rtl .lg-max-l-46 {
    left: auto!important; }
  .rtl .lg-max-l-48 {
    left: auto!important; }
  .rtl .lg-max-l-50 {
    left: auto!important; }
  .rtl .lg-max-l-60 {
    left: auto!important; }
  .rtl .lg-max-l-70 {
    left: auto!important; }
  .rtl .lg-max-l-80 {
    left: auto!important; }
  .rtl .lg-max-l-90 {
    left: auto!important; }
  .rtl .lg-max-l-100 {
    left: auto!important; }
  .rtl .lg-max-l-110 {
    left: auto!important; }
  .rtl .lg-max-l-120 {
    left: auto!important; }
  .rtl .lg-max-l-130 {
    left: auto!important; }
  .rtl .lg-max-l-140 {
    left: auto!important; }
  .rtl .lg-max-l-150 {
    left: auto!important; } }
@media (min-width: 1200px) {
  .rtl .xl-r-0 {
    left: 0rem!important; }
  .rtl .xl-r-10 {
    left: 0.7142857143rem!important; }
  .rtl .xl-r-12 {
    left: 0.8571428571rem!important; }
  .rtl .xl-r-14 {
    left: 1rem!important; }
  .rtl .xl-r-16 {
    left: 1.1428571429rem!important; }
  .rtl .xl-r-18 {
    left: 1.2857142857rem!important; }
  .rtl .xl-r-20 {
    left: 1.4285714286rem!important; }
  .rtl .xl-r-22 {
    left: 1.5714285714rem!important; }
  .rtl .xl-r-24 {
    left: 1.7142857143rem!important; }
  .rtl .xl-r-26 {
    left: 1.8571428571rem!important; }
  .rtl .xl-r-30 {
    left: 2.1428571429rem!important; }
  .rtl .xl-r-32 {
    left: 2.2857142857rem!important; }
  .rtl .xl-r-36 {
    left: 2.5714285714rem!important; }
  .rtl .xl-r-38 {
    left: 2.7142857143rem!important; }
  .rtl .xl-r-40 {
    left: 2.8571428571rem!important; }
  .rtl .xl-r-42 {
    left: 3rem!important; }
  .rtl .xl-r-46 {
    left: 3.2857142857rem!important; }
  .rtl .xl-r-48 {
    left: 3.4285714286rem!important; }
  .rtl .xl-r-50 {
    left: 3.5714285714rem!important; }
  .rtl .xl-r-60 {
    left: 4.2857142857rem!important; }
  .rtl .xl-r-70 {
    left: 5rem!important; }
  .rtl .xl-r-80 {
    left: 5.7142857143rem!important; }
  .rtl .xl-r-90 {
    left: 6.4285714286rem!important; }
  .rtl .xl-r-100 {
    left: 7.1428571429rem!important; }
  .rtl .xl-r-110 {
    left: 7.8571428571rem!important; }
  .rtl .xl-r-120 {
    left: 8.5714285714rem!important; }
  .rtl .xl-r-130 {
    left: 9.2857142857rem!important; }
  .rtl .xl-r-140 {
    left: 10rem!important; }
  .rtl .xl-r-150 {
    left: 10.7142857143rem!important; }
  .rtl .xl-r-0 {
    right: auto!important; }
  .rtl .xl-r-10 {
    right: auto!important; }
  .rtl .xl-r-12 {
    right: auto!important; }
  .rtl .xl-r-14 {
    right: auto!important; }
  .rtl .xl-r-16 {
    right: auto!important; }
  .rtl .xl-r-18 {
    right: auto!important; }
  .rtl .xl-r-20 {
    right: auto!important; }
  .rtl .xl-r-22 {
    right: auto!important; }
  .rtl .xl-r-24 {
    right: auto!important; }
  .rtl .xl-r-26 {
    right: auto!important; }
  .rtl .xl-r-30 {
    right: auto!important; }
  .rtl .xl-r-32 {
    right: auto!important; }
  .rtl .xl-r-36 {
    right: auto!important; }
  .rtl .xl-r-38 {
    right: auto!important; }
  .rtl .xl-r-40 {
    right: auto!important; }
  .rtl .xl-r-42 {
    right: auto!important; }
  .rtl .xl-r-46 {
    right: auto!important; }
  .rtl .xl-r-48 {
    right: auto!important; }
  .rtl .xl-r-50 {
    right: auto!important; }
  .rtl .xl-r-60 {
    right: auto!important; }
  .rtl .xl-r-70 {
    right: auto!important; }
  .rtl .xl-r-80 {
    right: auto!important; }
  .rtl .xl-r-90 {
    right: auto!important; }
  .rtl .xl-r-100 {
    right: auto!important; }
  .rtl .xl-r-110 {
    right: auto!important; }
  .rtl .xl-r-120 {
    right: auto!important; }
  .rtl .xl-r-130 {
    right: auto!important; }
  .rtl .xl-r-140 {
    right: auto!important; }
  .rtl .xl-r-150 {
    right: auto!important; }
  .rtl .xl-l-0 {
    right: 0rem!important; }
  .rtl .xl-l-10 {
    right: 0.7142857143rem!important; }
  .rtl .xl-l-12 {
    right: 0.8571428571rem!important; }
  .rtl .xl-l-14 {
    right: 1rem!important; }
  .rtl .xl-l-16 {
    right: 1.1428571429rem!important; }
  .rtl .xl-l-18 {
    right: 1.2857142857rem!important; }
  .rtl .xl-l-20 {
    right: 1.4285714286rem!important; }
  .rtl .xl-l-22 {
    right: 1.5714285714rem!important; }
  .rtl .xl-l-24 {
    right: 1.7142857143rem!important; }
  .rtl .xl-l-26 {
    right: 1.8571428571rem!important; }
  .rtl .xl-l-30 {
    right: 2.1428571429rem!important; }
  .rtl .xl-l-32 {
    right: 2.2857142857rem!important; }
  .rtl .xl-l-36 {
    right: 2.5714285714rem!important; }
  .rtl .xl-l-38 {
    right: 2.7142857143rem!important; }
  .rtl .xl-l-40 {
    right: 2.8571428571rem!important; }
  .rtl .xl-l-42 {
    right: 3rem!important; }
  .rtl .xl-l-46 {
    right: 3.2857142857rem!important; }
  .rtl .xl-l-48 {
    right: 3.4285714286rem!important; }
  .rtl .xl-l-50 {
    right: 3.5714285714rem!important; }
  .rtl .xl-l-60 {
    right: 4.2857142857rem!important; }
  .rtl .xl-l-70 {
    right: 5rem!important; }
  .rtl .xl-l-80 {
    right: 5.7142857143rem!important; }
  .rtl .xl-l-90 {
    right: 6.4285714286rem!important; }
  .rtl .xl-l-100 {
    right: 7.1428571429rem!important; }
  .rtl .xl-l-110 {
    right: 7.8571428571rem!important; }
  .rtl .xl-l-120 {
    right: 8.5714285714rem!important; }
  .rtl .xl-l-130 {
    right: 9.2857142857rem!important; }
  .rtl .xl-l-140 {
    right: 10rem!important; }
  .rtl .xl-l-150 {
    right: 10.7142857143rem!important; }
  .rtl .xl-l-0 {
    left: auto!important; }
  .rtl .xl-l-10 {
    left: auto!important; }
  .rtl .xl-l-12 {
    left: auto!important; }
  .rtl .xl-l-14 {
    left: auto!important; }
  .rtl .xl-l-16 {
    left: auto!important; }
  .rtl .xl-l-18 {
    left: auto!important; }
  .rtl .xl-l-20 {
    left: auto!important; }
  .rtl .xl-l-22 {
    left: auto!important; }
  .rtl .xl-l-24 {
    left: auto!important; }
  .rtl .xl-l-26 {
    left: auto!important; }
  .rtl .xl-l-30 {
    left: auto!important; }
  .rtl .xl-l-32 {
    left: auto!important; }
  .rtl .xl-l-36 {
    left: auto!important; }
  .rtl .xl-l-38 {
    left: auto!important; }
  .rtl .xl-l-40 {
    left: auto!important; }
  .rtl .xl-l-42 {
    left: auto!important; }
  .rtl .xl-l-46 {
    left: auto!important; }
  .rtl .xl-l-48 {
    left: auto!important; }
  .rtl .xl-l-50 {
    left: auto!important; }
  .rtl .xl-l-60 {
    left: auto!important; }
  .rtl .xl-l-70 {
    left: auto!important; }
  .rtl .xl-l-80 {
    left: auto!important; }
  .rtl .xl-l-90 {
    left: auto!important; }
  .rtl .xl-l-100 {
    left: auto!important; }
  .rtl .xl-l-110 {
    left: auto!important; }
  .rtl .xl-l-120 {
    left: auto!important; }
  .rtl .xl-l-130 {
    left: auto!important; }
  .rtl .xl-l-140 {
    left: auto!important; }
  .rtl .xl-l-150 {
    left: auto!important; } }
@media (min-width: 1440px) {
  .rtl .xxl-r-0 {
    left: 0rem!important; }
  .rtl .xxl-r-10 {
    left: 0.7142857143rem!important; }
  .rtl .xxl-r-12 {
    left: 0.8571428571rem!important; }
  .rtl .xxl-r-14 {
    left: 1rem!important; }
  .rtl .xxl-r-16 {
    left: 1.1428571429rem!important; }
  .rtl .xxl-r-18 {
    left: 1.2857142857rem!important; }
  .rtl .xxl-r-20 {
    left: 1.4285714286rem!important; }
  .rtl .xxl-r-22 {
    left: 1.5714285714rem!important; }
  .rtl .xxl-r-24 {
    left: 1.7142857143rem!important; }
  .rtl .xxl-r-26 {
    left: 1.8571428571rem!important; }
  .rtl .xxl-r-30 {
    left: 2.1428571429rem!important; }
  .rtl .xxl-r-32 {
    left: 2.2857142857rem!important; }
  .rtl .xxl-r-36 {
    left: 2.5714285714rem!important; }
  .rtl .xxl-r-38 {
    left: 2.7142857143rem!important; }
  .rtl .xxl-r-40 {
    left: 2.8571428571rem!important; }
  .rtl .xxl-r-42 {
    left: 3rem!important; }
  .rtl .xxl-r-46 {
    left: 3.2857142857rem!important; }
  .rtl .xxl-r-48 {
    left: 3.4285714286rem!important; }
  .rtl .xxl-r-50 {
    left: 3.5714285714rem!important; }
  .rtl .xxl-r-60 {
    left: 4.2857142857rem!important; }
  .rtl .xxl-r-70 {
    left: 5rem!important; }
  .rtl .xxl-r-80 {
    left: 5.7142857143rem!important; }
  .rtl .xxl-r-90 {
    left: 6.4285714286rem!important; }
  .rtl .xxl-r-100 {
    left: 7.1428571429rem!important; }
  .rtl .xxl-r-110 {
    left: 7.8571428571rem!important; }
  .rtl .xxl-r-120 {
    left: 8.5714285714rem!important; }
  .rtl .xxl-r-130 {
    left: 9.2857142857rem!important; }
  .rtl .xxl-r-140 {
    left: 10rem!important; }
  .rtl .xxl-r-150 {
    left: 10.7142857143rem!important; }
  .rtl .xxl-r-0 {
    right: auto!important; }
  .rtl .xxl-r-10 {
    right: auto!important; }
  .rtl .xxl-r-12 {
    right: auto!important; }
  .rtl .xxl-r-14 {
    right: auto!important; }
  .rtl .xxl-r-16 {
    right: auto!important; }
  .rtl .xxl-r-18 {
    right: auto!important; }
  .rtl .xxl-r-20 {
    right: auto!important; }
  .rtl .xxl-r-22 {
    right: auto!important; }
  .rtl .xxl-r-24 {
    right: auto!important; }
  .rtl .xxl-r-26 {
    right: auto!important; }
  .rtl .xxl-r-30 {
    right: auto!important; }
  .rtl .xxl-r-32 {
    right: auto!important; }
  .rtl .xxl-r-36 {
    right: auto!important; }
  .rtl .xxl-r-38 {
    right: auto!important; }
  .rtl .xxl-r-40 {
    right: auto!important; }
  .rtl .xxl-r-42 {
    right: auto!important; }
  .rtl .xxl-r-46 {
    right: auto!important; }
  .rtl .xxl-r-48 {
    right: auto!important; }
  .rtl .xxl-r-50 {
    right: auto!important; }
  .rtl .xxl-r-60 {
    right: auto!important; }
  .rtl .xxl-r-70 {
    right: auto!important; }
  .rtl .xxl-r-80 {
    right: auto!important; }
  .rtl .xxl-r-90 {
    right: auto!important; }
  .rtl .xxl-r-100 {
    right: auto!important; }
  .rtl .xxl-r-110 {
    right: auto!important; }
  .rtl .xxl-r-120 {
    right: auto!important; }
  .rtl .xxl-r-130 {
    right: auto!important; }
  .rtl .xxl-r-140 {
    right: auto!important; }
  .rtl .xxl-r-150 {
    right: auto!important; }
  .rtl .xxl-l-0 {
    right: 0rem!important; }
  .rtl .xxl-l-10 {
    right: 0.7142857143rem!important; }
  .rtl .xxl-l-12 {
    right: 0.8571428571rem!important; }
  .rtl .xxl-l-14 {
    right: 1rem!important; }
  .rtl .xxl-l-16 {
    right: 1.1428571429rem!important; }
  .rtl .xxl-l-18 {
    right: 1.2857142857rem!important; }
  .rtl .xxl-l-20 {
    right: 1.4285714286rem!important; }
  .rtl .xxl-l-22 {
    right: 1.5714285714rem!important; }
  .rtl .xxl-l-24 {
    right: 1.7142857143rem!important; }
  .rtl .xxl-l-26 {
    right: 1.8571428571rem!important; }
  .rtl .xxl-l-30 {
    right: 2.1428571429rem!important; }
  .rtl .xxl-l-32 {
    right: 2.2857142857rem!important; }
  .rtl .xxl-l-36 {
    right: 2.5714285714rem!important; }
  .rtl .xxl-l-38 {
    right: 2.7142857143rem!important; }
  .rtl .xxl-l-40 {
    right: 2.8571428571rem!important; }
  .rtl .xxl-l-42 {
    right: 3rem!important; }
  .rtl .xxl-l-46 {
    right: 3.2857142857rem!important; }
  .rtl .xxl-l-48 {
    right: 3.4285714286rem!important; }
  .rtl .xxl-l-50 {
    right: 3.5714285714rem!important; }
  .rtl .xxl-l-60 {
    right: 4.2857142857rem!important; }
  .rtl .xxl-l-70 {
    right: 5rem!important; }
  .rtl .xxl-l-80 {
    right: 5.7142857143rem!important; }
  .rtl .xxl-l-90 {
    right: 6.4285714286rem!important; }
  .rtl .xxl-l-100 {
    right: 7.1428571429rem!important; }
  .rtl .xxl-l-110 {
    right: 7.8571428571rem!important; }
  .rtl .xxl-l-120 {
    right: 8.5714285714rem!important; }
  .rtl .xxl-l-130 {
    right: 9.2857142857rem!important; }
  .rtl .xxl-l-140 {
    right: 10rem!important; }
  .rtl .xxl-l-150 {
    right: 10.7142857143rem!important; }
  .rtl .xxl-l-0 {
    left: auto!important; }
  .rtl .xxl-l-10 {
    left: auto!important; }
  .rtl .xxl-l-12 {
    left: auto!important; }
  .rtl .xxl-l-14 {
    left: auto!important; }
  .rtl .xxl-l-16 {
    left: auto!important; }
  .rtl .xxl-l-18 {
    left: auto!important; }
  .rtl .xxl-l-20 {
    left: auto!important; }
  .rtl .xxl-l-22 {
    left: auto!important; }
  .rtl .xxl-l-24 {
    left: auto!important; }
  .rtl .xxl-l-26 {
    left: auto!important; }
  .rtl .xxl-l-30 {
    left: auto!important; }
  .rtl .xxl-l-32 {
    left: auto!important; }
  .rtl .xxl-l-36 {
    left: auto!important; }
  .rtl .xxl-l-38 {
    left: auto!important; }
  .rtl .xxl-l-40 {
    left: auto!important; }
  .rtl .xxl-l-42 {
    left: auto!important; }
  .rtl .xxl-l-46 {
    left: auto!important; }
  .rtl .xxl-l-48 {
    left: auto!important; }
  .rtl .xxl-l-50 {
    left: auto!important; }
  .rtl .xxl-l-60 {
    left: auto!important; }
  .rtl .xxl-l-70 {
    left: auto!important; }
  .rtl .xxl-l-80 {
    left: auto!important; }
  .rtl .xxl-l-90 {
    left: auto!important; }
  .rtl .xxl-l-100 {
    left: auto!important; }
  .rtl .xxl-l-110 {
    left: auto!important; }
  .rtl .xxl-l-120 {
    left: auto!important; }
  .rtl .xxl-l-130 {
    left: auto!important; }
  .rtl .xxl-l-140 {
    left: auto!important; }
  .rtl .xxl-l-150 {
    left: auto!important; } }
@media (min-width: 1599px) {
  .rtl .xxxl-r-0 {
    left: 0rem!important; }
  .rtl .xxxl-r-10 {
    left: 0.7142857143rem!important; }
  .rtl .xxxl-r-12 {
    left: 0.8571428571rem!important; }
  .rtl .xxxl-r-14 {
    left: 1rem!important; }
  .rtl .xxxl-r-16 {
    left: 1.1428571429rem!important; }
  .rtl .xxxl-r-18 {
    left: 1.2857142857rem!important; }
  .rtl .xxxl-r-20 {
    left: 1.4285714286rem!important; }
  .rtl .xxxl-r-22 {
    left: 1.5714285714rem!important; }
  .rtl .xxxl-r-24 {
    left: 1.7142857143rem!important; }
  .rtl .xxxl-r-26 {
    left: 1.8571428571rem!important; }
  .rtl .xxxl-r-30 {
    left: 2.1428571429rem!important; }
  .rtl .xxxl-r-32 {
    left: 2.2857142857rem!important; }
  .rtl .xxxl-r-36 {
    left: 2.5714285714rem!important; }
  .rtl .xxxl-r-38 {
    left: 2.7142857143rem!important; }
  .rtl .xxxl-r-40 {
    left: 2.8571428571rem!important; }
  .rtl .xxxl-r-42 {
    left: 3rem!important; }
  .rtl .xxxl-r-46 {
    left: 3.2857142857rem!important; }
  .rtl .xxxl-r-48 {
    left: 3.4285714286rem!important; }
  .rtl .xxxl-r-50 {
    left: 3.5714285714rem!important; }
  .rtl .xxxl-r-60 {
    left: 4.2857142857rem!important; }
  .rtl .xxxl-r-70 {
    left: 5rem!important; }
  .rtl .xxxl-r-80 {
    left: 5.7142857143rem!important; }
  .rtl .xxxl-r-90 {
    left: 6.4285714286rem!important; }
  .rtl .xxxl-r-100 {
    left: 7.1428571429rem!important; }
  .rtl .xxxl-r-110 {
    left: 7.8571428571rem!important; }
  .rtl .xxxl-r-120 {
    left: 8.5714285714rem!important; }
  .rtl .xxxl-r-130 {
    left: 9.2857142857rem!important; }
  .rtl .xxxl-r-140 {
    left: 10rem!important; }
  .rtl .xxxl-r-150 {
    left: 10.7142857143rem!important; }
  .rtl .xxxl-r-0 {
    right: auto!important; }
  .rtl .xxxl-r-10 {
    right: auto!important; }
  .rtl .xxxl-r-12 {
    right: auto!important; }
  .rtl .xxxl-r-14 {
    right: auto!important; }
  .rtl .xxxl-r-16 {
    right: auto!important; }
  .rtl .xxxl-r-18 {
    right: auto!important; }
  .rtl .xxxl-r-20 {
    right: auto!important; }
  .rtl .xxxl-r-22 {
    right: auto!important; }
  .rtl .xxxl-r-24 {
    right: auto!important; }
  .rtl .xxxl-r-26 {
    right: auto!important; }
  .rtl .xxxl-r-30 {
    right: auto!important; }
  .rtl .xxxl-r-32 {
    right: auto!important; }
  .rtl .xxxl-r-36 {
    right: auto!important; }
  .rtl .xxxl-r-38 {
    right: auto!important; }
  .rtl .xxxl-r-40 {
    right: auto!important; }
  .rtl .xxxl-r-42 {
    right: auto!important; }
  .rtl .xxxl-r-46 {
    right: auto!important; }
  .rtl .xxxl-r-48 {
    right: auto!important; }
  .rtl .xxxl-r-50 {
    right: auto!important; }
  .rtl .xxxl-r-60 {
    right: auto!important; }
  .rtl .xxxl-r-70 {
    right: auto!important; }
  .rtl .xxxl-r-80 {
    right: auto!important; }
  .rtl .xxxl-r-90 {
    right: auto!important; }
  .rtl .xxxl-r-100 {
    right: auto!important; }
  .rtl .xxxl-r-110 {
    right: auto!important; }
  .rtl .xxxl-r-120 {
    right: auto!important; }
  .rtl .xxxl-r-130 {
    right: auto!important; }
  .rtl .xxxl-r-140 {
    right: auto!important; }
  .rtl .xxxl-r-150 {
    right: auto!important; }
  .rtl .xxxl-l-0 {
    right: 0rem!important; }
  .rtl .xxxl-l-10 {
    right: 0.7142857143rem!important; }
  .rtl .xxxl-l-12 {
    right: 0.8571428571rem!important; }
  .rtl .xxxl-l-14 {
    right: 1rem!important; }
  .rtl .xxxl-l-16 {
    right: 1.1428571429rem!important; }
  .rtl .xxxl-l-18 {
    right: 1.2857142857rem!important; }
  .rtl .xxxl-l-20 {
    right: 1.4285714286rem!important; }
  .rtl .xxxl-l-22 {
    right: 1.5714285714rem!important; }
  .rtl .xxxl-l-24 {
    right: 1.7142857143rem!important; }
  .rtl .xxxl-l-26 {
    right: 1.8571428571rem!important; }
  .rtl .xxxl-l-30 {
    right: 2.1428571429rem!important; }
  .rtl .xxxl-l-32 {
    right: 2.2857142857rem!important; }
  .rtl .xxxl-l-36 {
    right: 2.5714285714rem!important; }
  .rtl .xxxl-l-38 {
    right: 2.7142857143rem!important; }
  .rtl .xxxl-l-40 {
    right: 2.8571428571rem!important; }
  .rtl .xxxl-l-42 {
    right: 3rem!important; }
  .rtl .xxxl-l-46 {
    right: 3.2857142857rem!important; }
  .rtl .xxxl-l-48 {
    right: 3.4285714286rem!important; }
  .rtl .xxxl-l-50 {
    right: 3.5714285714rem!important; }
  .rtl .xxxl-l-60 {
    right: 4.2857142857rem!important; }
  .rtl .xxxl-l-70 {
    right: 5rem!important; }
  .rtl .xxxl-l-80 {
    right: 5.7142857143rem!important; }
  .rtl .xxxl-l-90 {
    right: 6.4285714286rem!important; }
  .rtl .xxxl-l-100 {
    right: 7.1428571429rem!important; }
  .rtl .xxxl-l-110 {
    right: 7.8571428571rem!important; }
  .rtl .xxxl-l-120 {
    right: 8.5714285714rem!important; }
  .rtl .xxxl-l-130 {
    right: 9.2857142857rem!important; }
  .rtl .xxxl-l-140 {
    right: 10rem!important; }
  .rtl .xxxl-l-150 {
    right: 10.7142857143rem!important; }
  .rtl .xxxl-l-0 {
    left: auto!important; }
  .rtl .xxxl-l-10 {
    left: auto!important; }
  .rtl .xxxl-l-12 {
    left: auto!important; }
  .rtl .xxxl-l-14 {
    left: auto!important; }
  .rtl .xxxl-l-16 {
    left: auto!important; }
  .rtl .xxxl-l-18 {
    left: auto!important; }
  .rtl .xxxl-l-20 {
    left: auto!important; }
  .rtl .xxxl-l-22 {
    left: auto!important; }
  .rtl .xxxl-l-24 {
    left: auto!important; }
  .rtl .xxxl-l-26 {
    left: auto!important; }
  .rtl .xxxl-l-30 {
    left: auto!important; }
  .rtl .xxxl-l-32 {
    left: auto!important; }
  .rtl .xxxl-l-36 {
    left: auto!important; }
  .rtl .xxxl-l-38 {
    left: auto!important; }
  .rtl .xxxl-l-40 {
    left: auto!important; }
  .rtl .xxxl-l-42 {
    left: auto!important; }
  .rtl .xxxl-l-46 {
    left: auto!important; }
  .rtl .xxxl-l-48 {
    left: auto!important; }
  .rtl .xxxl-l-50 {
    left: auto!important; }
  .rtl .xxxl-l-60 {
    left: auto!important; }
  .rtl .xxxl-l-70 {
    left: auto!important; }
  .rtl .xxxl-l-80 {
    left: auto!important; }
  .rtl .xxxl-l-90 {
    left: auto!important; }
  .rtl .xxxl-l-100 {
    left: auto!important; }
  .rtl .xxxl-l-110 {
    left: auto!important; }
  .rtl .xxxl-l-120 {
    left: auto!important; }
  .rtl .xxxl-l-130 {
    left: auto!important; }
  .rtl .xxxl-l-140 {
    left: auto!important; }
  .rtl .xxxl-l-150 {
    left: auto!important; } }
.rtl .r-0 {
  left: 0rem!important; }
.rtl .r-10 {
  left: 0.7142857143rem!important; }
.rtl .r-12 {
  left: 0.8571428571rem!important; }
.rtl .r-14 {
  left: 1rem!important; }
.rtl .r-16 {
  left: 1.1428571429rem!important; }
.rtl .r-18 {
  left: 1.2857142857rem!important; }
.rtl .r-20 {
  left: 1.4285714286rem!important; }
.rtl .r-22 {
  left: 1.5714285714rem!important; }
.rtl .r-24 {
  left: 1.7142857143rem!important; }
.rtl .r-26 {
  left: 1.8571428571rem!important; }
.rtl .r-30 {
  left: 2.1428571429rem!important; }
.rtl .r-32 {
  left: 2.2857142857rem!important; }
.rtl .r-36 {
  left: 2.5714285714rem!important; }
.rtl .r-38 {
  left: 2.7142857143rem!important; }
.rtl .r-40 {
  left: 2.8571428571rem!important; }
.rtl .r-42 {
  left: 3rem!important; }
.rtl .r-46 {
  left: 3.2857142857rem!important; }
.rtl .r-48 {
  left: 3.4285714286rem!important; }
.rtl .r-50 {
  left: 3.5714285714rem!important; }
.rtl .r-60 {
  left: 4.2857142857rem!important; }
.rtl .r-70 {
  left: 5rem!important; }
.rtl .r-80 {
  left: 5.7142857143rem!important; }
.rtl .r-90 {
  left: 6.4285714286rem!important; }
.rtl .r-100 {
  left: 7.1428571429rem!important; }
.rtl .r-110 {
  left: 7.8571428571rem!important; }
.rtl .r-120 {
  left: 8.5714285714rem!important; }
.rtl .r-130 {
  left: 9.2857142857rem!important; }
.rtl .r-140 {
  left: 10rem!important; }
.rtl .r-150 {
  left: 10.7142857143rem!important; }
.rtl .r-0 {
  right: auto!important; }
.rtl .r-10 {
  right: auto!important; }
.rtl .r-12 {
  right: auto!important; }
.rtl .r-14 {
  right: auto!important; }
.rtl .r-16 {
  right: auto!important; }
.rtl .r-18 {
  right: auto!important; }
.rtl .r-20 {
  right: auto!important; }
.rtl .r-22 {
  right: auto!important; }
.rtl .r-24 {
  right: auto!important; }
.rtl .r-26 {
  right: auto!important; }
.rtl .r-30 {
  right: auto!important; }
.rtl .r-32 {
  right: auto!important; }
.rtl .r-36 {
  right: auto!important; }
.rtl .r-38 {
  right: auto!important; }
.rtl .r-40 {
  right: auto!important; }
.rtl .r-42 {
  right: auto!important; }
.rtl .r-46 {
  right: auto!important; }
.rtl .r-48 {
  right: auto!important; }
.rtl .r-50 {
  right: auto!important; }
.rtl .r-60 {
  right: auto!important; }
.rtl .r-70 {
  right: auto!important; }
.rtl .r-80 {
  right: auto!important; }
.rtl .r-90 {
  right: auto!important; }
.rtl .r-100 {
  right: auto!important; }
.rtl .r-110 {
  right: auto!important; }
.rtl .r-120 {
  right: auto!important; }
.rtl .r-130 {
  right: auto!important; }
.rtl .r-140 {
  right: auto!important; }
.rtl .r-150 {
  right: auto!important; }
.rtl .l-0 {
  right: 0rem!important; }
.rtl .l-10 {
  right: 0.7142857143rem!important; }
.rtl .l-12 {
  right: 0.8571428571rem!important; }
.rtl .l-14 {
  right: 1rem!important; }
.rtl .l-16 {
  right: 1.1428571429rem!important; }
.rtl .l-18 {
  right: 1.2857142857rem!important; }
.rtl .l-20 {
  right: 1.4285714286rem!important; }
.rtl .l-22 {
  right: 1.5714285714rem!important; }
.rtl .l-24 {
  right: 1.7142857143rem!important; }
.rtl .l-26 {
  right: 1.8571428571rem!important; }
.rtl .l-30 {
  right: 2.1428571429rem!important; }
.rtl .l-32 {
  right: 2.2857142857rem!important; }
.rtl .l-36 {
  right: 2.5714285714rem!important; }
.rtl .l-38 {
  right: 2.7142857143rem!important; }
.rtl .l-40 {
  right: 2.8571428571rem!important; }
.rtl .l-42 {
  right: 3rem!important; }
.rtl .l-46 {
  right: 3.2857142857rem!important; }
.rtl .l-48 {
  right: 3.4285714286rem!important; }
.rtl .l-50 {
  right: 3.5714285714rem!important; }
.rtl .l-60 {
  right: 4.2857142857rem!important; }
.rtl .l-70 {
  right: 5rem!important; }
.rtl .l-80 {
  right: 5.7142857143rem!important; }
.rtl .l-90 {
  right: 6.4285714286rem!important; }
.rtl .l-100 {
  right: 7.1428571429rem!important; }
.rtl .l-110 {
  right: 7.8571428571rem!important; }
.rtl .l-120 {
  right: 8.5714285714rem!important; }
.rtl .l-130 {
  right: 9.2857142857rem!important; }
.rtl .l-140 {
  right: 10rem!important; }
.rtl .l-150 {
  right: 10.7142857143rem!important; }
.rtl .l-0 {
  left: auto!important; }
.rtl .l-10 {
  left: auto!important; }
.rtl .l-12 {
  left: auto!important; }
.rtl .l-14 {
  left: auto!important; }
.rtl .l-16 {
  left: auto!important; }
.rtl .l-18 {
  left: auto!important; }
.rtl .l-20 {
  left: auto!important; }
.rtl .l-22 {
  left: auto!important; }
.rtl .l-24 {
  left: auto!important; }
.rtl .l-26 {
  left: auto!important; }
.rtl .l-30 {
  left: auto!important; }
.rtl .l-32 {
  left: auto!important; }
.rtl .l-36 {
  left: auto!important; }
.rtl .l-38 {
  left: auto!important; }
.rtl .l-40 {
  left: auto!important; }
.rtl .l-42 {
  left: auto!important; }
.rtl .l-46 {
  left: auto!important; }
.rtl .l-48 {
  left: auto!important; }
.rtl .l-50 {
  left: auto!important; }
.rtl .l-60 {
  left: auto!important; }
.rtl .l-70 {
  left: auto!important; }
.rtl .l-80 {
  left: auto!important; }
.rtl .l-90 {
  left: auto!important; }
.rtl .l-100 {
  left: auto!important; }
.rtl .l-110 {
  left: auto!important; }
.rtl .l-120 {
  left: auto!important; }
.rtl .l-130 {
  left: auto!important; }
.rtl .l-140 {
  left: auto!important; }
.rtl .l-150 {
  left: auto!important; }

.rtl .icon-bar-sticky {
  right: auto;
  left: 0; }
.rtl .topbar .topbar-left ul li:after,
.rtl .topbar .topbar-right ul li:after {
  content: none; }
.rtl .topbar .topbar-left ul li:before,
.rtl .topbar .topbar-right ul li:before {
  position: absolute;
  width: 1px;
  height: 20px;
  background-color: #ffffff;
  left: 0;
  content: "";
  top: 2px;
  opacity: 0.2; }
.rtl .topbar .topbar-right ul li:before {
  right: 0;
  left: auto; }
.rtl .topbar .topbar-right ul li:first-child:before,
.rtl .topbar .topbar-left ul li:last-child:before {
  content: none; }
.rtl .text-dark .topbar .topbar-left ul li:before,
.rtl .text-dark .topbar .topbar-right ul li:before {
  background-color: #172b4c; }
.rtl .topbar-call ul li i {
  margin-right: 0;
  margin-left: 5px; }
.rtl .top-bar .lng-drop .btn-group .btn.dropdown-toggle::after {
  margin-left: 6px; }
.rtl .topbar-social ul li a {
  margin-left: 0;
  margin-right: 5px; }
  .rtl .topbar-social ul li a span {
    margin-right: 0;
    margin-left: 5px; }
.rtl ul.attributes {
  float: left; }
.rtl ul.attributes li {
  float: right; }
.rtl .megamenu-cart .cart-body ul {
  padding-right: 0; }
.rtl nav .cart-body ul li {
  float: none;
  padding-left: 20px;
  padding-right: 90px; }
  .rtl nav .cart-body ul li img {
    right: 20px;
    left: auto; }
.rtl nav .menu .megamenu-content li .menu-title {
  text-align: right; }
.rtl nav .menu .megamenu-content li a {
  text-align: right; }
  .rtl nav .menu .megamenu-content li a i {
    float: right;
    margin-top: 4px; }
.rtl .core-content .wrap-search-fullscreen .close-search {
  left: 40px;
  right: auto; }
.rtl .core-nav .wrap-core-nav-list.right {
  text-align: left; }
  .rtl .core-nav .wrap-core-nav-list.right .megamenu > .megamenu-content {
    left: 0;
    right: auto; }
  .rtl .core-nav .wrap-core-nav-list.right .core-nav-list li {
    float: right; }
.rtl .core-nav .dropdown > .dropdown-menu > li {
  float: none !important; }
  .rtl .core-nav .dropdown > .dropdown-menu > li > a {
    text-align: right; }
@media (min-width: 993px) {
  .rtl .top-bar.left-menu .nav-white .wrap-core-nav-list.right::after {
    display: block;
    clear: both;
    content: ""; }
  .rtl .top-bar.left-menu .nav-white .wrap-core-nav-list.right .menu.core-nav-list {
    float: right; } }
.rtl nav .menu > li.dropdown > a::before, .rtl nav .menu > li.megamenu > a::before, .rtl nav .menu > li.dropdown li.dropdown > a::before {
  margin-left: 0;
  margin-right: 5px;
  float: left; }
@media (max-width: 992px) {
  .rtl nav .nav-header .toggle-bar {
    left: inherit;
    right: 15px; }
  .rtl .core-nav .wrap-core-nav-list .core-nav-list li {
    float: none !important; }
  .rtl .core-nav .wrap-core-nav-list .core-nav-list li a {
    text-align: right; }
  .rtl .core-nav .dropdown .dropdown-menu {
    padding-left: inherit;
    padding-right: 15px; }
  .rtl .core-nav ul.attributes .megamenu .megamenu-content {
    margin-left: 0 !important;
    left: 5px;
    right: auto; } }

.rtl {
  /*---blogpost start---*/
  /*blog-comment*/
  /*gap*/
  /*---- list-style start ----*/
  /*---widget---*/
  /*------------------accordian-------------------*/
  /* Testimonials */
  /*---blockquote ---*/ }
  .rtl .cours-search .input-group .btn {
    margin-left: 0px !important;
    margin-right: 10px !important; }
  .rtl .owl-carousel, .rtl .flexslider2, .rtl .flexslider, .rtl #chartdiv {
    direction: ltr; }
  .rtl .blog-post .entry-image .blockquote blockquote {
    border-left: inherit;
    border-right: 0px; }
  .rtl .blog-post .entry-meta ul li {
    margin-right: inherit;
    margin-left: 12px; }
    .rtl .blog-post .entry-meta ul li i {
      padding-right: inherit;
      padding-left: 6px; }
    .rtl .blog-post .entry-meta ul li a {
      padding-right: inherit;
      padding-left: 5px; }
      .rtl .blog-post .entry-meta ul li a i {
        padding-right: inherit;
        padding-left: 6px; }
  .rtl .blog-post .social strong {
    margin-right: inherit;
    margin-left: 10px; }
  .rtl .blog-post .grid-post li {
    border-right: 0 solid #ffffff;
    border-left: 4px solid #ffffff; }
    .rtl .blog-post .grid-post li:nth-child(even) {
      border-left: 0px solid #ffffff;
      border-right: 4px solid #ffffff; }
  .rtl .comment-1 .comment-photo {
    margin-right: inherit;
    margin-left: 20px;
    float: right; }
  .rtl .comment-1.comment-2 {
    padding-left: inherit;
    padding-right: 125px; }
    .rtl .comment-1.comment-2 .comment-info {
      padding: 20px 20px 10px; }
  .rtl .gap-items > * {
    margin-left: 8px !important;
    margin-right: 8px !important; }
    .rtl .gap-items > *:first-child {
      margin-left: 0 !important; }
    .rtl .gap-items > *:last-child {
      margin-right: 0 !important; }
  .rtl .gap-items-1 > * {
    margin-left: 2px !important;
    margin-right: 2px !important; }
    .rtl .gap-items-1 > *:first-child {
      margin-right: 0 !important; }
    .rtl .gap-items-1 > *:last-child {
      margin-left: 0 !important; }
  .rtl .gap-items-2 > * {
    margin-left: 4px !important;
    margin-right: 4px !important; }
    .rtl .gap-items-2 > *:first-child {
      margin-right: 0 !important; }
    .rtl .gap-items-2 > *:last-child {
      margin-left: 0 !important; }
  .rtl .gap-items-3 > * {
    margin-left: 8px !important;
    margin-right: 8px !important; }
    .rtl .gap-items-3 > *:first-child {
      margin-right: 0 !important; }
    .rtl .gap-items-3 > *:last-child {
      margin-left: 0 !important; }
  .rtl .gap-items-4 > * {
    margin-left: 12px !important;
    margin-right: 12px !important; }
    .rtl .gap-items-4 > *:first-child {
      margin-right: 0 !important; }
    .rtl .gap-items-4 > *:last-child {
      margin-left: 0 !important; }
  .rtl .gap-items-5 > * {
    margin-left: 16px !important;
    margin-right: 16px !important; }
    .rtl .gap-items-5 > *:first-child {
      margin-right: 0 !important; }
    .rtl .gap-items-5 > *:last-child {
      margin-left: 0 !important; }
  .rtl ul.list li {
    padding-left: inherit;
    padding-right: 24px; }
    .rtl ul.list li:after {
      content: "";
      left: auto;
      right: 0; }
  .rtl ul.list i {
    left: auto;
    right: 0; }
  .rtl ul.list-mark li {
    padding-left: inherit;
    padding-right: 25px; }
    .rtl ul.list-mark li:after {
      content: "\e64c"; }
  .rtl ul.list-arrow li {
    padding-left: inherit;
    padding-right: 25px; }
    .rtl ul.list-arrow li:after {
      content: "\e629"; }
  .rtl ul.list-hand li {
    padding-left: inherit;
    padding-right: 25px; }
    .rtl ul.list-hand li:after {
      content: "\e71c"; }
  .rtl ul.list-edit li {
    padding-left: inherit;
    padding-right: 25px; }
    .rtl ul.list-edit li:after {
      content: "\e61c"; }
  .rtl .input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0; }
  .rtl .input-group:not(.has-validation) > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu), .rtl .input-group:not(.has-validation) > .dropdown-toggle:nth-last-child(n + 3) {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0; }
  .rtl footer .footer-links a:before {
    margin-right: 0;
    display: none; }
  .rtl footer .footer-links a:after {
    content: "\e629";
    font-size: 0.8rem;
    margin-left: 5px;
    position: relative;
    font-family: 'themify';
    opacity: 0.5; }
  .rtl .box-header > .box-tools {
    left: 1.5rem;
    right: auto; }
  .rtl .box-header > .box-controls {
    left: 1.5rem;
    right: auto; }
  .rtl .breadcrumb-item + .breadcrumb-item {
    padding-left: inherit;
    padding-right: 0.5rem; }
    .rtl .breadcrumb-item + .breadcrumb-item::before {
      float: right;
      padding-right: 0;
      padding-left: 0.5rem; }
  .rtl .widget {
    /*Recent Posts*/
    /*widget-testimonial*/ }
    .rtl .widget.courses-search-bx .input-group label {
      left: auto;
      right: 0; }
    .rtl .widget .recent-post .recent-post-image {
      float: right;
      margin-right: inherit;
      margin-left: 15px; }
    .rtl .widget .recent-post .recent-post-info span i {
      padding-right: inherit;
      padding-left: 10px; }
    .rtl .widget .testimonial-widget .testimonial-info .testimonial-avtar {
      padding-right: inherit;
      padding-left: 20px;
      float: right; }
    .rtl .widget .testimonial-widget .testimonial-info .testimonial-name {
      float: right; }
    .rtl .widget .testimonial-widget .testimonial-info::after {
      display: block;
      clear: both;
      content: ""; }
  .rtl [type=checkbox] + label {
    padding-left: 0;
    padding-right: 35px !important; }
    .rtl [type=checkbox] + label:before {
      right: 0;
      left: auto; }
  .rtl [type=checkbox]:not(.filled-in) + label:after {
    right: 0;
    left: auto; }
  .rtl [type=checkbox]:checked, .rtl [type=checkbox]:not(:checked) {
    right: -9999px;
    left: auto; }
  .rtl [type=checkbox]:checked + label:before {
    right: 10px;
    left: auto; }
  .rtl [type=checkbox].filled-in + label:before, .rtl [type=checkbox].filled-in + label:after {
    right: 0;
    left: auto; }
  .rtl [type=radio]:checked + label, .rtl [type=radio]:not(:checked) + label {
    padding-left: 0;
    padding-right: 35px !important; }
  .rtl [type=radio] + label:before, .rtl [type=radio] + label:after {
    right: 0;
    left: auto; }
  .rtl [type=radio]:checked, .rtl [type=radio]:not(:checked) {
    right: -9999px;
    left: auto; }
  .rtl [type="checkbox"].filled-in:checked + label:before {
    right: 10px; }
  .rtl ul.nav.nav-pills {
    padding-right: 0; }
  .rtl ol:not([class]), .rtl ul:not([class]) {
    padding-left: 0;
    padding-right: 2rem; }
  .rtl .nav, .rtl .list-inline, .rtl .cours-star, .rtl .breadcrumb {
    padding-right: 0; }
  .rtl .chart-legend, .rtl .contacts-list, .rtl .list-unstyled, .rtl .mailbox-attachments, .rtl .users-list {
    margin: 0;
    padding: 0; }
  .rtl .pagination {
    padding-left: 0;
    padding-right: 0; }
  .rtl .form-select {
    padding: 0.5rem 0.5rem 0.375rem 1.75rem;
    background-position: left 0.75rem center; }
  .rtl .course-overview li i {
    margin-right: 0;
    margin-left: 10px; }
  .rtl .cust-accordion .tab-wrapper.v1 .tab-btn em {
    float: left; }
  .rtl .pull-right {
    float: left; }
  .rtl .pull-left {
    float: right; }
  .rtl .float-start {
    float: right !important; }
  .rtl .float-end {
    float: left !important; }
  .rtl .widget-user-2 .nav-item .nav-link > span {
    float: right;
    margin-top: 5px; }
  .rtl .external-event i {
    margin-left: 5px;
    margin-right: 0; }
    .rtl .external-event i.fa-hand-o-right:before {
      content: "\f0a5"; }
  .rtl .ribbon-box .ribbon:before {
    right: 0;
    left: auto; }
  .rtl .ribbon-box .ribbon-two span {
    transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
    right: -21px;
    left: auto; }
  .rtl .ribbon-box .ribbon-two {
    right: -5px;
    left: auto; }
  .rtl .testimonial-bx {
    padding: 20px 70px 20px 20px;
    margin-left: 0px;
    margin-right: 30px; }
    .rtl .testimonial-bx .testimonial-info:after {
      right: auto;
      left: 30px; }
    .rtl .testimonial-bx .testimonial-thumb {
      left: auto;
      right: 0; }
  @media (max-width: 767px) {
    .rtl .testimonial-bx {
      padding: 20px 20px 20px 20px;
      margin-left: 0px;
      margin-right: 0px; }
      .rtl .testimonial-bx .testimonial-thumb {
        left: auto;
        right: auto; } }
  .rtl .card-courses-view {
    align-items: flex-start;
    direction: rtl; }
    .rtl .card-courses-view .card-courses-user .card-courses-user-pic {
      margin-left: 10px;
      margin-right: inherit; }
    .rtl .card-courses-view > li {
      padding-left: 20px;
      padding-right: inherit; }
      .rtl .card-courses-view > li:last-child {
        padding-right: inherit;
        padding-left: 0; }
  @media (max-width: 767px) {
    .rtl .card-courses-categories {
      float: left;
      text-align: left; } }
  @media (min-width: 768px) {
    .rtl .dl-horizontal dt {
      float: right;
      clear: right;
      text-align: left; }
    .rtl .dl-horizontal dd {
      margin-left: inherit;
      margin-right: 180px; } }
  .rtl .blockquote {
    border-left: none;
    border-right: 0.25rem solid #f3f6f9; }
  .rtl .blockquote-reverse {
    padding-right: 0;
    padding-left: 1rem;
    text-align: left;
    border-right: 0;
    border-left: 0.25rem solid #f3f6f9; }
  .rtl .info-box-content {
    padding: 10px 0 10px 10px;
    margin-right: 90px;
    margin-left: inherit; }
  .rtl .info-box .progress {
    margin: 5px 0 5px -10px; }
  .rtl .info-box-icon {
    float: right; }
  .rtl .small-box .icon {
    left: 10px;
    right: inherit; }
  .rtl .small-box > .small-box-footer {
    text-align: left; }
  .rtl .owl-carousel.owl-theme .owl-nav {
    right: auto;
    left: -5px; }
  .rtl .dropdown-menu {
    z-index: 9999; }
  .rtl .shop-page .widget .shop-post .shop-post-image {
    float: right;
    margin-right: inherit;
    margin-left: 15px; }
  .rtl .shop-page .widget .shop-post .shop-post-info span i {
    padding-right: inherit;
    padding-left: 10px; }
  .rtl .dataTables_filter {
    float: left; }
  .rtl .dataTables_wrapper .dataTables_paginate {
    float: left; }

.rtl .sector-style .sector-item {
  border-right: 0;
  border-left: 1px solid #f3f6f9; }
  .rtl .sector-style .sector-item:nth-child(4n+0) {
    border-right: 0;
    border-left: 0; }
@media (max-width: 767px) {
  .rtl .sector-style .sector-item:nth-child(2n+0) {
    border-right: 0;
    border-left: 0; } }
@media (max-width: 575px) {
  .rtl .sector-style .sector-item:nth-child(1n+0) {
    border-right: 0;
    border-left: 0; } }

.rtl .modal-header .btn-close {
  margin: 0; }
.rtl .core-nav .wrap-core-nav-list.right .dropdown > .dropdown-menu .dropdown > .dropdown-menu {
  left: auto; }
.rtl .core-nav .wrap-core-nav-list.right .dropdown > .dropdown-menu .dropdown > .dropdown-menu {
  left: 100%;
  right: auto; }

/*# sourceMappingURL=style_rtl.css.map */
