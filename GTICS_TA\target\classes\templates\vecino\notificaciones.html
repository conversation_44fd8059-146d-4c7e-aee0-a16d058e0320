<!DOCTYPE html>
<html lang="es" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notificaciones - Municipalidad de San Miguel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .notification-item {
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }
        .notification-item:hover {
            background-color: #f8f9fa;
            transform: translateX(5px);
        }
        .notification-item.unread {
            border-left-color: #007bff;
            background-color: #f8f9ff;
        }
        .notification-item.read {
            border-left-color: #6c757d;
            opacity: 0.8;
        }
        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }
        .notification-icon.cancelacion {
            background-color: #dc3545;
            color: white;
        }
        .notification-icon.mantenimiento {
            background-color: #ffc107;
            color: #212529;
        }
        .notification-icon.recordatorio {
            background-color: #17a2b8;
            color: white;
        }
        .notification-icon.promocion {
            background-color: #28a745;
            color: white;
        }
        .notification-content {
            flex: 1;
            min-width: 0;
        }
        .notification-title {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }
        .notification-message {
            color: #6c757d;
            font-size: 0.9rem;
            white-space: pre-line;
        }
        .notification-time {
            font-size: 0.8rem;
            color: #adb5bd;
        }
        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
            color: #6c757d;
        }
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" th:href="@{/vecino/}">
                <i class="fas fa-home me-2"></i>
                Municipalidad San Miguel
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/vecino/espacios}">
                            <i class="fas fa-dumbbell me-1"></i>Espacios
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link"th:href="@{/vecino/reservas}">
                            <i class="fas fa-calendar-check me-1"></i>Mis Reservas
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" th:href="@{/vecino/notificaciones}">
                            <i class="fas fa-bell me-1"></i>Notificaciones
                            <span th:if="${noLeidas > 0}" class="badge bg-danger ms-1" th:text="${noLeidas}"></span>
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            <span th:text="${usuario.nombres}"></span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item"th:href="@{/vecino/perfil}">
                                <i class="fas fa-user-edit me-2"></i>Mi Perfil
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" th:href="@{/logout}">
                                <i class="fas fa-sign-out-alt me-2"></i>Cerrar Sesión
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Contenido Principal -->
    <div class="container mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-1">
                            <i class="fas fa-bell text-primary me-2"></i>
                            Mis Notificaciones
                        </h2>
                        <p class="text-muted mb-0">
                            Gestiona tus notificaciones y mantente informado
                        </p>
                    </div>
                    <div th:if="${noLeidas > 0}">
                        <button class="btn btn-outline-primary" onclick="marcarTodasLeidas()">
                            <i class="fas fa-check-double me-2"></i>
                            Marcar todas como leídas
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Estadísticas -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card border-0 bg-primary text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-bell fa-2x mb-2"></i>
                        <h4 th:text="${#lists.size(notificaciones)}">0</h4>
                        <small>Total Notificaciones</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-0 bg-warning text-dark">
                    <div class="card-body text-center">
                        <i class="fas fa-envelope fa-2x mb-2"></i>
                        <h4 th:text="${noLeidas}">0</h4>
                        <small>No Leídas</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-0 bg-success text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                        <h4 th:text="${#lists.size(notificaciones) - noLeidas}">0</h4>
                        <small>Leídas</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Lista de Notificaciones -->
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body p-0">
                        <!-- Notificaciones -->
                        <div th:if="${#lists.isEmpty(notificaciones)}" class="empty-state">
                            <i class="fas fa-bell-slash"></i>
                            <h5>No tienes notificaciones</h5>
                            <p>Cuando recibas notificaciones aparecerán aquí</p>
                        </div>
                        
                        <div th:if="${!#lists.isEmpty(notificaciones)}">
                            <div th:each="notificacion : ${notificaciones}" 
                                 class="notification-item p-3 border-bottom"
                                 th:classappend="${notificacion.leida} ? 'read' : 'unread'"
                                 th:data-id="${notificacion.id}">
                                <div class="d-flex align-items-start">
                                    <!-- Icono -->
                                    <i th:switch="${notificacion.tipoNotificacion.name()}">
                                        <i th:case="'CANCELACION_RESERVA'" class="fas fa-times"></i>
                                        <i th:case="'MANTENIMIENTO_PROGRAMADO'" class="fas fa-tools"></i>
                                        <i th:case="'RECORDATORIO_RESERVA'" class="fas fa-clock"></i>
                                        <i th:case="'PROMOCION'" class="fas fa-gift"></i>
                                        <i th:case="*" class="fas fa-info"></i>
                                    </i>


                                    <!-- Contenido -->
                                    <div class="notification-content">
                                        <div class="notification-title" th:text="${notificacion.titulo}"></div>
                                        <div class="notification-message" th:text="${notificacion.mensaje}"></div>
                                        <div class="notification-time mt-2">
                                            <i class="fas fa-clock me-1"></i>
                                            <span th:text="${notificacion.fechaFormateada}"></span>
                                            <span th:if="${notificacion.leida}" class="ms-2">
                                                <i class="fas fa-check text-success me-1"></i>Leída
                                            </span>
                                        </div>
                                    </div>
                                    
                                    <!-- Acciones -->
                                    <div class="ms-3">
                                        <button th:if="${!notificacion.leida}" 
                                                class="btn btn-sm btn-outline-primary"
                                                th:onclick="'marcarLeida(' + ${notificacion.id} + ')'">
                                            <i class="fas fa-check"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function marcarLeida(id) {
            fetch(`/vecino/notificaciones/marcar-leida/${id}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Actualizar la interfaz
                    const notificationItem = document.querySelector(`[data-id="${id}"]`);
                    notificationItem.classList.remove('unread');
                    notificationItem.classList.add('read');
                    
                    // Remover botón de marcar como leída
                    const button = notificationItem.querySelector('button');
                    if (button) {
                        button.remove();
                    }
                    
                    // Agregar indicador de leída
                    const timeDiv = notificationItem.querySelector('.notification-time');
                    if (!timeDiv.querySelector('.text-success')) {
                        timeDiv.insertAdjacentHTML('beforeend', 
                            '<span class="ms-2"><i class="fas fa-check text-success me-1"></i>Leída</span>');
                    }
                    
                    // Actualizar contador
                    actualizarContador();
                } else {
                    alert('Error al marcar notificación: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error de conexión');
            });
        }

        function marcarTodasLeidas() {
            if (!confirm('¿Marcar todas las notificaciones como leídas?')) {
                return;
            }

            fetch('/vecino/notificaciones/marcar-todas-leidas', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Recargar página para actualizar todo
                    location.reload();
                } else {
                    alert('Error al marcar notificaciones: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error de conexión');
            });
        }

        function actualizarContador() {
            const noLeidas = document.querySelectorAll('.notification-item.unread').length;
            const badge = document.querySelector('.navbar .badge');
            const contadorNoLeidas = document.querySelector('.bg-warning h4');
            const contadorLeidas = document.querySelector('.bg-success h4');
            
            if (badge) {
                if (noLeidas > 0) {
                    badge.textContent = noLeidas;
                } else {
                    badge.remove();
                }
            }
            
            if (contadorNoLeidas) {
                contadorNoLeidas.textContent = noLeidas;
            }
            
            if (contadorLeidas) {
                const totalNotificaciones = document.querySelectorAll('.notification-item').length;
                contadorLeidas.textContent = totalNotificaciones - noLeidas;
            }
        }
    </script>
</body>
</html>
